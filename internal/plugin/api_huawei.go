package plugin

import (
	"errors"
	"fmt"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/mapping"
	"codeup.aliyun.com/xhey/server/adx/internal/protocol"
	"codeup.aliyun.com/xhey/server/adx/internal/track"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/bytedance/sonic"
	"github.com/duke-git/lancet/v2/pointer"
	"github.com/spf13/cast"
)

var (
	signKey   = "8e88c7e55b7dc22ecd76b0d0224bc4806a8449af2500d85ce757fb8ed5e691c8"
	publishID = "1315275462271910656"
	keyID     = "1"
)

type huaWeiResp struct {
	RetCode int    `json:"retcode"`
	Reason  string `json:"reason"`
	MultiAd []hAd  `json:"multiad"`
}

type hAd struct {
	SlotID    string    `json:"slotid"`
	RetCode30 int       `json:"retcode30"`
	Content   []content `json:"content"`
}

type content struct {
	ContentID       string     `json:"contentid"`
	InteractionType int        `json:"interactiontype"`
	CreativeType    int        `json:"creativetype"`
	NURL            string     `json:"nurl"`
	MetaData        metaData   `json:"metaData"`
	Monitor         []hmonitor `json:"monitor"`
	Price           float64    `json:"price"`
}

type metaData struct {
	Title      string      `json:"title"`
	ImageInfo  []imageInfo `json:"imageInfo"`
	ClickURL   string      `json:"clickUrl"`
	Intent     string      `json:"intent"`
	ApkInfo    apkInfo     `json:"apkInfo"`
	PrivacyURL string      `json:"privacyUrl"`
}

type imageInfo struct {
	URL string `json:"url"`
}

type hmonitor struct {
	EventType string   `json:"eventType"`
	URL       []string `json:"url"`
}

type apkInfo struct {
	URL           string       `json:"url"`
	VersionCode   string       `json:"versionCode"`
	FileSize      float64      `json:"fileSize"`
	PackageName   string       `json:"packageName"`
	AppName       string       `json:"appName"`
	Permissions   []permission `json:"permissions"`
	VersionName   string       `json:"versionName"`
	AppDesc       string       `json:"appDesc"`
	DeveloperName string       `json:"developerName"`
	PermissionURL string       `json:"permissionUrl"`
	DetailURL     string       `json:"detailUrl"`
}

type permission struct {
	PermissionLabel string `json:"permissionLabel"`
}

type huaWeiReq struct {
	Version           string   `json:"version"`
	MultiSlot         []slot   `json:"multislot"`
	App               hApp     `json:"app"`
	Device            hDevice  `json:"device"`
	Network           hNetwork `json:"network"`
	ClientAdRequestID string   `json:"clientAdRequestId"`
}

type slot struct {
	SlotID string `json:"slotid"`
	AdType int    `json:"adtype"`
	Test   int    `json:"test"`
}

type hApp struct {
	Version string `json:"version"`
	Name    string `json:"name"`
	PkgName string `json:"pkgname"`
}

type hDevice struct {
	Type              int     `json:"type"`
	Useragent         string  `json:"useragent"`
	Os                string  `json:"os"`
	Version           string  `json:"version"`
	Maker             string  `json:"maker"`
	Model             string  `json:"model"`
	Width             int32   `json:"width"`
	Height            int32   `json:"height"`
	Language          string  `json:"language"`
	Imei              string  `json:"imei"`
	Oaid              string  `json:"oaid"`
	AndroidID         string  `json:"androidid"`
	IsTrackingEnabled *string `json:"isTrackingEnabled,omitempty"`
	LocaleCountry     string  `json:"localeCountry"`
	BelongCountry     string  `json:"belongCountry"`
	VerCodeOfHms      string  `json:"verCodeOfHms"`
	ClientTime        string  `json:"clientTime"`
	VerCodeOfAG       string  `json:"verCodeOfAG"`
	IP                string  `json:"ip"`
}

type hNetwork struct {
	Type    int `json:"type"`
	Carrier int `json:"carrier"`
}

type huaWeiPlugin struct {
	opts Options
}

func (h *huaWeiPlugin) String() string {
	return fmt.Sprintf("opts: %+v", h.opts)
}

func (h *huaWeiPlugin) GetOptions() Options {
	return h.opts
}

func (h *huaWeiPlugin) Run(ctx *Context) {
	ctx.Err = hystrixRun(ctx, h.opts)
}

// 本地配置 1:开屏、2:信息流、3:插屏、4:激励视频、5:banner
// rtb接口 1: Banner，2: 插屏，3: 开屏，4: 信息流，5: 普通视频，6: 激励视频
var AdConf2RtbRespMp = map[int]int{1: 3, 2: 4, 3: 2, 4: 6, 5: 1}

func NewHuaWeiPlugin(conf dto.DSPConf) Plugin {
	plg := new(huaWeiPlugin)
	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(huaWeiMsgConvertBefore),
		WrapFunc(huaWeiCallAPI),
		WrapFunc(huaWeiMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func NewHuaWeiPluginV2(conf dto.AdTADMixConfV2) Plugin {
	plg := new(huaWeiPlugin)
	options := NewOptions(
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(huaWeiMsgConvertBefore),
		WrapFunc(huaWeiCallAPI),
		WrapFunc(huaWeiMsgConvertAfter),
	)
	ReplaceV2PluginArgs(&options, &conf)
	plg.opts = options
	return plg
}

func huaWeiMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq := ctx.GenericContext.Req
		if strings.ToLower(srcReq.Device.OS) == "ios" {
			return
		}
		// --- 非华为机型
		flag := false
		if !strings.Contains(srcReq.Device.Make, "HUAWEI") && !strings.Contains(srcReq.Device.Make, "HONOR") {
			flag = true
		}
		// --- 非华为机型
		var multiSlot []slot
		var test int
		if os.Getenv("env") == "test" || os.Getenv("env") == "local" {
			test = 1
		} else {
			test = 0
		}
		_app := hApp{
			Version: srcReq.App.AppVersion,
			Name:    srcReq.App.AppName,
			PkgName: srcReq.App.Bundle,
		}
		var _device hDevice
		var net hNetwork
		var pid string
		if strings.ToLower(srcReq.Device.OS) == "ios" {
			pid = ctx.Opt.IosPid
		} else {
			pid = ctx.Opt.AndroidPid
		}
		var isTrackingEnabled *string
		if srcReq.Device.OAID != "" {
			isTrackingEnabled = pointer.Of("1")
		}
		now := time.Now().Format("2006-01-02 15:04:05.000") + "+0800"
		var model string
		model, err := url.QueryUnescape(srcReq.Device.Model)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "[huaWeiMsgConvertBefore] device model err, model is %v", srcReq.Device.Model)
			model = srcReq.Device.Model
		}
		ua := srcReq.Device.UA
		tmpUa, err := url.QueryUnescape(srcReq.Device.UA)
		if err == nil {
			ua = tmpUa
		}
		_device = hDevice{
			Type:              4,
			Useragent:         ua,
			Os:                srcReq.Device.OS,
			Version:           srcReq.Device.OSVersion,
			Maker:             strings.ToUpper(srcReq.Device.Make),
			Model:             model,
			Width:             srcReq.Device.ScreenWidth,
			Height:            srcReq.Device.ScreenHeight,
			Language:          "zh",
			Imei:              srcReq.Device.IMEI,
			Oaid:              srcReq.Device.OAID,
			AndroidID:         srcReq.Device.AndroidID,
			IsTrackingEnabled: isTrackingEnabled,
			LocaleCountry:     "CN",
			BelongCountry:     "CN",
			VerCodeOfHms:      srcReq.Device.VerCodeOfHms,
			ClientTime:        now,
			VerCodeOfAG:       srcReq.Device.VerCodeOfAG,
			IP:                srcReq.Device.IP,
		}
		net = hNetwork{
			Type:    mapping.HNetMap[srcReq.Device.ConnectionType],
			Carrier: mapping.HCarrier[srcReq.Device.Carrier],
		}
		hwADType := 1
		if ctx.Opt.DspConfV2 != nil { // 默认使用开屏
			if tmpAdType, ok := mapping.HwADTypeMp[ctx.Opt.DspConfV2.AdType]; ok {
				hwADType = tmpAdType
			}
		}
		multiSlot = append(multiSlot, slot{
			SlotID: pid,
			AdType: hwADType,
			Test:   test,
		})

		notHWSlotIDMp := map[int]string{
			int(dto.AdContainerTypeStart):  "l7witw2bbl",
			int(dto.AdContainerTypeInsert): "l7witw2bbl", // fixme 待补充
		}
		// --- 非华为机型
		if flag && len(multiSlot) > 0 && ctx.Opt.DspConfV2 != nil {
			multiSlot[0].SlotID = notHWSlotIDMp[ctx.Opt.DspConfV2.AdType]
			multiSlot[0].AdType = mapping.HwADTypeMp[ctx.Opt.DspConfV2.AdType]
		} else if flag && len(multiSlot) > 0 {
			multiSlot[0].SlotID = "l7witw2bbl"
			multiSlot[0].AdType = 1
		}
		// --- 非华为机型
		dstReq := &huaWeiReq{
			Version:           "3.4",
			MultiSlot:         multiSlot,
			App:               _app,
			Device:            _device,
			Network:           net,
			ClientAdRequestID: srcReq.ID,
		}
		data, err := sonic.Marshal(dstReq)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "[huaWeiMsgConvertBefore] marshal err, err: %v", err)
			return
		}
		ctx.Data = data
		fn(ctx)
	}
}

func huaWeiCallAPI(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		data, ok := ctx.Data.([]byte)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "[huaWeiCallApi] req type err, req is %v", ctx.Data)
			return
		}
		sign := "Digest " + utils.GetAdSign(publishID, keyID, signKey)
		var header = map[string]string{
			"Authorization": sign,
		}
		_url := ctx.Opt.RtbURL
		//logger.InfoCtx(ctx.Ctx, "huaWeiCallApi req: %v", string(data))
		respData, httpCode, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      _url,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		ctx.HTTPCode = httpCode
		if err != nil {
			ctx.Err = errors.Join(errs.ErrCallAPI, err)
			return
		}
		//logger.InfoCtx(ctx.Ctx, "huaWeiCallApi resp: %v", string(respData))
		resp := &huaWeiResp{}
		if len(respData) != 0 {
			err = sonic.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "[huaWeiCallApi] unmarshal resp err, err: %v", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func getClickURLQuery(clickTrack track.ClickTrack, queryParams url.Values) track.ClickTrack {
	clickTrack.SldVal = queryParams.Get("sld")
	clickTrack.DownXVal = queryParams.Get("downx")
	clickTrack.UpXVal = queryParams.Get("upx")
	clickTrack.XmaxaccVal = queryParams.Get("xmaxacc")
	clickTrack.TurnxVal = queryParams.Get("turnx")
	clickTrack.ClickUTimeVal = queryParams.Get("clickUTime")
	clickTrack.ClickDTimeVal = queryParams.Get("clickDTime")
	return clickTrack
}

func trackClickURL(ctx *Context, srcReq *protocol.GenericRequest, _url string, phase int) {
	source := track.ClickTrackSourceSelfAdx
	if srcReq.InnerData.Source == dto.RequestSourceTopon {
		source = track.ClickTrackSourceTopon
	} else if srcReq.InnerData.Source == dto.RequestSourceSelfRender {
		source = track.ClickTrackSourceSelfAdxV2
	}
	var tagID, adPosID string
	if len(srcReq.Imp) > 0 {
		adPosID = srcReq.Imp[0].TagID
	}
	if strings.ToLower(srcReq.Device.OS) == "ios" {
		tagID = ctx.Opt.IosPid
	} else {
		tagID = ctx.Opt.AndroidPid
	}
	clickTrack := track.ClickTrack{
		RequestID:        srcReq.ID,
		Source:           source,
		Phase:            phase,
		TagID:            tagID,
		AdPosID:          adPosID,
		SourceAlias:      srcReq.InnerData.Source,
		DspName:          ctx.Opt.APIName,
		VersionCode:      cast.ToString(srcReq.InnerData.VersionCode),
		VersionCodeQuote: srcReq.InnerData.VersionCodeQuote,
		ClickURL:         _url,
		Oaid:             srcReq.Device.OAID,
		CurTime:          cast.ToString(time.Now().Unix()),
	}
	if srcReq.InnerData.Source == dto.RequestSourceSelfRender {
		clickTrack.DspName = ctx.Opt.DspConfV2.DspName
	}
	parsedURL, err := url.Parse(_url)
	if err != nil {
		logger.ErrorCtx(ctx.Ctx, "[trackClickUrl] err parsing before replace  URL:", err)
	} else if parsedURL != nil {
		queryParams := parsedURL.Query()
		clickTrack = getClickURLQuery(clickTrack, queryParams)
	}
	re := regexp.MustCompile(`[&?]pfsa=[^&]*(&|$)`)
	clickTrack.ClickURL = re.ReplaceAllString(_url, "&")

	track.SensorClickTrack(ctx.Ctx, srcReq.InnerData.UserID, srcReq.InnerData.DeviceID, clickTrack)
}

func huaWeiMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*huaWeiResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "[huaWeiMsgConvertAfter] resp type err, resp is %v", ctx.Resp)
			return
		}
		srcReq := ctx.GenericContext.Req
		srcResp := ctx.GenericContext.Resp

		if resp.RetCode != 200 && resp.RetCode != 204 {
			logger.ErrorCtx(ctx.Ctx, "[huaWeiMsgConvertAfter] resp code not 200, code is %v, reason is %v", resp.RetCode, resp.Reason)
			return
		}

		var bids []protocol.BidInfo
		if len(resp.MultiAd) != 0 {
			if resp.MultiAd[0].RetCode30 != 200 && resp.MultiAd[0].RetCode30 != 204 {
				logger.ErrorCtx(ctx.Ctx, "[huaWeiMsgConvertAfter] retCode30 is not 200, code is %v", resp.MultiAd[0].RetCode30)
				return
			}
			if len(resp.MultiAd[0].Content) != 0 {
				var mediaStyle int32
				var clickURL, dpURL string
				var err error
				switch {
				case resp.MultiAd[0].Content[0].InteractionType == 1:
				case resp.MultiAd[0].Content[0].InteractionType == 2:
					dpURL = resp.MultiAd[0].Content[0].MetaData.Intent
				case resp.MultiAd[0].Content[0].InteractionType == 3:
					dpURL, err = url.QueryUnescape(resp.MultiAd[0].Content[0].MetaData.Intent)
					if err != nil {
						logger.ErrorCtx(ctx.Ctx, "[huaWeiMsgConvertAfter] QueryUnescape err: %v, intent is %v", err, resp.MultiAd[0].Content[0].MetaData.Intent)
						dpURL = resp.MultiAd[0].Content[0].MetaData.Intent
					}
				case resp.MultiAd[0].Content[0].InteractionType == 5:
					dpURL = resp.MultiAd[0].Content[0].MetaData.Intent
				case resp.MultiAd[0].Content[0].InteractionType == 7:
					if resp.MultiAd[0].Content[0].MetaData.Intent != "" {
						dpURL = resp.MultiAd[0].Content[0].MetaData.Intent
					}
				}
				if len(dpURL) != 0 && !strings.HasPrefix(dpURL, "hwpps://") {
					decodedURL, err := url.QueryUnescape(dpURL)
					if err != nil {
						logger.ErrorCtx(ctx.Ctx, "huaWeiMsgConvertAfter QueryUnescape dpURL err:%v, dpURL is %v", err, dpURL)
					} else {
						dpURL = decodedURL
					}
				}
				mediaStyle = 1
				clickURL = resp.MultiAd[0].Content[0].MetaData.ClickURL
				title, _ := url.QueryUnescape(resp.MultiAd[0].Content[0].MetaData.Title)
				var _img []string
				for _, ig := range resp.MultiAd[0].Content[0].MetaData.ImageInfo {
					_img = append(_img, ig.URL)
				}
				var MonitorURLList []protocol.MonitorURL
				var exposeUrls, clickMonitorUrls, startURL, endURL, dpSuccessURL, installURL, appOpenURL []string
				for _, m := range resp.MultiAd[0].Content[0].Monitor {
					switch {
					case m.EventType == "imp":
						switch {
						case utils.NeedReplaceVersionV1(ctx.Ctx, srcReq.InnerData.VersionCodeQuote, srcReq.InnerData.Source, srcReq.Device.OS):
							for _, exposeURL := range m.URL {
								macroArgs := map[string]string{
									"__HW_SHOW_TIME__":      "{__SHOW_T__}",
									"__HW_MAX_SHOW_RATIO__": "{__M_SHOW_RATIO__}",
									"__HW_SLOT_SCREEN_X__":  "{__S_SCREEN_X__}",
									"__HW_SLOT_SCREEN_Y__":  "{__S_SCREEN_Y__}",
									"__HW_W__":              "{__WIDTH__}",
									"__HW_H__":              "{__HEIGHT__}",
									"__HW_EVENT_TIME__":     "{__TS_MSEC__}",
									"__HW_DENSITY__":        "{__DENSITY__}",
								}
								for source, to := range macroArgs {
									exposeURL = strings.ReplaceAll(exposeURL, source, to)
								}
								if len(srcReq.Imp) > 0 {
									exposeURL = strings.ReplaceAll(exposeURL, "__REQ_WIDTH__", cast.ToString(srcReq.Imp[0].AdSlotSize.Width))
									exposeURL = strings.ReplaceAll(exposeURL, "__REQ_HEIGHT__", cast.ToString(srcReq.Imp[0].AdSlotSize.Height))
								}
								exposeUrls = append(exposeUrls, exposeURL)
							}
						case srcReq.InnerData.Source == dto.RequestSourceSelfRender && utils.CompareVersion(ctx.Ctx, cast.ToString(srcReq.InnerData.VersionCode), "30016000") != -1:
							macroArgs := map[string]string{
								"__HW_SHOW_TIME__":      "__SHOW_T_MS__",
								"__HW_MAX_SHOW_RATIO__": "__MAX_SHOW_RATIO__",
								"__HW_SLOT_SCREEN_X__":  "__RELATIVE_SCREEN_X__",
								"__HW_SLOT_SCREEN_Y__":  "__RELATIVE_SCREEN_Y__",
								"__HW_W__":              "__WIDTH_PX__",
								"__HW_H__":              "__HEIGHT_PX__",
								"__HW_EVENT_TIME__":     "__EVENT_TS_MSEC__",
							}
							for _, exposeURL := range m.URL {
								for source, to := range macroArgs {
									exposeURL = strings.ReplaceAll(exposeURL, source, to)
								}
								exposeUrls = append(exposeUrls, exposeURL)
							}
						default:
							exposeUrls = append(exposeUrls, m.URL...)
						}
					case m.EventType == "click":
						for _, _url := range m.URL {
							trackClickURL(ctx, srcReq, _url, track.PhaseDspBeforeReplace)
							switch {
							case utils.NeedReplaceVersionV1(ctx.Ctx, srcReq.InnerData.VersionCodeQuote, srcReq.InnerData.Source, srcReq.Device.OS):
								macroArgs := map[string]string{
									"__HW_W__":         "{__WIDTH__}",
									"__HW_H__":         "{__HEIGHT__}",
									"__HW_SLD__":       "{__C_SLD__}",
									"__HW_DOWN_X__":    "{__RE_C_DOWN_X__}",
									"__HW_DOWN_Y__":    "{__RE_C_DOWN_Y__}",
									"__HW_UP_X__":      "{__RE_C_UP_X__}",
									"__HW_UP_Y__":      "{__RE_C_UP_Y__}",
									"__HW_X_MAX_ACC__": "{__M_X_ACC__}",
									"__HW_Y_MAX_ACC__": "{__M_Y_ACC__}",
									"__HW_Z_MAX_ACC__": "{__M_Z_ACC__}",
									"__HW_TURN_X__":    "{__TURN_X__}",
									"__HW_TURN_Y__":    "{__TURN_Y__}",
									"__HW_TURN_Z__":    "{__TURN_Z__}",
									"__HW_TURN_TIME__": "{__TURN_T__}",
									"__HW_UP_TIME__":   "{__END_TS_MSEC__}",
									"__HW_DOWN_TIME__": "{__TS_MSEC__}",
									"__HW_DENSITY__":   "{__DENSITY__}",
								}
								for source, to := range macroArgs {
									_url = strings.ReplaceAll(_url, source, to)
								}
								if len(srcReq.Imp) > 0 {
									_url = strings.ReplaceAll(_url, "__REQ_WIDTH__", cast.ToString(srcReq.Imp[0].AdSlotSize.Width))
									_url = strings.ReplaceAll(_url, "__REQ_HEIGHT__", cast.ToString(srcReq.Imp[0].AdSlotSize.Height))
								}
							case srcReq.InnerData.Source == dto.RequestSourceSelfRender:
								macroArgs := map[string]string{
									"__HW_W__":         "__WIDTH_PX__",
									"__HW_H__":         "__HEIGHT_PX__",
									"__HW_SLD__":       "__C_SLD__",
									"__HW_DENSITY__":   "__DENSITY_FLOAT__",
									"__HW_DOWN_X__":    "__RELATIVE_CLICK_DOWN_X__",
									"__HW_DOWN_Y__":    "__RELATIVE_CLICK_DOWN_Y__",
									"__HW_UP_X__":      "__RELATIVE_CLICK_UP_X__",
									"__HW_UP_Y__":      "__RELATIVE_CLICK_UP_Y__",
									"__HW_X_MAX_ACC__": "__SHAKE_X_MAX_ACC__",
									"__HW_Y_MAX_ACC__": "__SHAKE_Y_MAX_ACC__",
									"__HW_Z_MAX_ACC__": "__SHAKE_Z_MAX_ACC__",
									"__HW_TURN_X__":    "__TURN_X__",
									"__HW_TURN_Y__":    "__TURN_Y__",
									"__HW_TURN_Z__":    "__TURN_Z__",
									"__HW_TURN_TIME__": "__TURN_T__",
									"__HW_UP_TIME__":   "__END_EVENT_TS_MSEC__",
									"__HW_DOWN_TIME__": "__EVENT_TS_MSEC__",
								}
								for source, to := range macroArgs {
									_url = strings.ReplaceAll(_url, source, to)
								}
							default:
								_url = strings.ReplaceAll(_url, "__HW_W__", "__WIDTH__")
								_url = strings.ReplaceAll(_url, "__HW_H__", "__HEIGHT__")
								_url = strings.ReplaceAll(_url, "__HW_DOWN_X__", "__DOWN_X__")
								_url = strings.ReplaceAll(_url, "__HW_DOWN_Y__", "__DOWN_Y__")
								_url = strings.ReplaceAll(_url, "__HW_UP_X__", "__UP_X__")
								_url = strings.ReplaceAll(_url, "__HW_UP_Y__", "__UP_Y__")
							}
							trackClickURL(ctx, srcReq, _url, track.PhaseDspAfterReplace)
							clickMonitorUrls = append(clickMonitorUrls, _url)
						}
					case m.EventType == "downloadstart":
						startURL = append(startURL, m.URL...)
					case m.EventType == "download":
						endURL = append(endURL, m.URL...)
					case m.EventType == "intentSuccess":
						dpSuccessURL = append(dpSuccessURL, m.URL...)
					case m.EventType == "install":
						installURL = append(installURL, m.URL...)
					case m.EventType == "appOpen":
						appOpenURL = append(appOpenURL, m.URL...)
					}
				}
				MonitorURLList = append(MonitorURLList, protocol.MonitorURL{
					EventType: "dpStartINTENTrack",
					URLList:   dpSuccessURL,
				})
				MonitorURLList = append(MonitorURLList, protocol.MonitorURL{
					EventType: "dpStartPackageNameTrack",
					URLList:   appOpenURL,
				})
				adType := AdTypeAppStart
				if ctx.Opt.DspConfV2 != nil {
					adType = AdConf2RtbRespMp[ctx.Opt.DspConfV2.AdType]
				}
				bids = append(bids, protocol.BidInfo{
					ImpID:   srcReq.Imp[0].ID,
					AdType:  int32(adType),
					AdStyle: AdStylePic,
					Item: protocol.AdItem{
						Title:                  title,
						MediaStyle:             mediaStyle,
						DownloadURL:            "",
						ClickURL:               clickURL,
						DplURL:                 dpURL,
						ImgList:                _img,
						ExposalURLList:         exposeUrls,
						ClickMonitorURLList:    clickMonitorUrls,
						DownloadTrackURLList:   startURL,
						DownloadedTrackURLList: endURL,
						InstalledTrackURLList:  installURL,
						DpSuccessTrackURLList:  dpSuccessURL,
						ActionTrackURLList:     appOpenURL,
						PackageName:            resp.MultiAd[0].Content[0].MetaData.ApkInfo.PackageName,
						MonitorURLList:         MonitorURLList,
					},
					NURL:    resp.MultiAd[0].Content[0].NURL,
					Price:   resp.MultiAd[0].Content[0].Price * 100,
					APIName: ctx.Opt.APIName,
				})
			}
		}
		var seatBids []protocol.SeatBid
		seatBids = append(seatBids, protocol.SeatBid{BidList: bids})
		srcResp.ID = srcReq.ID
		srcResp.SeatBid = seatBids
		fn(ctx)
	}
}
