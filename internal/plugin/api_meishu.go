package plugin

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/mapping"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	proto "codeup.aliyun.com/xhey/server/adx/protobuf/adx"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/bytedance/sonic"
)

type msResp struct {
	ReqID            string   `json:"req_id"`
	TargetType       int      `json:"target_type"`
	DUrl             []string `json:"dUrl"`
	DeepLink         string   `json:"deep_link"`
	SrcUrls          []string `json:"srcUrls"`
	Icon             string   `json:"icon"`
	AppName          string   `json:"app_name"`
	AppVer           string   `json:"app_ver"`
	AppSize          string   `json:"app_size"`
	AppPermissionUrl string   `json:"app_permission_url"`
	PrivacyAgreement string   `json:"privacy_agreement"`
	Developer        string   `json:"developer"`
	MonitorUrl       []string `json:"monitorUrl"`
	ClickUrl         []string `json:"clickUrl"`
	Ecpm             int      `json:"ecpm"`
	DnStart          []string `json:"dn_start"`
	DnSucc           []string `json:"dn_succ"`
	DnInstSucc       []string `json:"dn_inst_succ"`
	DnActive         []string `json:"dn_active"`
	DpSucc           []string `json:"dp_succ"`
	WxUsername       *string  `json:"wx_username"`
	WxPath           *string  `json:"wx_path"`
	PackageName      string   `json:"package_name"`
	AppIntro         string   `json:"app_intro"`
	AppIntroUrl      string   `json:"app_intro_url"`
}

type meiShuPlugin struct {
	opts Options
}

func (m *meiShuPlugin) String() string {
	return fmt.Sprintf("opts: %+v", m.opts)
}

func (m *meiShuPlugin) GetOptions() Options {
	return m.opts
}

func (m *meiShuPlugin) Run(ctx *Context) {
	ctx.Err = hystrixRun(ctx, m.opts)
}

func NewMeiShuPlugin(conf dto.DSPConf) Plugin {
	plg := new(meiShuPlugin)

	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(meiShuMsgConvertBefore),
		WrapFunc(meiShuCallApi),
		WrapFunc(meiShuMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func meiShuMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil || len(srcReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuMsgConvertBefore src req type err, req is %v", ctx.SrcReq)
			return
		}
		data := url.Values{}

		data.Add("is_mobile", "1")
		data.Add("language", "zh")
		if srcReq.App != nil {
			data.Add("app_package", srcReq.App.Bundle)
			data.Add("app_name", srcReq.App.AppName)
			data.Add("app_ver", srcReq.App.AppVersion)
		}
		var appId, pid string
		if srcReq.Device != nil {
			var os string
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				os = "iOS"
				appId = ctx.Opt.IosAppID
				pid = ctx.Opt.IosPid
			} else {
				os = "Android"
				appId = ctx.Opt.AndroidAppID
				pid = ctx.Opt.AndroidPid
			}

			if os == "iOS" {
				data.Add("device_adid", srcReq.Device.Idfa)
				data.Add("device_caid", srcReq.Device.CaidVersion+srcReq.Device.Caid)
				data.Add("device_paid", srcReq.Device.Paid)
			} else {
				data.Add("device_imei", srcReq.Device.Imei)
				data.Add("device_oaid", srcReq.Device.Oaid)
				data.Add("device_adid", srcReq.Device.AndroidId)
				data.Add("device_hmscore", srcReq.Device.VerCodeOfHms)
			}

			if srcReq.Device.Geo != nil {
				data.Add("country", srcReq.Device.Geo.Country)
				data.Add("device_geo_lat", fmt.Sprintf("%.6f", srcReq.Device.Geo.Lat))
				data.Add("device_geo_lon", fmt.Sprintf("%.6f", srcReq.Device.Geo.Lon))
			}
			data.Add("device_ppi", strconv.Itoa(int(srcReq.Device.Ppi)))
			var mac string
			if len(srcReq.Device.Mac) == 12 {
				mac = srcReq.Device.Mac
				mac = fmt.Sprintf("%s:%s:%s:%s:%s:%s", mac[0:2], mac[2:4], mac[4:6], mac[6:8], mac[8:10], mac[10:12])

			}
			data.Add("device_mac", mac)
			data.Add("device_type_os", srcReq.Device.Osv)
			data.Add("device_type", "0")
			data.Add("device_brand", srcReq.Device.Brand)
			data.Add("device_model", srcReq.Device.Model)
			data.Add("device_width", strconv.Itoa(int(srcReq.Device.ScreenWidth)))
			data.Add("device_height", strconv.Itoa(int(srcReq.Device.ScreenHeight)))
			data.Add("device_imsi", mapping.MCarrierMap[srcReq.Device.Carrier])
			data.Add("device_network", mapping.MNetMap[srcReq.Device.ConnectionType])
			data.Add("device_os", os)
			data.Add("device_ip", srcReq.Device.Ip)
			data.Add("device_ua", srcReq.Device.Ua)
			data.Add("device_orientation", mapping.MOriMap[srcReq.Device.Orientation])
			data.Add("device_rom_version", srcReq.Device.RomVersion)
		}
		if len(srcReq.InstalledApp) != 0 {
			data.Add("installed_app", strings.Join(srcReq.InstalledApp, ","))
		}
		data.Add("app_id", appId)
		data.Add("pid", pid)
		ctx.Data = []byte(data.Encode())
		fn(ctx)
	}
}

func meiShuCallApi(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		data, ok := ctx.Data.([]byte)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuCallApi req type err, req is %v", ctx.Data)
			return
		}
		var header = map[string]string{
			"Accept-Encoding": "gzip",
			"Content-Type":    "application/x-www-form-urlencoded",
		}
		respData, _, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.URLEncode,
			Data:     data,
			URL:      ctx.Opt.RtbURL,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		if err != nil {
			ctx.Err = errors.Join(errs.ErrCallAPI, err)
			return
		}
		resp := &msResp{}
		if len(respData) != 0 {
			err = sonic.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuCallApi unmarshal resp err, err: %v", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func meiShuMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*msResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuMsgConvertAfter resp type err, resp is %v", ctx.Resp)
			return
		}
		srcResp, ok := ctx.SrcResp.(*proto.BidResponse)
		if !ok || srcResp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuMsgConvertAfter src resp type err, resp is %v", ctx.SrcResp)
			return
		}
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuMsgConvertAfter src req type err, req is %v", ctx.SrcReq)
			return
		}
		var bids []*proto.BidResponse_Bid
		var durl string
		if len(resp.DUrl) != 0 {
			durl = strings.Replace(resp.DUrl[0], "__MS_EVENT_MSEC__", "__TS__", -1)
		}
		// 宏替换
		var clickMonitorUrls []string
		for _, u := range resp.ClickUrl {
			u = strings.Replace(u, "__MS_EVENT_MSEC__", "__TS__", -1)
			clickMonitorUrls = append(clickMonitorUrls, u)
		}
		var mediaStyle int32
		var downloadUrl string
		if resp.TargetType == 0 {
			// 网页跳转
			mediaStyle = 1
		} else if resp.TargetType == 1 {
			mediaStyle = 2
			downloadUrl = durl
		} else if resp.WxUsername != nil && *resp.WxUsername != "" {
			mediaStyle = 4
		}
		var packSizeStr string
		if resp.AppSize != "" {
			packSize, err := strconv.ParseFloat(resp.AppSize, 64)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "meiShuMsgConvertAfter AppSize convert float err, appsize is %v", resp.AppSize)
				packSizeStr = ""
			} else {
				packSize /= 1024
				packSizeStr = fmt.Sprintf("%.6f", packSize)
			}
		} else {
			packSizeStr = ""
		}
		var progID, progPath string
		if resp.WxUsername != nil {
			progID = *resp.WxUsername
		}
		if resp.WxPath != nil {
			progPath = *resp.WxPath
		}
		bids = append(bids, &proto.BidResponse_Bid{
			Impid:   srcReq.Imp[0].Id,
			AdType:  AdTypeAppStart,
			AdStyle: AdStylePic,
			Item: &proto.BidResponse_Item{
				Icon:             resp.Icon,
				ClickUrl:         durl,
				Imgs:             resp.SrcUrls,
				ExposalUrls:      resp.MonitorUrl,
				ClickMonitorUrls: clickMonitorUrls,
				DownloadAppInfo: &proto.BidResponse_DownloadAppInfo{
					AppName:    resp.AppName,
					Developer:  resp.Developer,
					Version:    resp.AppVer,
					PacketSize: packSizeStr,
					Privacy:    resp.PrivacyAgreement,
					Permission: resp.AppPermissionUrl,
					Desc:       resp.AppIntro,
					DescURL:    resp.AppIntroUrl,
				},
				MediaStyle:                  mediaStyle,
				DownloadUrl:                 downloadUrl,
				DplUrl:                      resp.DeepLink,
				DownloadTrackUrls:           resp.DnStart,
				DownloadedTrackUrls:         resp.DnSucc,
				DpSuccessTrackUrls:          resp.DpSucc,
				InstalledTrackUrls:          resp.DnInstSucc,
				ActionTrackUrls:             resp.DnActive,
				MiniProgramId:               progID,
				MiniProgramPath:             progPath,
				MiniProgramType:             1,
				MiniProgramSuccessTrackUrls: nil,
				PackageName:                 resp.PackageName,
			},
			Price:   float64(resp.Ecpm),
			Nurl:    "",
			ApiName: ctx.Opt.APIName,
		})
		var seatbids []*proto.BidResponse_SeatBid
		seatbids = append(seatbids, &proto.BidResponse_SeatBid{Bid: bids})
		srcResp.Id = srcReq.Id
		srcResp.Bidid = resp.ReqID
		srcResp.Seatbid = seatbids
		ctx.SrcResp = srcResp
		fn(ctx)
	}
}
