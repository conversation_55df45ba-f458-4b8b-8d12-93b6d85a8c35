package plugin

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	proto "codeup.aliyun.com/xhey/server/adx/protobuf/adx"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/afex/hystrix-go/hystrix"
	"github.com/duke-git/lancet/v2/pointer"
)

// DEPRECATED 已弃用的三个私有协议API插件，zhangyu、youhe、fenglan

var (
	ZyCallErr = errors.New("call zyhd api err")
)

type zyResp struct {
	Code   int     `json:"code"`
	RespID string  `json:"resp_id"`
	Data   bidData `json:"data"`
}

type bidData struct {
	Ads []zAd `json:"ads"`
}

type zAd struct {
	AdType int    `json:"ad_type"`
	Price  int    `json:"price"`
	Native native `json:"native"`
}

type native struct {
	Assets              []asset       `json:"assets"`
	Link                link          `json:"link"`
	Imptrackers         []string      `json:"imptrackers"`
	WxMiniUser          *string       `json:"wx_mini_user"`
	WxMiniPath          *string       `json:"wx_mini_path"`
	DownloadTrackUrls   []string      `json:"download_track_urls"`
	DownloadedTrackUrls []string      `json:"downloaded_track_urls"`
	InstalledTrackUrls  []string      `json:"installed_track_urls"`
	OpenTrackUrls       []string      `json:"open_track_urls"`
	DeepLinkTrackers    []deeplinkImp `json:"deeplinktrackers"`
}

type deeplinkImp struct {
	Event int      `json:"event"`
	Imp   []string `json:"imp"`
}

type link struct {
	Url           string   `json:"url"`
	Clicktrackers []string `json:"clicktrackers"`
	Fallback      string   `json:"fallback"`
}

type asset struct {
	Img zImg `json:"img"`
}

type zImg struct {
	Url string `json:"url"`
}

type zyReq struct {
	Version  string  `json:"version"`
	Id       string  `json:"id"`
	Imp      []zImp  `json:"imp"`
	SlotId   string  `json:"slot_id"`
	Bidfloor float64 `json:"bidfloor"`
	Ip       string  `json:"ip"`
	App      zApp    `json:"app"`
	Device   zDevice `json:"device"`
}

type zImp struct {
	Id       string  `json:"id"`
	Bidfloor float64 `json:"bidfloor"`
	Secure   int     `json:"secure"`
}

type zApp struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Bundle  string `json:"bundle"`
}

type zDevice struct {
	Ext       ext    `json:"ext"`
	Make      string `json:"make"`
	Model     string `json:"model"`
	Oaid      string `json:"oaid"`
	Imei      string `json:"imei"`
	Imeimd5   string `json:"imeimd5"`
	Os        string `json:"os"`
	OsVersion string `json:"os_version"`
	Ua        string `json:"ua"`
	Idfa      string `json:"idfa"`
	Caid      string `json:"caid"`
}

type ext struct {
	AndroidId string `json:"androidId"`
	Mac       string `json:"mac"`
}

type zhangYuPlugin struct {
	opts Options
}

func (z *zhangYuPlugin) String() string {
	return fmt.Sprintf("opts: %+v", z.opts)
}

func (z *zhangYuPlugin) GetOptions() Options {
	return z.opts
}

func (z *zhangYuPlugin) Run(ctx *Context) {
	hystrixErr := hystrix.Do(z.opts.Name, func() error {
		defer func() {
			if err := recover(); err != nil {
				z.opts.RecoverFunc(ctx, z.opts, err)
			}
		}()
		runStart := time.Now()
		ctx.Opt = z.opts
		if pointer.UnwrapOr(z.opts.IsTurnOff, false) {
			// 插件已关闭，直接退出
			return nil
		}

		fn := func(ctx *Context) {
			runDuration := time.Since(runStart).Milliseconds()
			logger.DebugCtx(ctx.Ctx, "Plugin %v cost time is %v", z.opts.Name, runDuration)
		}

		for i := len(z.opts.HdlrWrappers); i > 0; i-- {
			fn = z.opts.HdlrWrappers[i-1](fn)
		}

		fn(ctx)

		if ctx.Err != nil {
			return ctx.Err
		}

		return nil
	}, func(exeErr error) error {
		if !errors.Is(exeErr, ZyCallErr) {
			logger.ErrorCtx(ctx.Ctx, z.opts.Name, "Plugin %v fallback err: %v", z.opts.Name, exeErr)
		}
		return exeErr
	})
	ctx.Err = hystrixErr
}

func NewZhangYuPlugin(conf dto.DSPConf) Plugin {
	plg := new(zhangYuPlugin)
	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(zhangYuMsgConvertBefore),
		WrapFunc(zhangYuCallApi),
		WrapFunc(zhangYuMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func zhangYuMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil || len(srcReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuMsgConvertBefore src req type err, req is %v", ctx.SrcReq)
			return
		}
		var imps []zImp
		imps = append(imps, zImp{
			Id:       srcReq.Imp[0].Id,
			Bidfloor: srcReq.Imp[0].Bidfloor,
			Secure:   int(srcReq.Imp[0].Secure),
		})

		var _app zApp
		if srcReq.App != nil {
			_app = zApp{
				Name:    srcReq.App.AppName,
				Version: srcReq.App.AppVersion,
				Bundle:  srcReq.App.Bundle,
			}
		}
		var dev zDevice
		var pid string
		if srcReq.Device != nil {
			var os string
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				os = "2"
				pid = ctx.Opt.IosPid
			} else {
				os = "1"
				pid = ctx.Opt.AndroidPid
			}
			var mac string
			if len(srcReq.Device.Mac) == 12 {
				mac = srcReq.Device.Mac
				mac = fmt.Sprintf("%s:%s:%s:%s:%s:%s", mac[0:2], mac[2:4], mac[4:6], mac[6:8], mac[8:10], mac[10:12])

			}
			dev = zDevice{
				Ext: ext{
					AndroidId: srcReq.Device.AndroidId,
					Mac:       mac,
				},
				Make:      srcReq.Device.Make,
				Model:     srcReq.Device.Model,
				Oaid:      srcReq.Device.Oaid,
				Imei:      srcReq.Device.Imei,
				Imeimd5:   strings.ToLower(srcReq.Device.ImeiMd5),
				Os:        os,
				OsVersion: srcReq.Device.Osv,
				Ua:        srcReq.Device.Ua,
				Idfa:      srcReq.Device.Idfa,
				Caid:      srcReq.Device.Caid,
			}
		}
		dstReq := &zyReq{
			Version:  "2.1",
			Id:       srcReq.Id,
			Imp:      imps,
			SlotId:   pid,
			Bidfloor: srcReq.Imp[0].Bidfloor,
			Ip:       srcReq.Device.Ip,
			App:      _app,
			Device:   dev,
		}
		ctx.Data = dstReq
		fn(ctx)
	}
}

func zhangYuCallApi(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		dstReq, ok := ctx.Data.(*zyReq)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuCallApi req type err, req is %v", ctx.Data)
			return
		}
		pid := dstReq.SlotId

		data, err := json.Marshal(dstReq)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuCallApi marshal err, err is %v", err)
			return
		}
		var header = map[string]string{
			"Content-Type": "application/json",
		}
		respData, _, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      ctx.Opt.RtbURL + pid,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		if err != nil {
			if errors.Is(err, utils.ErrHTTPCall) || errors.Is(err, utils.ErrHTTPCallTimeout) {
				ctx.Err = ZyCallErr
			}
			return
		}
		resp := &zyResp{}
		if len(respData) != 0 {
			err = json.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuCallApi unmarshal resp err, err:", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func zhangYuMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*zyResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuMsgConvertAfter resp type err, resp is %v", ctx.Resp)
			return
		}
		srcResp, ok := ctx.SrcResp.(*proto.BidResponse)
		if !ok || srcResp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuMsgConvertAfter src resp type err, resp is %v", ctx.SrcResp)
			return
		}
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "zhangYuMsgConvertAfter src req type err, req is %v", ctx.SrcReq)
			return
		}

		if resp.Code != 0 {
			logger.InfoCtx(ctx.Ctx, "zhangYuMsgConvertAfter resp code not 0, code is %v", resp.Code)
			return
		}
		var bids []*proto.BidResponse_Bid
		if len(resp.Data.Ads) != 0 {
			var _img []string
			for _, aet := range resp.Data.Ads[0].Native.Assets {
				if aet.Img.Url != "" {
					_img = append(_img, aet.Img.Url)
				}
			}

			// 宏替换
			var exposalUrls, clickMonitorUrls []string
			pStr := strconv.Itoa(resp.Data.Ads[0].Price)
			for _, url := range resp.Data.Ads[0].Native.Imptrackers {
				exposalUrls = append(exposalUrls, strings.Replace(url, "__PRICE__", pStr, -1))
			}
			for _, url := range resp.Data.Ads[0].Native.Link.Clicktrackers {
				url = strings.Replace(url, "{DOWN_X}", "__DOWN_X__", -1)
				url = strings.Replace(url, "__DP_DOWN_X__", "__DOWN_X__", -1)
				url = strings.Replace(url, "{DOWN_Y}", "__DOWN_Y__", -1)
				url = strings.Replace(url, "__DP_DOWN_Y__", "__DOWN_Y__", -1)
				url = strings.Replace(url, "{UP_X}", "__UP_X__", -1)
				url = strings.Replace(url, "__DP_UP_X__", "__UP_X__", -1)
				url = strings.Replace(url, "{UP_Y}", "__UP_Y__", -1)
				url = strings.Replace(url, "__DP_UP_Y__", "__UP_Y__", -1)
				url = strings.Replace(url, "{TS_M}", "__TS__", -1)
				clickMonitorUrls = append(clickMonitorUrls, url)
			}

			var mediaStyle int32
			if resp.Data.Ads[0].AdType == 1 {
				mediaStyle = 1
			} else if resp.Data.Ads[0].AdType == 2 {
				mediaStyle = 2
			} else if resp.Data.Ads[0].AdType == 3 {
				mediaStyle = 4
			}
			var dpTrackUrls []string
			for _, dp := range resp.Data.Ads[0].Native.DeepLinkTrackers {
				// deeplink 安装调起成功
				if dp.Event == 103 {
					dpTrackUrls = dp.Imp
				}
			}
			var progID, progPath string
			if resp.Data.Ads[0].Native.WxMiniUser != nil {
				progID = *resp.Data.Ads[0].Native.WxMiniUser
			}
			if resp.Data.Ads[0].Native.WxMiniPath != nil {
				progPath = *resp.Data.Ads[0].Native.WxMiniPath
			}
			var clickUrl, dpurl string
			if resp.Data.Ads[0].Native.Link.Fallback == "" {
				clickUrl = resp.Data.Ads[0].Native.Link.Url
			} else {
				dpurl = resp.Data.Ads[0].Native.Link.Url
				clickUrl = resp.Data.Ads[0].Native.Link.Fallback
			}
			bids = append(bids, &proto.BidResponse_Bid{
				Impid:   srcReq.Imp[0].Id,
				AdType:  AdTypeAppStart,
				AdStyle: AdStylePic,
				Item: &proto.BidResponse_Item{
					ClickUrl:                    clickUrl,
					Imgs:                        _img,
					ExposalUrls:                 exposalUrls,
					ClickMonitorUrls:            clickMonitorUrls,
					DownloadAppInfo:             nil,
					MediaStyle:                  mediaStyle,
					DownloadUrl:                 "",
					DplUrl:                      dpurl,
					DownloadTrackUrls:           resp.Data.Ads[0].Native.DownloadTrackUrls,
					DownloadedTrackUrls:         resp.Data.Ads[0].Native.DownloadedTrackUrls,
					DpSuccessTrackUrls:          dpTrackUrls,
					InstalledTrackUrls:          resp.Data.Ads[0].Native.InstalledTrackUrls,
					ActionTrackUrls:             resp.Data.Ads[0].Native.OpenTrackUrls,
					MiniProgramId:               progID,
					MiniProgramPath:             progPath,
					MiniProgramType:             1,
					MiniProgramSuccessTrackUrls: nil,
				},
				Price:   float64(resp.Data.Ads[0].Price),
				Nurl:    "",
				ApiName: ctx.Opt.APIName,
			})
		}
		var seatbids []*proto.BidResponse_SeatBid
		seatbids = append(seatbids, &proto.BidResponse_SeatBid{Bid: bids})
		srcResp.Id = srcReq.Id
		srcResp.Seatbid = seatbids

		ctx.SrcResp = srcResp
		fn(ctx)
	}
}

var (
	ErrFLCall = errors.New("call feng lan api err")

	connectTypeMap = map[int32]int{
		1:   0,
		2:   2,
		3:   3,
		4:   4,
		5:   7,
		100: 1,
	}

	carrieMap = map[int32]int{
		0: 0,
		1: 1,
		2: 3,
		3: 2,
	}
)

type flResp struct {
	Ret  int64           `json:"ret"`
	Msg  string          `json:"msg"`
	Data map[int64]posId `json:"data"`
}

type posId struct {
	List []list `json:"list"`
}

type list struct {
	AdId           string      `json:"ad_id"`
	Crid           string      `json:"crid"`
	ImpressionLink []string    `json:"impression_link"`
	ClickLink      []string    `json:"click_link"`
	InteractType   int         `json:"interact_type"`
	ConvTracks     []convTrack `json:"conv_tracks"`
	AdUrl          string      `json:"ad_url"`
	LandpageUrl    string      `json:"landpage_url"`
	DownloadUrl    string      `json:"download_url"`
	DeepLink       string      `json:"deep_link"`
	CrtType        int         `json:"crt_type"`
	IconUrl        string      `json:"icon_url"`
	Imgs           []flImg     `json:"imgs"`
	Title          string      `json:"title"`
	Description    string      `json:"description"`
	PackageName    string      `json:"package_name"`
	AppName        string      `json:"app_name"`
	Publisher      string      `json:"publisher"`
	AppVersion     string      `json:"app_version"`
	Appinfo        string      `json:"appinfo"`
	AppinfoUrl     string      `json:"appinfo_url"`
	PackageSize    int         `json:"package_size"`
	Permission     string      `json:"permission"`
	PrivacyUrl     string      `json:"privacy_url"`
	Ecpm           int         `json:"ecpm"`
	WinNoticeUrl   string      `json:"win_notice_url"`
}

type convTrack struct {
	ConvType int      `json:"conv_type"`
	ConvUrls []string `json:"conv_urls"`
}

type flImg struct {
	Url string `json:"url"`
}

type pos struct {
	Id      int64 `json:"id"`
	Width   int   `json:"width"`
	Height  int   `json:"height"`
	AdCount int   `json:"ad_count"`
}

type media struct {
	AppId         string `json:"app_id"`
	AppBundleId   string `json:"app_bundle_id"`
	AppBundleName string `json:"app_bundle_name"`
}

type flDevice struct {
	Ip                 string   `json:"ip"`
	Mac                string   `json:"mac"`
	Ua                 string   `json:"ua"`
	Os                 string   `json:"os"`
	OsVersion          string   `json:"os_version"`
	Model              string   `json:"model"`
	Manufacturer       string   `json:"manufacturer"`
	DeviceType         int      `json:"device_type"`
	ScreenWidth        int32    `json:"screen_width"`
	ScreenHeight       int32    `json:"screen_height"`
	Idfa               string   `json:"idfa"`
	IdfaMd5            string   `json:"idfa_md5"`
	Imei               string   `json:"imei"`
	ImeiMd5            string   `json:"imei_md5"`
	AndroidID          string   `json:"android_id"`
	Oaid               string   `json:"oaid"`
	DeviceBirthSec     string   `json:"device_birth_sec"`
	DeviceStartSec     string   `json:"device_start_sec"`
	DeviceNameMd5      string   `json:"device_name_md5"`
	HardwareMachine    string   `json:"hardware_machine"`
	PhysicalMemoryByte string   `json:"physical_memory_byte"`
	HarddiskSizeByte   string   `json:"harddisk_size_byte"`
	SystemUpdateSec    string   `json:"system_update_sec"`
	HardwareModel      string   `json:"hardware_model"`
	Country            string   `json:"country"`
	Language           string   `json:"language"`
	TimeZone           string   `json:"time_zone"`
	CaidMulti          []flCaid `json:"caid_multi"`
	HmsVersion         string   `json:"hms_version"`
	BootMark           string   `json:"boot_mark"`
	UpdateMark         string   `json:"update_mark"`
	AppList            []string `json:"applist_package_name"`
}

type network struct {
	ConnectType int `json:"connect_type"`
	Carrier     int `json:"carrier"`
}

type flGeo struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type flCaid struct {
	Caid        string `json:"caid"`
	CaidVersion string `json:"caid_version"`
}

type fengLanPlugin struct {
	opts Options
}

func (f *fengLanPlugin) String() string {
	return fmt.Sprintf("opts: %+v", f.opts)
}

func (f *fengLanPlugin) GetOptions() Options {
	return f.opts
}

func (f *fengLanPlugin) Run(ctx *Context) {
	hystrixErr := hystrix.Do(f.opts.Name, func() error {
		defer func() {
			if err := recover(); err != nil {
				f.opts.RecoverFunc(ctx, f.opts, err)
			}
		}()
		runStart := time.Now()
		ctx.Opt = f.opts
		if pointer.UnwrapOr(f.opts.IsTurnOff, false) == true {
			// 插件已关闭，直接退出
			return nil
		}

		fn := func(ctx *Context) {
			runDuration := time.Since(runStart).Milliseconds()
			logger.DebugCtx(ctx.Ctx, "Plugin %v cost time is %v", f.opts.Name, runDuration)
		}

		for i := len(f.opts.HdlrWrappers); i > 0; i-- {
			fn = f.opts.HdlrWrappers[i-1](fn)
		}

		fn(ctx)

		if ctx.Err != nil {
			return ctx.Err
		}

		return nil
	}, func(exeErr error) error {
		if !errors.Is(exeErr, ErrFLCall) {
			logger.ErrorCtx(ctx.Ctx, f.opts.Name, "Plugin %v fallback err: %v", f.opts.Name, exeErr)
		}
		return exeErr
	})
	ctx.Err = hystrixErr

	return
}

func NewFengLanPlugin(conf dto.DSPConf) Plugin {
	plg := new(fengLanPlugin)

	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(fengLanMsgConvertBefore),
		WrapFunc(fengLanCallApi),
		WrapFunc(fengLanMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func fengLanMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil || len(srcReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertBefore src req type err, req is %v", ctx.SrcReq)
			return
		}
		var appID, bundle, appName string
		if srcReq.App != nil {
			bundle = srcReq.App.Bundle
			appName = srcReq.App.AppName
		}

		var _device flDevice
		var _network network
		var _geo flGeo
		var pid string
		if srcReq.Device != nil {
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				pid = ctx.Opt.IosPid
			} else {
				pid = ctx.Opt.AndroidPid
			}
			var mac string
			if len(srcReq.Device.Mac) == 12 {
				mac = srcReq.Device.Mac
				mac = fmt.Sprintf("%s:%s:%s:%s:%s:%s", mac[0:2], mac[2:4], mac[4:6], mac[6:8], mac[8:10], mac[10:12])

			}
			var physicalMemoryByte, harddiskSizeByte string
			if srcReq.Device.MemorySize != 0 {
				physicalMemoryByte = fmt.Sprintf("%d", srcReq.Device.MemorySize*1024)
			}
			if srcReq.Device.DiskSize != 0 {
				harddiskSizeByte = fmt.Sprintf("%d", srcReq.Device.DiskSize*1024)
			}
			var model string
			model, err := url.QueryUnescape(srcReq.Device.Model)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore device model err, model is %v", srcReq.Device.Model)
				model = srcReq.Device.Model
			}
			_device = flDevice{
				Ip:                 srcReq.Device.Ip,
				Mac:                mac,
				Ua:                 srcReq.Device.Ua,
				Os:                 strings.ToLower(srcReq.Device.Os),
				OsVersion:          srcReq.Device.Osv,
				Model:              model,
				Manufacturer:       srcReq.Device.Make,
				DeviceType:         1,
				ScreenWidth:        srcReq.Device.ScreenWidth,
				ScreenHeight:       srcReq.Device.ScreenHeight,
				Idfa:               srcReq.Device.Idfa,
				IdfaMd5:            strings.ToLower(srcReq.Device.IdfaMd5),
				Imei:               srcReq.Device.Imei,
				ImeiMd5:            strings.ToLower(srcReq.Device.ImeiMd5),
				AndroidID:          srcReq.Device.AndroidId,
				Oaid:               srcReq.Device.Oaid,
				DeviceBirthSec:     srcReq.Device.DeviceBirthTime,
				DeviceStartSec:     srcReq.Device.BootTimeSec,
				DeviceNameMd5:      srcReq.Device.PhoneName,
				HardwareMachine:    model,
				PhysicalMemoryByte: physicalMemoryByte,
				HarddiskSizeByte:   harddiskSizeByte,
				SystemUpdateSec:    srcReq.Device.OsUpdateTimeSec,
				HardwareModel:      srcReq.Device.ModelCode,
				Country:            "CN",
				Language:           "zh-Hans-CN",
				TimeZone:           srcReq.Device.TimeZone,
				CaidMulti: []flCaid{{
					Caid:        srcReq.Device.Caid,
					CaidVersion: srcReq.Device.CaidVersion,
				}},
				HmsVersion: srcReq.Device.VerCodeOfHms,
				BootMark:   srcReq.Device.BootMark,
				UpdateMark: srcReq.Device.UpdateMark,
				AppList:    srcReq.InstalledApp,
			}
			_network = network{
				ConnectType: connectTypeMap[srcReq.Device.ConnectionType],
				Carrier:     carrieMap[srcReq.Device.Carrier],
			}

			if srcReq.Device.Geo != nil {
				_geo = flGeo{
					Lat: srcReq.Device.Geo.Lat,
					Lng: srcReq.Device.Geo.Lon,
				}
			}

			if strings.ToLower(srcReq.Device.Os) == "ios" {
				// todo appId = "10835"
				appID = ctx.Opt.IosAppID
			} else {
				// todo appId = "10834"
				appID = ctx.Opt.AndroidAppID
			}

		}

		_media := media{
			AppId:         appID,
			AppBundleId:   bundle,
			AppBundleName: appName,
		}

		pidInt, err := strconv.ParseInt(pid, 10, 64)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertBefore parse pid err, pid is %v", pid)
			return
		}
		_pos := pos{
			Id:      pidInt,
			Width:   1080,
			Height:  1920,
			AdCount: 1,
		}
		posData, err := json.Marshal(_pos)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore marshal pos err, err is %v", err)
			return
		}
		mediaData, err := json.Marshal(_media)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore marshal media err, err is %v", err)
			return
		}
		deviceData, err := json.Marshal(_device)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore marshal device err, err is %v", err)
			return
		}
		networkData, err := json.Marshal(_network)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore marshal network err, err is %v", err)
			return
		}
		geoData, err := json.Marshal(_geo)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertBefore marshal geo err, err is %v", err)
			return
		}
		data := url.Values{}
		data.Add("api_version", "1.6.0")
		data.Add("pos", string(posData))
		data.Add("media", string(mediaData))
		data.Add("device", string(deviceData))
		data.Add("network", string(networkData))
		data.Add("geo", string(geoData))
		ctx.Data = []byte(data.Encode())
		fn(ctx)
	}
}

func fengLanCallApi(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		data, ok := ctx.Data.([]byte)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanCallApi req type err, req is %v", ctx.Data)
			return
		}
		_url := ctx.Opt.RtbURL + "?" + string(data)
		respData, _, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "GET",
			CallType: utils.URLEncode,
			Data:     data,
			URL:      _url,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		if err != nil {
			if errors.Is(err, utils.ErrHTTPCall) || errors.Is(err, utils.ErrHTTPCallTimeout) {
				ctx.Err = ErrFLCall
			}
			return
		}
		resp := &flResp{}
		if respData != nil && len(respData) != 0 {
			err = json.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanCallApi unmarshal resp err, err:", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func fengLanMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*flResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertAfter resp type err, resp is %v", ctx.Resp)
			return
		}
		srcResp, ok := ctx.SrcResp.(*proto.BidResponse)
		if !ok || srcResp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertAfter src resp type err, resp is %v", ctx.SrcResp)
			return
		}
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertAfter src req type err, req is %v", ctx.SrcReq)
			return
		}

		if resp.Ret == 102006 || resp.Ret == 104440 {
			logger.InfoCtx(ctx.Ctx, "fengLanMsgConvertAfter no fill, code is %v", resp.Ret)
			return
		}

		if resp.Ret != 0 {
			logger.ErrorCtx(ctx.Ctx, "fengLanMsgConvertAfter resp code not 0, code is %v", resp.Ret)
			return
		}
		var bids []*proto.BidResponse_Bid
		var pidInt int64
		if srcReq.Device != nil {
			var pidStr string
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				pidStr = ctx.Opt.IosPid
			} else {
				pidStr = ctx.Opt.AndroidPid
			}

			var err error
			pidInt, err = strconv.ParseInt(pidStr, 10, 64)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fengLanMsgConvertAfter parse pid err, pid is %v", pidStr)
				return
			}
		}
		bidResp, ok := resp.Data[pidInt]
		if ok && len(bidResp.List) != 0 {
			var adstyle int32
			var _img []string
			if bidResp.List[0].CrtType == 11 {
				adstyle = AdStylePic
				for _, ig := range bidResp.List[0].Imgs {
					_img = append(_img, ig.Url)
				}
			}
			var mediaStyle int32
			var downloadAppInfo *proto.BidResponse_DownloadAppInfo
			if bidResp.List[0].InteractType == 0 {
				mediaStyle = 1
			} else if bidResp.List[0].InteractType == 1 {
				mediaStyle = 2
				mFlag := 1024.0 * 1024.0
				res := float64(bidResp.List[0].PackageSize) / mFlag
				packSize := fmt.Sprintf("%.2f", res)
				downloadAppInfo = &proto.BidResponse_DownloadAppInfo{
					AppName:    bidResp.List[0].AppName,
					Developer:  bidResp.List[0].Publisher,
					Version:    bidResp.List[0].AppVersion,
					PacketSize: packSize,
					Privacy:    bidResp.List[0].PrivacyUrl,
					Permission: bidResp.List[0].Permission,
					Desc:       bidResp.List[0].Appinfo,
					DescURL:    bidResp.List[0].AppinfoUrl,
				}
			}
			var startDownload, completeDownload, dpSuccess, installed []string
			for _, ct := range bidResp.List[0].ConvTracks {
				if ct.ConvType == 1 {
					startDownload = append(startDownload, ct.ConvUrls...)
				} else if ct.ConvType == 2 {
					completeDownload = append(completeDownload, ct.ConvUrls...)
				} else if ct.ConvType == 3 {
					installed = append(installed, ct.ConvUrls...)
				} else if ct.ConvType == 10 {
					dpSuccess = append(dpSuccess, ct.ConvUrls...)
				}

			}
			clickUrl := bidResp.List[0].LandpageUrl
			dpUrl := bidResp.List[0].DeepLink

			if bidResp.List[0].InteractType == 1 {
				if clickUrl == "" && dpUrl == "" {
					clickUrl = bidResp.List[0].DownloadUrl
				}
			}
			pstr := strconv.Itoa(bidResp.List[0].Ecpm)
			encodePriceValue := utils.AesECBEncrypt([]byte(pstr), []byte("316868516bfc2e78d89588e8f58a6b37"))
			encodePriceWinHexValue := string(utils.HexEncode(string(encodePriceValue)))
			nurl := strings.Replace(bidResp.List[0].WinNoticeUrl, "__AUCTION_PRICE__", encodePriceWinHexValue, -1)

			bids = append(bids, &proto.BidResponse_Bid{
				Impid:   srcReq.Imp[0].Id,
				AdType:  AdTypeAppStart,
				AdStyle: adstyle,
				Item: &proto.BidResponse_Item{
					Title:               bidResp.List[0].Title,
					Desc:                bidResp.List[0].Description,
					Icon:                bidResp.List[0].IconUrl,
					MediaStyle:          mediaStyle,
					DownloadUrl:         bidResp.List[0].DownloadUrl,
					DownloadAppInfo:     downloadAppInfo,
					ClickUrl:            clickUrl,
					DplUrl:              dpUrl,
					Imgs:                _img,
					ExposalUrls:         bidResp.List[0].ImpressionLink,
					ClickMonitorUrls:    bidResp.List[0].ClickLink,
					DownloadTrackUrls:   startDownload,
					DownloadedTrackUrls: completeDownload,
					InstalledTrackUrls:  dpSuccess,
					DpSuccessTrackUrls:  installed,
					PackageName:         bidResp.List[0].PackageName,
				},
				Price:   float64(bidResp.List[0].Ecpm),
				Nurl:    nurl,
				ApiName: ctx.Opt.APIName,
			})
		}
		var seatbids []*proto.BidResponse_SeatBid
		seatbids = append(seatbids, &proto.BidResponse_SeatBid{Bid: bids})
		srcResp.Id = srcReq.Id
		srcResp.Seatbid = seatbids

		ctx.SrcResp = srcResp
		fn(ctx)
	}
}

var (
	YhCallErr = errors.New("call you he api err")
	ycarrier  = map[int32]int{
		0: 0,
		1: 2,
		2: 1,
		3: 3,
	}

	ynet = map[int32]int{
		1:   0,
		2:   4,
		3:   5,
		4:   6,
		5:   7,
		100: 2,
	}
)

type yhResp struct {
	Id          string     `json:"id"`
	SeatBidList []yseatbid `json:"seat_bid_list"`
}

type yseatbid struct {
	BidList []ybid `json:"bid_list"`
}

type ybid struct {
	ImpId            string   `json:"imp_id"`
	Price            int      `json:"price"`
	ActionType       int      `json:"action_type"`
	SrcType          int      `json:"src_type"`
	DownloadType     int      `json:"download_type"`
	Material         material `json:"material"`
	AppInfo          app_info `json:"app_info"`
	WxMiniProgram    program  `json:"wx_mini_program"`
	ClickUrl         string   `json:"click_url"`
	Deeplink         string   `json:"deeplink"`
	Universal_link   string   `json:"universal_link"`
	Nurl             []string `json:"nurl"`
	Curl             []string `json:"curl"`
	DeeplinkTrackers []string `json:"deeplink_trackers"`
	DnStart          []string `json:"dn_start"`
	DnSucc           []string `json:"dn_succ"`
	DnInstStart      []string `json:"dn_inst_start"`
	DnInstSucc       []string `json:"dn_inst_succ"`
	DnActive         []string `json:"dn_active"`
}

type material struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Images      []yimg `json:"images"`
	Icon        yimg   `json:"icon"`
}

type yimg struct {
	Url string `json:"url"`
}

type app_info struct {
	PackageName string `json:"package_name"`
	AppName     string `json:"app_name"`
	AppSize     int    `json:"app_size"`
	AppLogo     string `json:"app_logo"`
	Version     string `json:"version"`
	Developer   string `json:"developer"`
}

type program struct {
	WxUsername string `json:"wx_username"`
	WxPath     string `json:"wx_path"`
}

type yhReq struct {
	Id      string   `json:"id"`
	ApiVer  string   `json:"api_ver"`
	Imp     []yImp   `json:"imp"`
	App     yApp     `json:"app"`
	Device  yDevice  `json:"device"`
	AppList []string `json:"iapp_list"`
}

type yImp struct {
	Id         string `json:"id"`
	Type       int    `json:"type"`
	Pid        string `json:"pid"`
	W          int    `json:"w"`
	H          int    `json:"h"`
	BidType    int    `json:"bid_type"`
	BidFloor   int    `json:"bid_floor"`
	ActionType int    `json:"action_type"`
}

type yApp struct {
	Name   string `json:"name"`
	Bundle string `json:"bundle"`
	Ver    string `json:"ver"`
}

type yDevice struct {
	Ip             string `json:"ip"`
	Ua             string `json:"ua"`
	Carrier        int    `json:"carrier"`
	ConnectionType int    `json:"connection_type"`
	DeviceType     int    `json:"device_type"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	Os             int    `json:"os"`
	Osv            string `json:"osv"`
	H              int32  `json:"h"`
	W              int32  `json:"w"`
	Imei           string `json:"imei"`
	ImeiMd5        string `json:"imei_md5"`
	Oaid           string `json:"oaid"`
	OaidMd5        string `json:"oaid_md5"`
	Caid           ycaid  `json:"caid"`
	Idfa           string `json:"idfa"`
	IdfaMd5        string `json:"idfa_md5"`
	Androidid      string `json:"androidid"`
	Mac            string `json:"mac"`
	BootMark       string `json:"boot_mark"`
	UpdateMark     string `json:"update_mark"`
	BirthTime      string `json:"birth_time"`
	BootTime       string `json:"boot_time"`
	UpdateTime     string `json:"update_time"`
	Paid           string `json:"paid"`
	AppVersion     string `json:"appstore_version"`
	HmsVersion     string `json:"hms_version"`
	Geo            ygeo   `json:"geo"`
}

type ycaid struct {
	Id      string `json:"id"`
	Version string `json:"version"`
}

type ygeo struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
}

type youHePlugin struct {
	opts Options
}

func (y *youHePlugin) String() string {
	return fmt.Sprintf("opts: %+v", y.opts)
}

func (y *youHePlugin) GetOptions() Options {
	return y.opts
}

func (y *youHePlugin) Run(ctx *Context) {
	hystrixErr := hystrix.Do(y.opts.Name, func() error {
		defer func() {
			if err := recover(); err != nil {
				y.opts.RecoverFunc(ctx, y.opts, err)
			}
		}()
		runStart := time.Now()
		ctx.Opt = y.opts
		if pointer.UnwrapOr(y.opts.IsTurnOff, false) {
			// 插件已关闭，直接退出
			return nil
		}

		fn := func(ctx *Context) {
			runDuration := time.Since(runStart).Milliseconds()
			logger.DebugCtx(ctx.Ctx, "Plugin %v cost time is %v", y.opts.Name, runDuration)
		}

		for i := len(y.opts.HdlrWrappers); i > 0; i-- {
			fn = y.opts.HdlrWrappers[i-1](fn)
		}

		fn(ctx)

		if ctx.Err != nil {
			return ctx.Err
		}

		return nil
	}, func(exeErr error) error {
		if !errors.Is(exeErr, YhCallErr) {
			logger.ErrorCtx(ctx.Ctx, y.opts.Name, "Plugin %v fallback err: %v", y.opts.Name, exeErr)
		}
		return exeErr
	})
	ctx.Err = hystrixErr

	return
}

func NewYouHePlugin(conf dto.DSPConf) Plugin {
	plg := new(youHePlugin)

	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(youHeMsgConvertBefore),
		WrapFunc(youHeCallApi),
		WrapFunc(youHeMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func youHeMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil || len(srcReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeMsgConvertBefore src req type err, req is %v", ctx.SrcReq)
			return
		}

		var _app yApp
		if srcReq.App != nil {
			_app = yApp{
				Name:   srcReq.App.AppName,
				Bundle: srcReq.App.Bundle,
				Ver:    srcReq.App.AppVersion,
			}
		}

		var _device yDevice
		var pid string
		if srcReq.Device != nil {
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				pid = ctx.Opt.IosPid
			} else {
				pid = ctx.Opt.AndroidPid
			}

			var os int
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				os = 2
			} else {
				os = 1
			}

			var mac string
			if len(srcReq.Device.Mac) == 12 {
				mac = srcReq.Device.Mac
				mac = fmt.Sprintf("%s:%s:%s:%s:%s:%s", mac[0:2], mac[2:4], mac[4:6], mac[6:8], mac[8:10], mac[10:12])

			}
			var _geo ygeo
			if srcReq.Device.Geo != nil {
				_geo = ygeo{
					Lat: srcReq.Device.Geo.Lat,
					Lon: srcReq.Device.Geo.Lon,
				}
			}
			var model string
			model, err := url.QueryUnescape(srcReq.Device.Model)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, "youHeMsgConvertBefore device model err, model is %v", srcReq.Device.Model)
				model = srcReq.Device.Model
			}
			_device = yDevice{
				Ip:             srcReq.Device.Ip,
				Ua:             srcReq.Device.Ua,
				Carrier:        ycarrier[srcReq.Device.Carrier],
				ConnectionType: ynet[srcReq.Device.ConnectionType],
				DeviceType:     1,
				Make:           srcReq.Device.Make,
				Model:          model,
				Os:             os,
				Osv:            srcReq.Device.Osv,
				H:              srcReq.Device.ScreenHeight,
				W:              srcReq.Device.ScreenWidth,
				Imei:           srcReq.Device.Imei,
				ImeiMd5:        srcReq.Device.ImeiMd5,
				Oaid:           srcReq.Device.Oaid,
				OaidMd5:        srcReq.Device.OaidMd5,
				Caid: ycaid{
					Id:      srcReq.Device.Caid,
					Version: srcReq.Device.CaidVersion,
				},
				Idfa:       srcReq.Device.Idfa,
				IdfaMd5:    srcReq.Device.IdfaMd5,
				Androidid:  srcReq.Device.AndroidId,
				Mac:        mac,
				BootMark:   srcReq.Device.BootMark,
				UpdateMark: srcReq.Device.UpdateMark,
				BirthTime:  srcReq.Device.DeviceBirthTime,
				BootTime:   srcReq.Device.BootTimeSec,
				UpdateTime: srcReq.Device.OsUpdateTimeSec,
				Paid:       srcReq.Device.Paid,
				AppVersion: srcReq.Device.VerCodeOfAG,
				HmsVersion: srcReq.Device.VerCodeOfHms,
				Geo:        _geo,
			}
		}
		var _imp []yImp
		_imp = append(_imp, yImp{
			Id:         srcReq.Imp[0].Id,
			Type:       3,
			Pid:        pid,
			W:          0,
			H:          0,
			BidType:    0,
			BidFloor:   int(srcReq.Imp[0].Bidfloor),
			ActionType: 0,
		})

		dstReq := &yhReq{
			Id:      srcReq.Id,
			ApiVer:  "1.0",
			Imp:     _imp,
			App:     _app,
			Device:  _device,
			AppList: srcReq.InstalledApp,
		}
		ctx.Data = dstReq
		fn(ctx)
	}
}

func youHeCallApi(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		dstReq, ok := ctx.Data.(*yhReq)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeCallApi req type err, req is %v", ctx.Data)
			return
		}

		data, err := json.Marshal(dstReq)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeCallApi marshal err, err is %v", err)
			return
		}
		var header = map[string]string{
			"Content-Type": "application/json",
		}
		respData, _, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      ctx.Opt.RtbURL,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		if err != nil {
			if errors.Is(err, utils.ErrHTTPCall) || errors.Is(err, utils.ErrHTTPCallTimeout) {
				ctx.Err = YhCallErr
			}
			return
		}
		resp := &yhResp{}
		if len(respData) != 0 {
			err = json.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeCallApi unmarshal resp err, err:", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func youHeMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*yhResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeMsgConvertAfter resp type err, resp is %v", ctx.Resp)
			return
		}
		srcResp, ok := ctx.SrcResp.(*proto.BidResponse)
		if !ok || srcResp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeMsgConvertAfter src resp type err, resp is %v", ctx.SrcResp)
			return
		}
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "youHeMsgConvertAfter src req type err, req is %v", ctx.SrcReq)
			return
		}
		var bids []*proto.BidResponse_Bid
		if len(resp.SeatBidList) != 0 && len(resp.SeatBidList[0].BidList) != 0 {
			_bid := resp.SeatBidList[0].BidList[0]
			var mediaStyle int32
			var miniType int32
			var downloadUrl string
			if _bid.ActionType == 0 || _bid.ActionType == 1 || _bid.ActionType == 3 {
				mediaStyle = 1
			} else if _bid.ActionType == 2 {
				mediaStyle = 2
				downloadUrl = _bid.ClickUrl
			} else if _bid.ActionType == 4 {
				mediaStyle = 4
				miniType = 1
			}
			mFlag := 1024.0
			res := float64(_bid.AppInfo.AppSize) / mFlag
			packSize := fmt.Sprintf("%.2f", res)
			var dp string
			if _bid.Deeplink != "" {
				dp = _bid.Deeplink
			} else {
				dp = _bid.Universal_link
			}
			var _img []string
			for _, ig := range _bid.Material.Images {
				_img = append(_img, ig.Url)
			}
			var nurl []string
			pstr := strconv.Itoa(_bid.Price)
			for _, url1 := range _bid.Nurl {
				url1 = strings.Replace(url1, "__PRICE__", pstr, -1)
				nurl = append(nurl, url1)
			}
			bids = append(bids, &proto.BidResponse_Bid{
				Impid:   srcReq.Imp[0].Id,
				AdType:  AdTypeAppStart,
				AdStyle: AdStylePic,
				Item: &proto.BidResponse_Item{
					Title:       _bid.Material.Title,
					Desc:        _bid.Material.Description,
					Icon:        _bid.Material.Icon.Url,
					MediaStyle:  mediaStyle,
					DownloadUrl: downloadUrl,
					DownloadAppInfo: &proto.BidResponse_DownloadAppInfo{
						AppName:    _bid.AppInfo.AppName,
						Developer:  _bid.AppInfo.Developer,
						Version:    _bid.AppInfo.Version,
						PacketSize: packSize,
					},
					ClickUrl:            _bid.ClickUrl,
					DplUrl:              dp,
					Imgs:                _img,
					ExposalUrls:         nurl,
					ClickMonitorUrls:    _bid.Curl,
					MiniProgramId:       _bid.WxMiniProgram.WxUsername,
					MiniProgramPath:     _bid.WxMiniProgram.WxPath,
					MiniProgramType:     miniType,
					DownloadTrackUrls:   _bid.DnStart,
					DownloadedTrackUrls: _bid.DnSucc,
					InstalledTrackUrls:  _bid.DnInstSucc,
					DpSuccessTrackUrls:  _bid.DeeplinkTrackers,
					ActionTrackUrls:     _bid.DnActive,
					PackageName:         _bid.AppInfo.PackageName,
				},
				Price:   float64(_bid.Price),
				ApiName: ctx.Opt.APIName,
			})
		}
		var seatbids []*proto.BidResponse_SeatBid
		seatbids = append(seatbids, &proto.BidResponse_SeatBid{Bid: bids})
		srcResp.Id = srcReq.Id
		srcResp.Seatbid = seatbids

		ctx.SrcResp = srcResp
		fn(ctx)
	}
}
