package plugin

import (
	"context"
	"errors"
	"fmt"
	"math"
	"os"
	"reflect"
	"regexp"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dao"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

const (
	DTalkRedisKey = "adx_plugin_dtalk_msg"
)

type DSPConfName string

var (
	CurPluginManager = NewPluginManager()

	AdxConfBusinessXunFei = DSPConfName("xunfei")
	AdxConfBusinessFanWei = DSPConfName("fanwei")
	AdxConfBusinessMeiShu = DSPConfName("meishu")
	AdxConfBusinessIQ     = DSPConfName("iq")
	AdxConfBusinessHuaWei = DSPConfName("huawei")

	// DEPRECATED 已弃用的插件

	AdxConfBusinessZhangYu = DSPConfName("zhangyu")
	AdxConfBusinessFengLan = DSPConfName("fenglan")
	AdxConfBusinessYouHe   = DSPConfName("youhe")

	// 已添加的插件列表，adx v1
	namePluginInitFuncMap = map[DSPConfName]func(conf dto.DSPConf) Plugin{
		AdxConfBusinessXunFei: NewXunFeiPlugin,
		AdxConfBusinessFanWei: NewFanWeiPlugin,
		AdxConfBusinessMeiShu: NewMeiShuPlugin,
		AdxConfBusinessIQ:     NewIQPlugin,
		AdxConfBusinessHuaWei: NewHuaWeiPlugin,
		// DEPRECATED 已弃用的插件
		AdxConfBusinessZhangYu: NewZhangYuPlugin,
		AdxConfBusinessFengLan: NewFengLanPlugin,
		AdxConfBusinessYouHe:   NewYouHePlugin,
	}
	APINameRegexp = regexp.MustCompile("adx_.*")
)

func LoadPlugin(pluginManager Manager, isInit bool) {
	ctx := context.Background()
	needGetLock := os.Getenv("env") != "staging"
	lockedDTalk := false
	// 等待自动过期
	mutex := redsync.New(goredis.NewPool(config.RedisClient)).NewMutex(DTalkRedisKey, redsync.WithExpiry(config.DefaultConfig.PluginSendMsgWait))

	if needGetLock {
		if err := mutex.TryLock(); err != nil {
			logger.InfoCtx(ctx, "try lock msg send mutex failed:%v", err)
		} else {
			lockedDTalk = true
		}
	}
	dTalk := utils.NewDingTalkMsgSend(config.DefaultConfig.DTalkToken, config.DefaultConfig.DTalkSecret, !lockedDTalk)

	dspConfDao := dao.NewDspConf()
	dspConfList, dbErr := dspConfDao.QueryData(ctx)
	dspConfDtoList := make([]dto.DSPConf, len(dspConfList))
	for i, v := range dspConfList {
		dspConfDto, err := dto.ConvertDSPConf2Dto(v)
		if err != nil {
			dTalk.SendMsg(ctx, fmt.Sprintf("加载参数出现错误\n转化对象错误:%v", err))
			logger.AlertCtx(ctx, "LoadPlugin", "check err :%v", err.Error())
			return
		}
		dspConfDtoList[i] = dspConfDto
	}

	slice.SortBy(dspConfDtoList, func(a, b dto.DSPConf) bool {
		return strings.Compare(a.Name, b.Name) < 0
	})
	dspConfMap := lo.SliceToMap(dspConfDtoList, func(item dto.DSPConf) (string, dto.DSPConf) {
		return item.Name, item
	})

	// 1、检查 mysql/redis 的err & 检查查询的配置是否为空
	err := checkErr(dspConfDtoList, dbErr)
	if err != nil {
		if errors.Is(err, errs.ErrEmptyDspConf) {
			dTalk.SendMsg(ctx, "加载参数出现错误\n查询存储返回DSP配置为空")
		} else {
			dTalk.SendMsg(ctx, "加载参数出现错误\n查询存储返回异常")
		}
		logger.AlertCtx(ctx, "LoadPlugin", "check err :%v", err.Error())
		return
	}

	// 2、检查是否的丢失部分必要插件配置
	var loseCustomDspNames []string
	customizeDspConfMap := lo.SliceToMap(lo.Filter(dspConfDtoList, func(item dto.DSPConf, _ int) bool {
		return item.PluginType == int(dto.PluginCustomize)
	}), func(item dto.DSPConf) (string, dto.DSPConf) { return item.Name, item })

	for customizeDspName := range namePluginInitFuncMap {
		if _, ok := customizeDspConfMap[string(customizeDspName)]; !ok {
			loseCustomDspNames = append(loseCustomDspNames, string(customizeDspName))
		}
	}
	if len(loseCustomDspNames) != 0 {
		dTalk.SendMsg(ctx, fmt.Sprintf("加载参数出现错误\n\n缺少定制化DSP配置项，DSP名称为：%v", strings.Join(loseCustomDspNames, "、")))
		logger.AlertCtx(ctx, "LoadPlugin", "lose loseCustomDspNames :%v", loseCustomDspNames)
		return
	}

	// 3、配置参数检查
	var updatedBadChangeNames []string
	_, oldDspConfMap := pluginManager.GetDspPluginMap()
	oldDspConfs := make([]dto.DSPConf, len(oldDspConfMap))
	i := 0
	for _, v := range oldDspConfMap {
		oldDspConfs[i] = v
		i++
	}
	slice.SortBy(oldDspConfs, func(a, b dto.DSPConf) bool {
		return strings.Compare(a.Name, b.Name) < 0
	})

	var dspConfCheckErrTps []lo.Tuple2[string, error]

	// 如果发生配置变更，但 updated_at 没有发生变更，这种情况为异常情况
	for _, dspConf := range dspConfDtoList {
		if cErr := dspConfCheck(dspConf); cErr != nil {
			dspConfCheckErrTps = append(dspConfCheckErrTps, lo.Tuple2[string, error]{A: dspConf.Name, B: cErr})
			continue
		}
		if isInit {
			continue
		}

		oldDspConf, ok := oldDspConfMap[dspConf.Name]
		// !ok -> 新增的
		if ok && oldDspConf.UpdatedAt.Equal(dspConf.UpdatedAt) {
			if oldDspConf.String() != dspConf.String() {
				updatedBadChangeNames = append(updatedBadChangeNames, dspConf.Name)
			}
		}
	}

	if len(dspConfCheckErrTps) != 0 {
		msgStr := ""
		for _, dspConfCheckErrTp := range dspConfCheckErrTps {
			msgStr += fmt.Sprintf("DSP名称：%v；错误原因：%v\n", dspConfCheckErrTp.A, dspConfCheckErrTp.B.Error())
		}
		dTalk.SendMsg(ctx, fmt.Sprintf("加载参数出现错误\n\nDSP配置错误\n%v", msgStr))
		logger.ErrorCtx(ctx, "dsp conf check result:%+v", dspConfCheckErrTps)
		return
	}
	if len(updatedBadChangeNames) != 0 {
		dTalk.SendMsg(ctx, fmt.Sprintf("加载参数出现错误\nDSP 配置变更时没有变更 updated_at 字段但内容字段发生了变更，DSP名称为：%v", strings.Join(updatedBadChangeNames, "、")))
		logger.AlertCtx(ctx, "LoadPlugin", "dsp conf updated_at not change:%v", updatedBadChangeNames)
		return
	}

	switch {
	case isInit:
		dTalk.SendMsg(ctx, fmt.Sprintf("配置初始化生效（立即生效）\n配置：\n%v",
			strings.Join(lo.Map(dspConfDtoList, func(item dto.DSPConf, i int) string { return cast.ToString(i) + "--" + item.String() }), "\n----------\n")))
		logger.InfoCtx(ctx, "init dsp conf:%+v", dspConfMap)
		resetPlugins(pluginManager, dspConfMap)
	case !reflect.DeepEqual(oldDspConfMap, dspConfMap):
		dTalk.SendMsg(ctx, fmt.Sprintf("配置即将生效（%d分钟后）\n差异:\n%v\n",
			int(math.Floor(config.DefaultConfig.PluginEffectWait.Minutes())),
			utils.FormatterPlgChangeText(
				strings.Join(lo.Map(oldDspConfs, func(item dto.DSPConf, i int) string { return cast.ToString(i) + "--" + item.String() }), "\n----------\n"),
				strings.Join(lo.Map(dspConfDtoList, func(item dto.DSPConf, i int) string { return cast.ToString(i) + "--" + item.String() }), "\n----------\n"))))
		logger.InfoCtx(ctx, "dsp conf reload:%+v", dspConfMap)
		<-time.After(config.DefaultConfig.PluginEffectWait)
		resetPlugins(pluginManager, dspConfMap)
		logger.InfoCtx(ctx, "dsp conf reload success")
	default:
		// logger.InfoCtx(ctx, "dsp conf not change:%+v", dspConfDtoList)
	}
}

func resetPlugins(pluginManager Manager, dspConfMap map[string]dto.DSPConf) {
	pluginMap := make(map[string]Plugin)
	customizeDspConfMap := make(map[string]dto.DSPConf)
	normalDspConfMap := make(map[string]dto.DSPConf)

	for _, dspConf := range dspConfMap {
		if dspConf.PluginType == int(dto.PluginCustomize) {
			customizeDspConfMap[dspConf.Name] = dspConf
		} else if dspConf.PluginType == int(dto.PluginNormal) {
			normalDspConfMap[dspConf.Name] = dspConf
		}
	}

	for _, dspConf := range customizeDspConfMap {
		initPluginFunc, ok := namePluginInitFuncMap[DSPConfName(dspConf.Name)]
		if !ok {
			logger.AlertCtx(context.Background(), "LoadPlugin", "initPluginFunc not found:%v", dspConf.Name)
			continue
		}
		pluginMap[dspConf.Name] = initPluginFunc(dspConf)
	}

	for _, dspConf := range normalDspConfMap {
		pluginMap[dspConf.Name] = NewDynamicPlugin(dspConf)
	}

	pluginManager.ResetDspPlugins(pluginMap, dspConfMap)
}

func dspConfCheck(dspConf dto.DSPConf) error {
	if !APINameRegexp.MatchString(dspConf.APIName) {
		return errors.New("API接口名称 必须以 adx_ 开头")
	}
	if !strings.HasPrefix(dspConf.RtbURL, "http://") && !strings.HasPrefix(dspConf.RtbURL, "https://") {
		return errors.New("请求地址必须以 http:// 或 https:// 开头")
	}
	if dspConf.PluginType != int(dto.PluginCustomize) && dspConf.PluginType != int(dto.PluginNormal) {
		return errors.New("插件类型不支持")
	}
	if dspConf.RtbTimeOut > 5000 {
		return errors.New("rtb 请求超时时间过大")
	}
	if dspConf.PluginTimeout > 2000 {
		return errors.New("整个DSP插件处理超时时间过大")
	}
	if dspConf.ErrorPercentThread < 0 || dspConf.ErrorPercentThread > 100 {
		return errors.New("错误率熔断阈值不在0-100内")
	}
	return nil
}

func checkErr(dspConfs []dto.DSPConf, dbErr error) error {
	if dbErr != nil {
		return dbErr
	}
	if len(dspConfs) == 0 {
		return errs.ErrEmptyDspConf
	}
	return nil
}
