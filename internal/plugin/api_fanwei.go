package plugin

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/mapping"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	proto "codeup.aliyun.com/xhey/server/adx/protobuf/adx"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/bytedance/sonic"
)

type fwResp struct {
	ID      string    `json:"id"`
	Seatbid []seatbid `json:"seatbid"`
	Bidid   string    `json:"bidid"`
}

type seatbid struct {
	Seat string `json:"seat"`
	Bid  []bid  `json:"bid"`
}

type bid struct {
	Price               float64  `json:"price"`
	Nurl                string   `json:"nurl"`
	Adm                 adm      `json:"adm"`
	ImpTrackers         []string `json:"imp_trackers"`
	ClickTrackers       []string `json:"click_trackers"`
	DownStartTrackers   []string `json:"down_start_trackers"`
	DownCompTrackers    []string `json:"down_comp_trackers"`
	DpTrackers          []string `json:"dp_trackers"`
	InstallCompTrackers []string `json:"install_comp_trackers"`
}

type adm struct {
	Img      []fImg `json:"img"`
	Land     string `json:"land"`
	Interact int    `json:"interact"`
	Dplink   string `json:"dplink"`
	App      dApp   `json:"app"`
}

type dApp struct {
	Name         string     `json:"name"`
	Icon         string     `json:"icon"`
	PackageName  string     `json:"package_name"`
	Version      string     `json:"version"`
	PackageSize  int        `json:"package_size"`
	Privacy      string     `json:"privacy"`
	Permission   string     `json:"permission"`
	WechatExt    *wechatObj `json:"wechat_ext"`
	AppDesc      string     `json:"app_desc"`
	AppDescUrl   string     `json:"app_desc_url"`
	AppDeveloper string     `json:"app_developer"`
}

type wechatObj struct {
	ProgramId   string `json:"program_id"`
	ProgramPath string `json:"program_path"`
}

type fImg struct {
	Url string `json:"url"`
}

type fwReq struct {
	Id     string  `json:"id"`
	Imp    []fImp  `json:"imp"`
	App    fApp    `json:"app"`
	Device fDevice `json:"device"`
	User   fUser   `json:"user"`
}

type fUser struct {
	AppList []string `json:"a_list"`
}

type fImp struct {
	Id       string  `json:"id"`
	SlotId   string  `json:"slotid"`
	Bidfloor float64 `json:"bidfloor"`
}

type fApp struct {
	Name   string `json:"name"`
	Ver    string `json:"ver"`
	Bundle string `json:"bundle"`
}

type fDevice struct {
	Ua             string `json:"ua"`
	Ip             string `json:"ip"`
	Imei           string `json:"imei"`
	Imeimd5        string `json:"imeimd5"`
	Oaid           string `json:"oaid"`
	Oaidmd5        string `json:"oaidmd5"`
	Idfa           string `json:"idfa"`
	Idfamd5        string `json:"idfamd5"`
	Caid           string `json:"caid"`
	Carrier        int    `json:"carrier"`
	Make           string `json:"make"`
	Model          string `json:"model"`
	Os             int    `json:"os"`
	Osv            string `json:"osv"`
	Devicetype     int    `json:"devicetype"`
	ConnectionType int    `json:"connection_type"`
	Den            int    `json:"den"`
	Ori            int    `json:"ori"`
	Sw             int    `json:"sw"`
	Sh             int    `json:"sh"`
	Paid           string `json:"paid"`
}

type fanWeiPlugin struct {
	opts Options
}

func (f *fanWeiPlugin) String() string {
	return fmt.Sprintf("opts: %+v", f.opts)
}

func (f *fanWeiPlugin) GetOptions() Options {
	return f.opts
}

func (f *fanWeiPlugin) Run(ctx *Context) {
	ctx.Err = hystrixRun(ctx, f.opts)
}

func NewFanWeiPlugin(conf dto.DSPConf) Plugin {
	plg := new(fanWeiPlugin)
	options := NewOptions(
		Name(conf.Name),
		APIName(conf.APIName),
		RtbURL(conf.RtbURL),
		IosAppID(conf.IosAppID),
		AndroidAppID(conf.AndroidAppID),
		IosPid(conf.IosPid),
		AndroidPid(conf.AndroidPid),
		TimeOut(conf.RtbTimeOut),
		IsTurnOff(conf.IsTurnOff),
		DailyReqLimiterOn(conf.DailyReqLimiterOn),
		PeriodLimit(utils.NewMinutePeriodLimit(conf.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))),
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(fanWeiMsgConvertBefore),
		WrapFunc(fanWeiCallApi),
		WrapFunc(fanWeiMsgConvertAfter),
	)
	plg.opts = options
	return plg
}

func fanWeiMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil || len(srcReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiMsgConvertBefore src req type err, req is %v", ctx.SrcReq)
			return
		}
		var pid string
		var _app fApp
		if srcReq.App != nil {
			_app = fApp{
				Ver:    srcReq.App.AppVersion,
				Name:   srcReq.App.AppName,
				Bundle: srcReq.App.Bundle,
			}
		}
		var dev fDevice
		if srcReq.Device != nil {
			var os int
			if strings.ToLower(srcReq.Device.Os) == "ios" {
				os = 1
				pid = ctx.Opt.IosPid
			} else {
				os = 2
				pid = ctx.Opt.AndroidPid
			}
			dev = fDevice{
				Ua:             srcReq.Device.Ua,
				Ip:             srcReq.Device.Ip,
				Imei:           srcReq.Device.Imei,
				Imeimd5:        srcReq.Device.ImeiMd5,
				Oaid:           srcReq.Device.Oaid,
				Oaidmd5:        srcReq.Device.OaidMd5,
				Idfa:           srcReq.Device.Idfa,
				Idfamd5:        srcReq.Device.IdfaMd5,
				Caid:           srcReq.Device.Caid,
				Carrier:        mapping.FCarrierMap[srcReq.Device.Carrier],
				Make:           srcReq.Device.Make,
				Model:          srcReq.Device.Model,
				Os:             os,
				Osv:            srcReq.Device.Osv,
				Devicetype:     1,
				ConnectionType: mapping.ConnectMap[srcReq.Device.ConnectionType],
				Den:            int(srcReq.Device.Ppi),
				Ori:            mapping.OriMap[srcReq.Device.Orientation],
				Sw:             int(srcReq.Device.ScreenWidth),
				Sh:             int(srcReq.Device.ScreenHeight),
				Paid:           srcReq.Device.Paid,
			}
		}
		var imps []fImp
		imps = append(imps, fImp{
			Id:       srcReq.Imp[0].Id,
			SlotId:   pid,
			Bidfloor: srcReq.Imp[0].Bidfloor,
		})
		dstReq := &fwReq{
			Id:     srcReq.Id,
			Imp:    imps,
			App:    _app,
			Device: dev,
			User: fUser{
				AppList: srcReq.InstalledApp,
			},
		}
		data, err := sonic.Marshal(dstReq)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiMsgConvertBefore marshal err, err is %v", err)
			return
		}
		ctx.Data = data
		fn(ctx)
	}
}

func fanWeiCallApi(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		data, ok := ctx.Data.([]byte)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiCallApi req type err, req is %v", ctx.Data)
			return
		}
		var header = map[string]string{
			"Connection":   "Keep-Alive",
			"Content-Type": "application/json",
		}
		respData, _, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      ctx.Opt.RtbURL,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		if err != nil {
			ctx.Err = errors.Join(errs.ErrCallAPI, err)
			return
		}
		resp := &fwResp{}
		if len(respData) != 0 {
			err = sonic.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiCallApi unmarshal resp err, err:", err)
				return
			}
		}
		ctx.Resp = resp
		fn(ctx)
	}
}

func fanWeiMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*fwResp)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiMsgConvertAfter resp type err, resp is %v", ctx.Resp)
			return
		}
		srcResp, ok := ctx.SrcResp.(*proto.BidResponse)
		if !ok || srcResp == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiMsgConvertAfter src resp type err, resp is %v", ctx.SrcResp)
			return
		}
		srcReq, ok := ctx.SrcReq.(*proto.BidRequest)
		if !ok || srcReq == nil {
			logger.ErrorCtx(ctx.Ctx, ctx.Opt.Name, "fanWeiMsgConvertAfter src req type err, req is %v", ctx.SrcReq)
			return
		}
		var bids []*proto.BidResponse_Bid
		if len(resp.Seatbid) != 0 {
			if len(resp.Seatbid[0].Bid) != 0 {
				var imgs []string
				for _, ig := range resp.Seatbid[0].Bid[0].Adm.Img {
					if ig.Url != "" {
						imgs = append(imgs, ig.Url)
					}
				}
				// 宏替换
				var exposalUrls, clickMonitorUrls []string
				price := resp.Seatbid[0].Bid[0].Price
				pStr := fmt.Sprintf("%.2f", price)
				for _, url := range resp.Seatbid[0].Bid[0].ImpTrackers {
					exposalUrls = append(exposalUrls, strings.ReplaceAll(url, "${AUCTION_PRICE}", pStr))
				}
				for _, url := range resp.Seatbid[0].Bid[0].ClickTrackers {
					url = strings.ReplaceAll(url, "__AUCTION_DX__", "__DOWN_X__")
					url = strings.Replace(url, "__AUCTION_DY__", "__DOWN_Y__", -1)
					url = strings.Replace(url, "__AUCTION_UX__", "__UP_X__", -1)
					url = strings.Replace(url, "__AUCTION_UY__", "__UP_Y__", -1)
					clickMonitorUrls = append(clickMonitorUrls, url)
				}
				appSize := float64(resp.Seatbid[0].Bid[0].Adm.App.PackageSize) / 1024
				packSizeStr := fmt.Sprintf("%.6f", appSize)
				var mediaStyle int32
				var downloadUrl string
				switch {
				case resp.Seatbid[0].Bid[0].Adm.Interact == 0 || resp.Seatbid[0].Bid[0].Adm.Interact == 3:
					mediaStyle = 1
				case resp.Seatbid[0].Bid[0].Adm.Interact == 1:
					mediaStyle = 2
					downloadUrl = resp.Seatbid[0].Bid[0].Adm.Land
				case resp.Seatbid[0].Bid[0].Adm.Interact == 2:
					mediaStyle = 4
				}

				var progID, progPath string
				if resp.Seatbid[0].Bid[0].Adm.App.WechatExt != nil {
					progID = resp.Seatbid[0].Bid[0].Adm.App.WechatExt.ProgramId
					progPath = resp.Seatbid[0].Bid[0].Adm.App.WechatExt.ProgramPath
				}
				bids = append(bids, &proto.BidResponse_Bid{
					Impid:   srcReq.Imp[0].Id,
					AdType:  AdTypeAppStart,
					AdStyle: AdStylePic,
					Item: &proto.BidResponse_Item{
						Icon:             resp.Seatbid[0].Bid[0].Adm.App.Icon,
						ClickUrl:         resp.Seatbid[0].Bid[0].Adm.Land,
						Imgs:             imgs,
						ExposalUrls:      exposalUrls,
						ClickMonitorUrls: clickMonitorUrls,
						DownloadAppInfo: &proto.BidResponse_DownloadAppInfo{
							AppName:    resp.Seatbid[0].Bid[0].Adm.App.Name,
							Developer:  resp.Seatbid[0].Bid[0].Adm.App.AppDeveloper,
							Version:    resp.Seatbid[0].Bid[0].Adm.App.Version,
							PacketSize: packSizeStr,
							Privacy:    resp.Seatbid[0].Bid[0].Adm.App.Privacy,
							Permission: resp.Seatbid[0].Bid[0].Adm.App.Permission,
							DescURL:    resp.Seatbid[0].Bid[0].Adm.App.AppDescUrl,
							Desc:       resp.Seatbid[0].Bid[0].Adm.App.AppDesc,
						},
						MediaStyle:                  mediaStyle,
						DownloadUrl:                 downloadUrl,
						DplUrl:                      resp.Seatbid[0].Bid[0].Adm.Dplink,
						DownloadTrackUrls:           resp.Seatbid[0].Bid[0].DownStartTrackers,
						DownloadedTrackUrls:         resp.Seatbid[0].Bid[0].DownCompTrackers,
						DpSuccessTrackUrls:          resp.Seatbid[0].Bid[0].DpTrackers,
						InstalledTrackUrls:          resp.Seatbid[0].Bid[0].InstallCompTrackers,
						ActionTrackUrls:             nil,
						MiniProgramId:               progID,
						MiniProgramPath:             progPath,
						MiniProgramType:             1,
						MiniProgramSuccessTrackUrls: nil,
						PackageName:                 resp.Seatbid[0].Bid[0].Adm.App.PackageName,
					},
					Price:   price,
					Nurl:    resp.Seatbid[0].Bid[0].Nurl,
					ApiName: ctx.Opt.APIName,
				})
			}
		}
		var seatbids []*proto.BidResponse_SeatBid
		seatbids = append(seatbids, &proto.BidResponse_SeatBid{Bid: bids})
		srcResp.Id = srcReq.Id
		srcResp.Bidid = resp.Bidid
		srcResp.Seatbid = seatbids

		ctx.SrcResp = srcResp
		fn(ctx)
	}
}
