package plugin

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/protocol"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	protoCSJ "codeup.aliyun.com/xhey/server/adx/protobuf/csj"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/duke-git/lancet/v2/pointer"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
)

const (
	CSJAPIVersion = "1.0.8"
)

// ImageMode 广告创意类型
type ImageMode int32

const (
	SingleImage ImageMode = iota + 1
	MultiImage
	Videos ImageMode = 4
)

// InteractionType 广告交互类型
type InteractionType int32

const (
	LandingPage InteractionType = 2
	Download    InteractionType = 4
)

// CSJPlugin 穿山甲
type CSJPlugin struct {
	opts Options
}

func (p *CSJPlugin) String() string {
	return fmt.Sprintf("opts: %+v", p.opts)
}

func (p *CSJPlugin) GetOptions() Options {
	return p.opts
}

func (p *CSJPlugin) Run(ctx *Context) {
	ctx.Err = hystrixRun(ctx, p.opts)
}

// NewCSJPluginV2 创建穿山甲插件
func NewCSJPluginV2(conf dto.AdTADMixConfV2) Plugin {
	plg := new(CSJPlugin)
	options := NewOptions(
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(CSJMsgConvertBefore),
		WrapFunc(CSJCallAPI),
		WrapFunc(CSJMsgConvertAfter),
	)
	// 加载V2配置至Options
	ReplaceV2PluginArgs(&options, &conf)
	plg.opts = options
	return plg
}

// CSJMsgConvertBefore 前置过滤，拼接参数
func CSJMsgConvertBefore(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		srcReq := ctx.GenericContext.Req
		// sdkExt为空，不请求渠道
		if len(srcReq.Ext.SDKExt) == 0 {
			return
		}
		var pid string
		if strings.ToLower(srcReq.Device.OS) == "android" {
			pid = ctx.Opt.AndroidPid
		}
		if strings.ToLower(srcReq.Device.OS) == "ios" {
			pid = ctx.Opt.IosPid
		}
		req := &protoCSJ.BidRequest{
			RequestId:  pointer.Of(srcReq.ID),
			ApiVersion: pointer.Of(CSJAPIVersion),
			AdxName:    pointer.Of(AdxName),
			Ip:         pointer.Of(srcReq.Device.IP),
			Adslots: []*protoCSJ.BidRequest_AdSlot{{
				Id:      pointer.Of(pid),
				AdCount: pointer.Of(uint32(1)), // 默认只请求一条广告
			}},
			Timeout: pointer.Of(cast.ToUint32(ctx.Opt.TimeOut)),
		}
		for _, sdkExtList := range srcReq.Ext.SDKExt {
			if sdkExtList.SDKType == "csj" && sdkExtList.TagID != "" && ctx.Opt.DspConfV2 != nil && sdkExtList.TagID == ctx.Opt.DspConfV2.TagID {
				for _, sdkInfoList := range sdkExtList.SDKInfo {
					if sdkInfoList.Type == "sdkToken" {
						req.SdkToken = pointer.Of(sdkInfoList.Value)
					}
				}
			}
		}
		logger.DebugCtx(ctx.Ctx, "CSJ plugin[CSJMsgConvertBefore] CSJ API req is %+v", req)
		data, err := proto.Marshal(req)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJMsgConvertBefore] marshal err, err is %v", err)
			return
		}
		ctx.Data = data
		fn(ctx)
	}
}

// CSJCallAPI 调用穿山甲API
func CSJCallAPI(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		if ctx.Err != nil {
			logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJCallAPI] Find err in CSJ plugin request context %v", ctx.Err)
		}
		data, ok := ctx.Data.([]byte)
		if !ok {
			logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJCallAPI] req type err, req is %v", ctx.Data)
			return
		}
		respData, httpCode, err := utils.HTTPCall(ctx.Ctx, utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      ctx.Opt.RtbURL,
			Header: map[string]string{
				"Content-Type": "application/octet-stream; charset=utf-8",
			},
			TimeOut: time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		})
		ctx.HTTPCode = httpCode
		if err != nil {
			ctx.Err = errors.Join(errs.ErrCallAPI, err)
			return
		}
		resp := &protoCSJ.BidResponse{}
		if len(respData) != 0 {
			err = proto.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJCallAPI] unmarshal resp err, err:", err)
				return
			}
		}
		logger.DebugCtx(ctx.Ctx, "CSJ plugin[CSJCallAPI] resp: %+v", resp)
		ctx.Resp = resp
		if resp.GetStatusCode() != 20000 {
			logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJCallAPI] resp code not 20000, code is %v, reason is %v, desc is %v", resp.StatusCode, resp.Reason, resp.Desc)
			return
		}
		fn(ctx)
	}
}

// CSJMsgConvertAfter 后置过滤，转换广告响应
func CSJMsgConvertAfter(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*protoCSJ.BidResponse)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, "CSJ plugin[CSJMsgConvertAfter] resp type err or resp is nil, resp is %v", ctx.Resp)
			return
		}
		srcReq := ctx.GenericContext.Req
		srcResp := ctx.GenericContext.Resp
		srcResp.ID = srcReq.ID
		// 如果没有广告返回，直接返回
		if len(resp.Ads) == 0 {
			logger.WarnCtx(ctx.Ctx, "CSJ plugin[CSJMsgConvertAfter] resp has no ads return")
			return
		}
		ad := resp.Ads[0]
		srcResp.BidID = ad.GetAdId()

		srcResp.DspRespCode = resp.GetStatusCode()
		srcResp.DspRespDesc = resp.GetDesc()

		sdkExt := protocol.ResponseSDKExt{
			SDKType: "csj",
			DspID:   ctx.Opt.DspConfV2.DSPTagConf.DspID,
			TagID:   ctx.Opt.DspConfV2.TagID,
			Price:   float64(ad.GetPrice()),
		}
		sdkExt.SDKInfo = []protocol.ResponseSDKInfo{{Type: "CSJSDKInfo", Value: ad.GetCreative().GetAdm()}}
		if len(ad.GetCreative().GetWinNoticeUrl()) > 0 && len(ad.GetCreative().GetLossNoticeUrl()) > 0 {
			sdkExt.NURL = ad.GetCreative().GetWinNoticeUrl()[0]
			sdkExt.LURL = ad.GetCreative().GetLossNoticeUrl()[0]
		}
		srcResp.Ext = protocol.ResponseExt{SDKExt: []protocol.ResponseSDKExt{sdkExt}}
		fn(ctx)
	}
}
