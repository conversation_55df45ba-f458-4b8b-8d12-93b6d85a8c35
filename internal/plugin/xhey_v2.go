package plugin

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/dao"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/errs"
	"codeup.aliyun.com/xhey/server/adx/internal/mapping"
	"codeup.aliyun.com/xhey/server/adx/internal/protocol"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	adx4DspProto "codeup.aliyun.com/xhey/server/adx/protobuf/adx4dsp"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/bytedance/sonic"
	"github.com/duke-git/lancet/v2/pointer"
	"github.com/spf13/cast"
)

type dynamicPluginV2 struct {
	opts Options
}

func (d *dynamicPluginV2) String() string {
	return fmt.Sprintf("opts: %+v", d.opts)
}

func (d *dynamicPluginV2) GetOptions() Options {
	return d.opts
}

func (d *dynamicPluginV2) Run(ctx *Context) {
	defer func() {
		if err := recover(); err != nil {
			d.opts.RecoverFunc(ctx, d.opts, err)
		}
	}()
	runStart := time.Now()
	ctx.Opt = d.opts
	if pointer.UnwrapOr(d.opts.IsTurnOff, false) {
		return
	}

	fn := func(ctx *Context) {
		runDuration := time.Since(runStart).Milliseconds()
		logger.DebugCtx(ctx.Ctx, "Plugin %v cost time is %v", d.opts.Name, runDuration)
	}

	for i := len(d.opts.HdlrWrappers); i > 0; i-- {
		fn = d.opts.HdlrWrappers[i-1](fn)
	}
	fn(ctx)
	if ctx.Err != nil {
		if !(errors.Is(ctx.Err, utils.ErrHTTPCall) || errors.Is(ctx.Err, utils.ErrHTTPCallTimeout) || errors.Is(ctx.Err, utils.ErrRejectByHystrix)) {
			logger.ErrorCtx(ctx.Ctx, "Plugin %v fallback err: %v", ctx.Opt.Name, ctx.Err)
		}
	}
	// ctx.Err = hystrixRun(ctx, d.opts)
}

func NewDynamicPluginV2(confV2 *dto.AdTADMixConfV2) Plugin {
	plg := new(dynamicPluginV2)
	options := NewOptions(
		RecoverFunc(DefaultRecoverFunc),
		WrapFunc(hystrixControl),
		WrapFunc(dynamicCallBeforeMsgConvertV2),
		WrapFunc(dynamicCallAPIV2),
		WrapFunc(dynamicCallAfterMsgCheckV2),
	)
	ReplaceV2PluginArgs(&options, confV2)
	plg.opts = options
	return plg
}

func dynamicCallBeforeMsgConvertV2(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		req := ctx.GenericContext.Req
		os := strings.ToLower(req.Device.OS)
		var appSecMap map[string]int64
		if os == "ios" {
			appSecMap = mapping.IosInstallAppSecMap
		} else {
			appSecMap = mapping.AndroidInstallAppSecMap
		}
		req.Appendix01 = []int64{}
		for _, installAppStr := range req.InstalledApp {
			if _, ok := appSecMap[installAppStr]; ok {
				req.Appendix01 = append(req.Appendix01, appSecMap[installAppStr])
			}
		}
		ctx.SrcReq = req
		fn(ctx)
	}
}

func matchDealIDOverWrite(dspName string, dealType int32) bool {
	if dealType == dao.DealTypeBiddingPD && dspName == "讯飞" {
		return true
	}
	return false
}

func dynamicCallAPIV2(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		req := ctx.GenericContext.Req
		// 将GenericRequest转换为BidRequest
		dspReq := &adx4DspProto.BidRequest{
			Id:           req.ID,
			ApiVersion:   req.APIVersion,
			InstalledApp: req.InstalledApp,
			Appendix01:   req.Appendix01,

			// 转换App信息
			App: &adx4DspProto.BidRequest_App{
				AppName:    req.App.AppName,
				Bundle:     req.App.Bundle,
				AppVersion: req.App.AppVersion,
			},

			// 转换Device信息
			Device: &adx4DspProto.BidRequest_Device{
				Os:              req.Device.OS,
				Osv:             req.Device.OSVersion,
				Imei:            req.Device.IMEI,
				ImeiMd5:         req.Device.IMEIMD5,
				Oaid:            req.Device.OAID,
				OaidMd5:         req.Device.OAIDMD5,
				AndroidId:       req.Device.AndroidID,
				Idfa:            req.Device.IDFA,
				IdfaMd5:         req.Device.IDFAMD5,
				Mac:             req.Device.MAC,
				MacMd5:          req.Device.MACMD5,
				Ip:              req.Device.IP,
				IpV6:            req.Device.IPV6,
				Ua:              req.Device.UA,
				ConnectionType:  req.Device.ConnectionType,
				Brand:           req.Device.Brand,
				Make:            req.Device.Make,
				Model:           req.Device.Model,
				Hwv:             req.Device.HWV,
				Carrier:         req.Device.Carrier,
				MccMnc:          req.Device.MccMnc,
				ScreenHeight:    req.Device.ScreenHeight,
				ScreenWidth:     req.Device.ScreenWidth,
				Ppi:             req.Device.PPI,
				AppList:         req.Device.AppList,
				BootMark:        req.Device.BootMark,
				UpdateMark:      req.Device.UpdateMark,
				UpdateMark1:     req.Device.UpdateMark1,
				VerCodeOfHms:    req.Device.VerCodeOfHms,
				VerCodeOfAG:     req.Device.VerCodeOfAG,
				RomVersion:      req.Device.ROMVersion,
				Orientation:     req.Device.Orientation,
				BootTimeSec:     req.Device.BootTimeSec,
				PhoneName:       req.Device.PhoneName,
				MemorySize:      req.Device.MemorySize,
				DiskSize:        req.Device.DiskSize,
				OsUpdateTimeSec: req.Device.OSUpdateTimeSec,
				ModelCode:       req.Device.ModelCode,
				TimeZone:        req.Device.TimeZone,
				FileTime:        req.Device.FileTime,
				DeviceBirthTime: req.Device.DeviceBirthTime,
				CaidVersion:     req.Device.CaidVersion,
				Caid:            req.Device.Caid,
				Paid:            req.Device.Paid,
				WxInstalled:     req.Device.WxInstalled,

				// 转换Geo信息
				Geo: &adx4DspProto.BidRequest_Geo{
					Type:    req.Device.Geo.Type,
					Lat:     req.Device.Geo.Lat,
					Lon:     req.Device.Geo.Lon,
					Country: req.Device.Geo.Country,
				},
				CaidWithFactors: &adx4DspProto.CaidWithFactors{
					Caid:            req.Device.CaidWithFactors.CAID,
					CaidVersion:     req.Device.CaidWithFactors.CAIDVersion,
					CaidGenTime:     req.Device.CaidWithFactors.CAIDGenTime,
					CaidLast:        req.Device.CaidWithFactors.CAIDLast,
					CaidVersionLast: req.Device.CaidWithFactors.CAIDVersionLast,
					Factors: &adx4DspProto.CaidFactors{
						BootTimeInSec:  req.Device.CaidWithFactors.CAIDFactors.BootTimeInSec,
						CountryCode:    req.Device.CaidWithFactors.CAIDFactors.CountryCode,
						Language:       req.Device.CaidWithFactors.CAIDFactors.Language,
						DeviceName:     req.Device.CaidWithFactors.CAIDFactors.DeviceName,
						SystemVersion:  req.Device.CaidWithFactors.CAIDFactors.SystemVersion,
						Machine:        req.Device.CaidWithFactors.CAIDFactors.Machine,
						CarrierInfo:    req.Device.CaidWithFactors.CAIDFactors.CarrierInfo,
						Memory:         req.Device.CaidWithFactors.CAIDFactors.Memory,
						Disk:           req.Device.CaidWithFactors.CAIDFactors.Disk,
						SysFileTime:    req.Device.CaidWithFactors.CAIDFactors.SysFileTime,
						Model:          req.Device.CaidWithFactors.CAIDFactors.Model,
						TimeZone:       req.Device.CaidWithFactors.CAIDFactors.TimeZone,
						MntId:          req.Device.CaidWithFactors.CAIDFactors.MntID,
						DeviceInitTime: req.Device.CaidWithFactors.CAIDFactors.DeviceInitTime,
					},
				},
			},

			// 转换User信息
			User: &adx4DspProto.BidRequest_User{
				Age:    req.User.Age,
				Gender: req.User.Gender,
			},
		}

		// 转换Imp信息
		dspReq.Imp = make([]*adx4DspProto.BidRequest_Imp, len(req.Imp))
		for i, imp := range req.Imp {
			secure := 0
			dspImp := &adx4DspProto.BidRequest_Imp{
				Id:       imp.ID,
				TagId:    imp.TagID,
				Bidfloor: imp.BidFloor,
				Deeplink: imp.Deeplink,
				Secure:   int32(secure),

				// 转换AdSlotSize
				AdslotSize: &adx4DspProto.BidRequest_AdslotSize{
					Width:       imp.AdSlotSize.Width,
					Height:      imp.AdSlotSize.Height,
					Mimes:       imp.AdSlotSize.MimeList,
					Size:        imp.AdSlotSize.Size,
					TitleLength: imp.AdSlotSize.TitleLength,
					DescLength:  imp.AdSlotSize.DescLength,
					MinDuration: imp.AdSlotSize.MinDuration,
					MaxDuration: imp.AdSlotSize.MaxDuration,
				},
			}

			// 转换Deal信息
			dspImp.Deals = make([]*adx4DspProto.BidRequest_Deal, len(imp.DealList))
			for j, deal := range imp.DealList {
				dspImp.Deals[j] = &adx4DspProto.BidRequest_Deal{
					Id:       deal.ID,
					Bidfloor: deal.BidFloor,
					DealType: deal.DealType,
					Pdfloor:  deal.PDFloor,
				}
			}

			dspReq.Imp[i] = dspImp
		}
		if len(dspReq.Imp) == 0 {
			logger.ErrorCtx(ctx.Ctx, "[dynamicCallApi] device nil or imp=0, imp len=%v", len(dspReq.Imp))
			ctx.Err = errors.New("[dynamicCallApi] device nil or imp=0")
			return
		}
		// 设置平台相关的TagID
		if strings.ToLower(dspReq.Device.Os) == "ios" {
			dspReq.Imp[0].TagId = ctx.Opt.IosPid
			dspReq.Device.Os = "iOS"
		} else {
			dspReq.Imp[0].TagId = ctx.Opt.AndroidPid
			dspReq.Device.Os = "Android"
		}
		// 处理Deal信息
		for _, implDeal := range dspReq.Imp {
			if len(implDeal.Deals) == 0 {
				implDeal.Deals = append(implDeal.Deals, &adx4DspProto.BidRequest_Deal{
					Id: implDeal.Id,
				})
			}
			for _, deal := range implDeal.Deals {
				if ctx.Opt.DspConfV2 != nil {
					deal.DealType = cast.ToInt32(ctx.Opt.DspConfV2.DealType)
					if matchDealIDOverWrite(ctx.Opt.DspConfV2.DspName, deal.DealType) {
						deal.Id = ctx.Opt.DspConfV2.TagID
					}
					pdFloor := float64(0)
					if ctx.Opt.DspConfV2.PDFloor != nil {
						pdFloor = cast.ToFloat64(*ctx.Opt.DspConfV2.PDFloor)
					}
					bidFloor := float64(0)
					if ctx.Opt.DspConfV2.BidFloor != nil {
						bidFloor = cast.ToFloat64(*ctx.Opt.DspConfV2.BidFloor)
					}
					deal.Pdfloor = pdFloor
					deal.Bidfloor = bidFloor
				}
			}
		}
		for i := range req.Imp {
			if len(req.Imp[i].DealList) == 0 {
				req.Imp[i].DealList = append(req.Imp[i].DealList, protocol.DealInfo{
					ID: req.Imp[i].ID,
				})
			}
			for j := range req.Imp[i].DealList {
				if ctx.Opt.DspConfV2 != nil {
					req.Imp[i].DealList[j].DealType = cast.ToInt32(ctx.Opt.DspConfV2.DealType)
					if matchDealIDOverWrite(ctx.Opt.DspConfV2.DspName, req.Imp[i].DealList[j].DealType) {
						req.Imp[i].DealList[j].ID = ctx.Opt.DspConfV2.TagID
					}
					pdFloor := float64(0)
					if ctx.Opt.DspConfV2.PDFloor != nil {
						pdFloor = cast.ToFloat64(*ctx.Opt.DspConfV2.PDFloor)
					}
					bidFloor := float64(0)
					if ctx.Opt.DspConfV2.BidFloor != nil {
						bidFloor = cast.ToFloat64(*ctx.Opt.DspConfV2.BidFloor)
					}
					req.Imp[i].DealList[j].PDFloor = pdFloor
					req.Imp[i].DealList[j].BidFloor = bidFloor
				}
			}
		}
		data, err := sonic.Marshal(dspReq)
		if err != nil {
			logger.ErrorCtx(ctx.Ctx, "dynamicCallApi marshal err, err is %v", err)
			ctx.Err = err
			return
		}
		var header = map[string]string{
			"Content-Type": "application/json",
		}
		reqParam := utils.CallParam{
			Name:     ctx.Opt.Name,
			Method:   "POST",
			CallType: utils.Default,
			Data:     data,
			URL:      ctx.Opt.RtbURL,
			Header:   header,
			TimeOut:  time.Millisecond * time.Duration(ctx.Opt.TimeOut),
		}

		respData, httpCode, err := utils.HTTPCall(ctx.Ctx, reqParam)
		ctx.HTTPCode = httpCode
		if err != nil {
			ctx.Err = errors.Join(errs.ErrCallAPI, err)
			return
		}

		resp := &adx4DspProto.BidResponse{}
		if len(respData) != 0 {
			err = sonic.Unmarshal(respData, resp)
			if err != nil {
				logger.ErrorCtx(ctx.Ctx, "dynamicCallApi unmarshal resp err, err: %v", err)
				ctx.Err = err
				return
			}
		}
		ctx.Resp = resp

		reqStr, _ := sonic.MarshalString(dspReq)
		respStr, _ := sonic.MarshalString(resp)
		logger.DebugCtx(ctx.Ctx, "dynamicCallApi CallParam: name: %v , url: %v , req: %v,\n\n respData: %v", reqParam.Name, reqParam.URL, reqStr, respStr)
		if len(resp.GetSeatbid()) == 0 || len(resp.GetSeatbid()[0].GetBid()) == 0 {
			//
			//logger.InfoCtx(ctx.Ctx, "dynamicCallApi CallParam get empty result: name: %v , url: %v , req: %v,\n\n respData: %v", reqParam.Name, reqParam.URL, reqStr, respStr)
		}
		if reqParam.Name != "" && strings.Contains(reqParam.Name, "光粒") {
			logger.InfoCtx(ctx.Ctx, "[光粒渠道] dynamicCallApi CallParam - name:%s, url:%s, req:%s, resp:%s",
				reqParam.Name, reqParam.URL, reqStr, respStr)
		}
		fn(ctx)
	}
}

func dynamicCallAfterMsgCheckV2(fn HandlerFunc) HandlerFunc {
	return func(ctx *Context) {
		resp, ok := ctx.Resp.(*adx4DspProto.BidResponse)
		if !ok || resp == nil {
			logger.ErrorCtx(ctx.Ctx, "dynamicCallAfterMsgCheck resp type err, resp is %v", ctx.Resp)
			return
		}
		srcReq := ctx.GenericContext.Req
		srcResp := ctx.GenericContext.Resp

		if len(resp.Seatbid) != 0 && len(resp.Seatbid[0].Bid) != 0 && resp.Seatbid[0].Bid != nil {
			ret := resp.Seatbid[0].Bid[0]
			// 过滤不支持类型报价
			if (ret.AdType != AdTypeAppStart && ret.AdType != AdTypeInsert && ret.AdType != AdTypeBanner && ret.AdType != AdTypeInFeed) || ret.AdStyle != AdStylePic {
				return
			}
			// items为空 没素材放弃
			if ret.Items == nil {
				return
			}
			// 非PD的 价格小于等于0 放弃
			if ret.Price <= 0 && !(len(srcReq.Imp) == 1 && len(srcReq.Imp[0].DealList) == 1 && srcReq.Imp[0].DealList[0].DealType == dao.DealTypeBiddingPD) {
				return
			}
			// 下载��要素校验
			if ret.Items.MediaStyle == 2 && strings.ToLower(srcReq.Device.OS) != "ios" {
				if ret.Items.DownloadAppInfo == nil {
					return
				}
				downloadApp := ret.Items.DownloadAppInfo
				if downloadApp.AppName == "" || downloadApp.Developer == "" || downloadApp.Privacy == "" ||
					downloadApp.Permission == "" || downloadApp.Version == "" || downloadApp.DescURL == "" {
					return
				}
			}
			// 图片素材为空 放弃
			if len(ret.Items.Imgs) == 0 {
				return
			}
			resp.Seatbid[0].Bid[0].ApiName = ctx.Opt.APIName
		}
		// 校验成功，参与bidding
		srcResp.ID = srcReq.ID
		srcResp.BidID = resp.Bidid

		// 转换 SeatBid
		if len(resp.Seatbid) > 0 {
			srcResp.SeatBid = make([]protocol.SeatBid, len(resp.Seatbid))
			for i, seat := range resp.Seatbid {
				if len(seat.Bid) > 0 {
					srcResp.SeatBid[i].BidList = make([]protocol.BidInfo, len(seat.Bid))
					for j, bid := range seat.Bid {
						srcResp.SeatBid[i].BidList[j] = protocol.BidInfo{
							ImpID:   bid.Impid,
							AdType:  bid.AdType,
							AdStyle: bid.AdStyle,
							Price:   bid.Price,
							NURL:    bid.Nurl,
							LURL:    bid.Lurl,
							CRID:    bid.Crid,
							APIName: bid.ApiName,
							Cid:     bid.Cid,
						}
						if bid.Items != nil {
							srcResp.SeatBid[i].BidList[j].Item = protocol.AdItem{
								Title:                          bid.Items.Title,
								Desc:                           bid.Items.Desc,
								Icon:                           bid.Items.Icon,
								HTML:                           bid.Items.Html,
								MediaStyle:                     bid.Items.MediaStyle,
								DownloadURL:                    bid.Items.DownloadUrl,
								ClickURL:                       bid.Items.ClickUrl,
								DplURL:                         bid.Items.DplUrl,
								ImgList:                        bid.Items.Imgs,
								ExposalURLList:                 bid.Items.ExposalUrls,
								ClickMonitorURLList:            bid.Items.ClickMonitorUrls,
								MiniProgramID:                  bid.Items.MiniProgramId,
								MiniProgramPath:                bid.Items.MiniProgramPath,
								MiniProgramType:                bid.Items.MiniProgramType,
								MiniProgramSuccessTrackURLList: bid.Items.MiniProgramSuccessTrackUrls,
								DownloadTrackURLList:           bid.Items.DownloadTrackUrls,
								DownloadedTrackURLList:         bid.Items.DownloadedTrackUrls,
								InstalledTrackURLList:          bid.Items.InstalledTrackUrls,
								DpSuccessTrackURLList:          bid.Items.DpSuccessTrackUrls,
								ActionTrackURLList:             bid.Items.ActionTrackUrls,
								PackageName:                    bid.Items.PackageName,
								MiniProgramExtData:             bid.Items.MiniProgramExtData,
							}
							if bid.Items.DownloadAppInfo != nil {
								srcResp.SeatBid[i].BidList[j].Item.DownloadAppInfo = protocol.DownloadAppInfo{
									AppName:    bid.Items.DownloadAppInfo.AppName,
									Developer:  bid.Items.DownloadAppInfo.Developer,
									Version:    bid.Items.DownloadAppInfo.Version,
									PacketSize: bid.Items.DownloadAppInfo.PacketSize,
									Privacy:    bid.Items.DownloadAppInfo.Privacy,
									Permission: bid.Items.DownloadAppInfo.Permission,
									Desc:       bid.Items.DownloadAppInfo.Desc,
									DescURL:    bid.Items.DownloadAppInfo.DescURL,
								}
							}
							if bid.Items.Video != nil {
								srcResp.SeatBid[i].BidList[j].Item.Video = protocol.VideoInfo{
									VideoURL:       bid.Items.Video.VideoUrl,
									VideoDuration:  bid.Items.Video.VideoDuration,
									VideoStartURL:  bid.Items.Video.VideoStartUrl,
									VideoFinishURL: bid.Items.Video.VideoFinishUrl,
									VideoVastXML:   bid.Items.Video.VideoVastXml,
									VideoEndImgURL: bid.Items.Video.VideoEndImgurl,
									VideoPreImgURL: bid.Items.Video.VideoPreImgurl,
								}
							}
						}
						// 转换 MonitorURLs
						if bid.Items.MonitorUrls != nil {
							for _, mu := range bid.Items.MonitorUrls {
								srcResp.SeatBid[i].BidList[j].Item.MonitorURLList = append(
									srcResp.SeatBid[i].BidList[j].Item.MonitorURLList,
									protocol.MonitorURL{
										EventType: mu.EventType,
										URLList:   mu.Urls,
									},
								)
							}
						}
					}
				}
			}
		}
		fn(ctx)
	}
}
