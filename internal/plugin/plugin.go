package plugin

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dao"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/adx/internal/protocol"
	"codeup.aliyun.com/xhey/server/adx/internal/utils"
	proto "codeup.aliyun.com/xhey/server/adx/protobuf/adx"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/afex/hystrix-go/hystrix"
	"github.com/duke-git/lancet/v2/pointer"
)

type Param struct {
	Req        any
	Resp       any
	GenericCtx *protocol.GenericContext
	Ctx        context.Context
}

type HandlerFunc func(*Context) // 插件真正执行逻辑

type HandlerWrapper func(HandlerFunc) HandlerFunc

type Manager interface {
	Fire(param Param) map[string]*Context
	ResetDspPlugins(map[string]Plugin, map[string]dto.DSPConf)
	GetDspPluginMap() (map[string]Plugin, map[string]dto.DSPConf)
}

func NewPluginManager() Manager {
	return &pluginManager{
		pluginMap:  make(map[string]Plugin),
		dspConfMap: make(map[string]dto.DSPConf),
		rwLock:     new(sync.RWMutex),
	}
}

type pluginManager struct {
	pluginMap  map[string]Plugin
	dspConfMap map[string]dto.DSPConf
	rwLock     *sync.RWMutex
}

// Fire 插件系统开始工作
func (pm *pluginManager) Fire(param Param) map[string]*Context {
	pm.rwLock.RLock()
	defer pm.rwLock.RUnlock()
	result := map[string]*Context{}
	wg := sync.WaitGroup{}
	for name, plg := range pm.pluginMap {
		newCtx := NewContext(param, name)
		dspConf, dspOk := pm.dspConfMap[name]
		req, convertOk := newCtx.SrcReq.(*proto.BidRequest)
		if convertOk && dspOk && req.GetInner().GetSource() == "topon" {
			if !matchVersion(newCtx.Ctx, req.GetDevice().GetOs(), req.GetApp().GetAppVersion(), dspConf) {
				logger.DebugCtx(newCtx.Ctx, "[plugin]fire matchVersion failed: %v", req.GetDevice().GetOs(), req.GetApp().GetAppVersion())
				continue
			}
		}
		wg.Add(1)
		result[plg.GetOptions().Name] = newCtx
		go func(pg Plugin, ctx *Context) {
			defer wg.Done()
			pg.Run(ctx)
		}(plg, newCtx)
	}
	wg.Wait()
	return result
}

func (pm *pluginManager) ResetDspPlugins(pluginMap map[string]Plugin, dspConfMap map[string]dto.DSPConf) {
	pm.rwLock.Lock()
	defer pm.rwLock.Unlock()
	pm.pluginMap = pluginMap
	pm.dspConfMap = dspConfMap
	for _, dspConf := range dspConfMap {
		hystrix.ConfigureCommand(dspConf.Name, hystrix.CommandConfig{
			Timeout:                dspConf.PluginTimeout,
			MaxConcurrentRequests:  dspConf.MaxConcurrentReq,
			RequestVolumeThreshold: dspConf.ReqVolumeThread,
			SleepWindow:            dspConf.SleepWindow,
			ErrorPercentThreshold:  dspConf.ErrorPercentThread,
		})
	}
}
func (pm *pluginManager) GetDspPluginMap() (map[string]Plugin, map[string]dto.DSPConf) {
	pm.rwLock.RLock()
	defer pm.rwLock.RUnlock()
	cpPlgMap := make(map[string]Plugin)
	cpDspConfMap := make(map[string]dto.DSPConf)
	for k, v := range pm.pluginMap {
		cpPlgMap[k] = v
	}
	for k, v := range pm.dspConfMap {
		cpDspConfMap[k] = v
	}
	return cpPlgMap, cpDspConfMap
}

type Plugin interface {
	GetOptions() Options
	Run(ctx *Context)
	fmt.Stringer // 实现该接口，有助于增强日志可读性
}

func ReplaceV2PluginArgs(options *Options, confV2 *dto.AdTADMixConfV2) {
	if confV2 == nil {
		return
	}
	options.DspConfV2 = confV2
	options.Name = confV2.TagName
	options.APIName = confV2.TagName
	options.RtbURL = confV2.ReqURL
	if confV2.Os == int(dto.OsTypeIos) {
		options.IosAppID = confV2.DSPAppConf.AppID
		options.IosPid = confV2.DSPTagConf.TagID
	} else if confV2.Os == int(dto.OsTypeAndroid) || confV2.Os == int(dto.OsTypeHarmony) {
		options.AndroidAppID = confV2.DSPAppConf.AppID
		options.AndroidPid = confV2.DSPTagConf.TagID
	}
	// 设置超时时间
	options.TimeOut = 500
	for _, sceneTimeOut := range confV2.AdReqTime {
		if sceneTimeOut.AdType == confV2.AdType {
			options.TimeOut = sceneTimeOut.Timeout
		}
	}
	turnOff := !(confV2.BaseDSPConf.Status == int(dao.PosStatusNormal) && confV2.DSPAppConf.Status == int(dao.PosStatusNormal) && confV2.DSPTagConf.Status == int(dao.PosStatusNormal))
	options.IsTurnOff = &turnOff
	options.DailyReqLimiterOn = pointer.UnwrapOr(confV2.RestrictType, 0) == 1
	options.PeriodLimit = utils.NewMinutePeriodLimit(confV2.GetDailyReqCnt2Minute(), config.RedisClient, "adx_hystrix", utils.TimeOut(utils.RedisTimeOut))
}

func matchVersion(ctx context.Context, os string, version string, dspConf dto.DSPConf) bool {
	match := true
	var osType dto.OsType
	if strings.ToLower(os) == "ios" {
		osType = dto.OsTypeIos
	} else {
		osType = dto.OsTypeAndroid
	}
	for _, osVerFilter := range dspConf.OsVersionFilter {
		curOk := version != ""
		if curOk && osVerFilter.Os == int(osType) {
			if osVerFilter.MinVersion != "" && utils.CompareVersion(ctx, version, osVerFilter.MinVersion) == -1 {
				match = false
			}
			if osVerFilter.MaxVersion != "" && utils.CompareVersion(ctx, version, osVerFilter.MaxVersion) == 1 {
				match = false
			}
		}
	}
	return match
}
