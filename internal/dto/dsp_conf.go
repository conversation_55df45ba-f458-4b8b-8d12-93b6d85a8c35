package dto

import (
	"fmt"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/dao"
	"github.com/bytedance/sonic"
)

type DSPConf struct {
	// 注意这里属性的变更对 plugin_loader.go 的影响
	Name       string
	APIName    string
	RtbURL     string
	IsTurnOff  bool
	PluginType int

	AndroidPid   string
	AndroidAppID string
	IosPid       string
	IosAppID     string

	DailyReqLimiterOn bool
	DailyReqCnt       int // 单位：万次

	RtbTimeOut         int
	PluginTimeout      int
	MaxConcurrentReq   int
	SleepWindow        int
	ReqVolumeThread    int
	ErrorPercentThread int

	OsVersionFilter []OsVersionFilter
	AdDspConfV2     *AdTADMixConfV2
	UpdatedAt       time.Time
}

type OsVersionFilter struct {
	Os         int
	MinVersion string
	MaxVersion string
}

func (d DSPConf) GetDailyReqCnt2Minute() int {
	return d.DailyReqCnt * 10000 / 24 / 60
}

func (d DSPConf) String() string {
	return fmt.Sprintf("DSP名称:%v  API接口名称:%v  请求地址:%v  流量开启:%v  DSP类型:%v iOS应用的唯一标识符:%v  Android应用的唯一标识符:%v  iOS平台广告位ID:%v  Android平台广告位ID:%v  流量逐步放量开关:%v  日流量总量:%v  请求超时时间:%v  整个DSP插件处理超时时间:%v  最大并发请求数量:%v  服务熔断重试等待时间:%v  请求次数熔断阈值:%v  错误率熔断阈值:%v  版本控制:%v 最后编辑时间:%v",
		d.Name, d.APIName, d.RtbURL, !d.IsTurnOff, d.PluginType, d.IosAppID, d.AndroidAppID, d.IosPid, d.AndroidPid, d.DailyReqLimiterOn, d.DailyReqCnt, d.RtbTimeOut, d.PluginTimeout, d.MaxConcurrentReq, d.SleepWindow, d.ReqVolumeThread, d.ErrorPercentThread, d.OsVersionFilter, d.UpdatedAt.Format(time.DateTime),
	)
}

func ConvertDSPConf2Dto(dspConf dao.DSPConf) (DSPConf, error) {
	osVersionFilter := make([]OsVersionFilter, 0)
	if dspConf.VersionExtra != "" {
		err := sonic.Unmarshal([]byte(dspConf.VersionExtra), &osVersionFilter)
		if err != nil {
			return DSPConf{}, err
		}
	}
	return DSPConf{
		Name:               dspConf.Name,
		APIName:            dspConf.APIName,
		RtbURL:             dspConf.RtbURL,
		IsTurnOff:          dspConf.IsTurnOff,
		PluginType:         dspConf.PluginType,
		AndroidPid:         dspConf.AndroidPid,
		AndroidAppID:       dspConf.AndroidAppID,
		IosPid:             dspConf.IosPid,
		IosAppID:           dspConf.IosAppID,
		DailyReqLimiterOn:  dspConf.DailyReqLimiterOn,
		DailyReqCnt:        dspConf.DailyReqCnt,
		RtbTimeOut:         dspConf.RtbTimeOut,
		PluginTimeout:      dspConf.PluginTimeout,
		MaxConcurrentReq:   dspConf.MaxConcurrentReq,
		SleepWindow:        dspConf.SleepWindow,
		ReqVolumeThread:    dspConf.ReqVolumeThread,
		ErrorPercentThread: dspConf.ErrorPercentThread,
		UpdatedAt:          dspConf.UpdatedAt.Local(),
		OsVersionFilter:    osVersionFilter,
	}, nil
}
