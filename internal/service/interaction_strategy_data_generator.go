package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sync"
	"time"

	"codeup.aliyun.com/xhey/server/adx/internal/config"
	"codeup.aliyun.com/xhey/server/adx/internal/dto"
	"codeup.aliyun.com/xhey/server/serverkit/v2/logger"
	"github.com/bytedance/sonic"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
)

// 预期点击率变化时需要修改
const (
	RdsKeyMiddleFixClickRatioQueue             = "interaction_strategy_middle_fix_click_ratio_data_%s_%s"
	RdsKeyMiddleCurExpectClickRatio            = "interaction_strategy_middle_cur_expect_click_ratio_%s_%s"
	RdsKeyReceivedMiddleExposureRecallDataList = "interaction_strategy_middle_exposure_recall_list_%s_%s"
	RdsKeyReceivedMiddleClickedRecallDataHash  = "interaction_strategy_middle_click_recall_hash_%s_%s"
	exposureIntervalSec                        = 10
)

// localMiddleDelayXTsGp 曝光数据的延时队列组，曝光接受后需等待一段时间使用
var (
	localMiddleDelayXTsGp      = make(map[dto.StrategyUk]map[int64][]dto.InteractionStrategySend)
	localMiddleDelayXTsGpMutex sync.RWMutex
)

// Receive kafka data
func Receive(ctx context.Context, dataRecall dto.InteractionStrategySend) {
	memData := MemoryAdData
	logger.InfoCtx(ctx, "[InteractionStrategy] Receive Data:%+v", dataRecall)
	var strategyConf dto.InteractionStrategy
	for _, isl := range memData.InteractionStrategyList {
		if isl.InteractionId == dataRecall.StrategyId {
			strategyConf = isl
		}
	}
	if strategyConf.InteractionId == "" {
		logger.ErrorCtx(ctx, "[InteractionStrategy] receive not found strategyId:%v, ignore", dataRecall.StrategyId)
		return
	}
	if dataRecall.Sensitivity != dto.SensitivityLevelMiddle {
		logger.DebugCtx(ctx, "[InteractionStrategy] receive not care data :%+v", dataRecall)
		return
	}
	if dataRecall.Type == "exposal" {
		ReceiveMiddleExposure(ctx, dataRecall, strategyConf)
	} else if dataRecall.Type == "click" { //fixme 时序的问题如何解决？两个应该是同时接受的？
		ReceiveMiddleClick(ctx, dataRecall, strategyConf)
	} else {
		logger.ErrorCtx(ctx, "[InteractionStrategy] receive get unknown type:%v", dataRecall.Type)
	}

}

// ReceiveMiddleClick 接受点击数据，点击数据不需要延时，直接添加
func ReceiveMiddleClick(ctx context.Context, dataRecall dto.InteractionStrategySend, strategyConf dto.InteractionStrategy) {
	rdsCli := config.TrackRedisClient
	uk := dto.StrategyUk{
		StrategyId: dataRecall.StrategyId,
		TagId:      dataRecall.TagId,
	}
	dataRecallStr, _ := sonic.MarshalString(dataRecall)
	err := rdsCli.HSet(ctx, GetITSTRdsKey(RdsKeyReceivedMiddleClickedRecallDataHash, uk), dataRecall.RequestId, dataRecallStr).Err()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] ReceiveMiddleClick get err:%v", err)
	}
}

func receiveExposureDelayX(ctx context.Context, uk dto.StrategyUk, dataRecall dto.InteractionStrategySend) []dto.InteractionStrategySend {
	curTs := time.Now().Unix()

	// 获取写锁
	localMiddleDelayXTsGpMutex.Lock()

	// 初始化 map
	if _, ok := localMiddleDelayXTsGp[uk]; !ok {
		localMiddleDelayXTsGp[uk] = make(map[int64][]dto.InteractionStrategySend)
	}

	// 添加新数据
	localMiddleDelayXTsGp[uk][dataRecall.ExposureTimestamp] = append(localMiddleDelayXTsGp[uk][dataRecall.ExposureTimestamp], dataRecall)

	// 需要移除延时队列的数据
	rmTsGp := []int64{}
	needInsert2Data := []dto.InteractionStrategySend{}
	localTsGpTmp := localMiddleDelayXTsGp[uk]

	// 处理过期数据
	for ts, datas := range localTsGpTmp {
		if ts <= curTs-int64(exposureIntervalSec) {
			needInsert2Data = append(needInsert2Data, datas...)
			rmTsGp = append(rmTsGp, ts)
		}
	}

	// 删除过期数据
	for _, rmTs := range rmTsGp {
		delete(localTsGpTmp, rmTs)
	}

	// 释放写锁
	localMiddleDelayXTsGpMutex.Unlock()

	return needInsert2Data
}

// ReceiveMiddleExposure
// fixme clickData 的数据依赖于 exposure 数据删除，如果收到 clickData，没有收到 exposure 会存在数据泄露
// fixme 修改为接受100个数据后再处理，批量处理的话最好有单机锁
func ReceiveMiddleExposure(ctx context.Context, dataRecall dto.InteractionStrategySend, strategyConf dto.InteractionStrategy) {
	rdsCli := config.TrackRedisClient
	uk := dto.StrategyUk{
		StrategyId: dataRecall.StrategyId,
		TagId:      dataRecall.TagId,
	}

	// 检查是否需生成新的曝光预期值，获取失败可以等待 rtb 中再次获取
	workDataLen, err := getWorkDataLen(ctx, uk)
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] getWorkDataLen get err:%v", err)
		return
	} else if workDataLen < uint64(float64(batchSize)*float64(0.001)) {
		err = GenNewBatchWorkData(ctx, uk, strategyConf)
		if err != nil {
			logger.ErrorCtx(ctx, "[InteractionStrategy] GenNewBatchWorkData get err:%v", err)
		}
	}

	// 本地实现延时队列，符合条件后再插入到 redis 中
	needInsert2Data := receiveExposureDelayX(ctx, uk, dataRecall)
	exposureRecallListKey := GetITSTRdsKey(RdsKeyReceivedMiddleExposureRecallDataList, uk)
	clickRecallHashKey := GetITSTRdsKey(RdsKeyReceivedMiddleClickedRecallDataHash, uk)
	if len(needInsert2Data) == 0 {
		return
	}

	needInsert2DataDtos := []interface{}{}
	for _, data := range needInsert2Data {
		dataStr, _ := sonic.MarshalString(data)
		needInsert2DataDtos = append(needInsert2DataDtos, dataStr)
	}
	err = rdsCli.LPush(ctx, exposureRecallListKey, needInsert2DataDtos...).Err()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] push exposure recall data strategyId:%v, ignore", dataRecall.StrategyId)
		return
	}

	staSize, err := rdsCli.LLen(ctx, exposureRecallListKey).Uint64()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] received not found strategyId:%v, ignore", dataRecall.StrategyId)
		return
	}
	if staSize < uint64(batchSize) {
		logger.InfoCtx(ctx, "[InteractionStrategy] Received end staSize too little:uk:%+v:%v", uk, staSize)
		return
	}

	exposureDataProcessRdsKey := GetITSTRdsKey("interaction_strategy_exposure_data_process_%s_%s", uk)
	lockErr := config.RedisLock(ctx, exposureDataProcessRdsKey, time.Minute)
	if lockErr != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] interaction_strategy_exposure_data_process lock err:%v", lockErr)
		return
	}

	// unlock
	defer func() {
		uLockErr := config.RedisUnLock(ctx, exposureDataProcessRdsKey)
		if uLockErr != nil {
			logger.AlertCtx(ctx, "[InteractionStrategy] exposure_data_process", "[InteractionStrategy] interaction_strategy_exposure_data_process unlock err:%v", lockErr)
		}
	}()
	// 二次判断
	staSize, err = rdsCli.LLen(ctx, exposureRecallListKey).Uint64()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] received not found strategyId:%v, ignore", dataRecall.StrategyId)
		return
	}
	if staSize < uint64(batchSize) {
		logger.InfoCtx(ctx, "[InteractionStrategy] Received end staSize too little: uk:%+v,  %v", uk, staSize)
		return
	}

	clickedByExpectRatioGp := make(map[string]int)
	exposureByExpectRatioGp := make(map[string]int)
	clickDataReqIdMp := make(map[string]int)

	pipeline := rdsCli.Pipeline()
	for i := 0; i < batchSize; i++ {
		pipeline.RPop(ctx, exposureRecallListKey)
	}
	poppedItems, err := pipeline.Exec(ctx)
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] pop exposure recall   exec error:%v", err)
		return
	}
	recallExposureDatas := []dto.InteractionStrategySend{}
	for _, item := range poppedItems {
		val, err := item.(*redis.StringCmd).Result()
		if err != nil {
			logger.ErrorCtx(ctx, "[InteractionStrategy] pop exposure recall   exec error:%v", err)
			return
		}
		var recallData dto.InteractionStrategySend
		err = sonic.UnmarshalString(val, &recallData)
		if err != nil {
			logger.ErrorCtx(ctx, "[InteractionStrategy] unmarshallString InteractionStrategySend get err:%v, ignore", err)
			continue
		}
		recallExposureDatas = append(recallExposureDatas, recallData)
	}

	if len(recallExposureDatas) < int(float64(batchSize)*(0.9)) {
		logger.AlertCtx(ctx, "[InteractionStrategy]Recall Exposure", "[InteractionStrategy] recallData too little skip :%v, ignore", len(recallExposureDatas))
		return
	}
	// 取得曝光数据
	recallRequestIds := []string{}
	for _, recallData := range recallExposureDatas {
		recallRequestIds = append(recallRequestIds, recallData.RequestId)
	}
	clickDataStrs, err := rdsCli.HMGet(ctx, clickRecallHashKey, recallRequestIds...).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.ErrorCtx(ctx, "[InteractionStrategy] received not found click data strategyId:%+v, ignore:%v", uk, err)
		return
	}
	for _, clickDataTmp := range clickDataStrs {
		clickDataStr, ok := clickDataTmp.(string)
		if !ok {
			continue
		}
		var clickData dto.InteractionStrategySend
		err = sonic.UnmarshalString(clickDataStr, &clickData)
		if err != nil {
			logger.ErrorCtx(ctx, "[InteractionStrategy] ReceiveMiddleExposure get bad clickData UnmarshalString :%v", clickDataTmp)
			continue
		}
		clickDataReqIdMp[clickData.RequestId] = 1
	}
	err = rdsCli.HDel(ctx, clickRecallHashKey, recallRequestIds...).Err()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] received not found hdel strategyId:%v, ignore")
	}

	for _, recallExposureData := range recallExposureDatas {
		k := cast.ToString(recallExposureData.ExpectClickRatio)
		exposureByExpectRatioGp[k] = exposureByExpectRatioGp[k] + 1
		// 存放到有序的列表中
		_, ok := clickDataReqIdMp[recallExposureData.RequestId]
		if ok {
			clickedByExpectRatioGp[k] = clickedByExpectRatioGp[k] + 1
		}
	}

	actualClickRatio := 0.0
	for k, _ := range exposureByExpectRatioGp {
		expCnt := exposureByExpectRatioGp[k]
		clkCnt := clickedByExpectRatioGp[k]
		if expCnt == 0 { // 实际上不会存在
			continue
		}
		actualClickRatio = float64(clkCnt) / float64(expCnt)
		logger.InfoCtx(ctx, "[InteractionStrategy] received hasClick:uk:%+v, %v, exposure:%v, actualClickRatio:%v, expectClickRatio:%v",
			uk, clkCnt, expCnt, actualClickRatio, cast.ToFloat64(k))
		FixMiddleClickRatio(ctx, ReceiveBatchClickRatio{
			Uk: dto.StrategyUk{
				dataRecall.StrategyId,
				dataRecall.TagId,
			},
			ActualClickRatio: actualClickRatio,
			ExpectClickRatio: cast.ToFloat64(k),
			ExposureCnt:      expCnt,
		}, strategyConf)
	}
}

type ReceiveBatchClickRatio struct {
	Uk               dto.StrategyUk
	ActualClickRatio float64
	ExpectClickRatio float64
	ExposureCnt      int
}

// FixMiddleClickRatio 添加前面已经处理的点击率数据
// fixme 迁移数据存储到 dao 中，没有获取到则默认为空数组
// fixme 可以考虑优化为带上 count
func FixMiddleClickRatio(ctx context.Context, preClickRatio ReceiveBatchClickRatio, strategyConf dto.InteractionStrategy) {
	if preClickRatio.ExposureCnt < int(float64(batchSize)*float64(0.4)) { // 批次数据中的数据较少，不采用
		logger.InfoCtx(ctx, "[InteractionStrategy] get append is too little ignore uk:%+v :%+v", preClickRatio.Uk, preClickRatio)
		return
	}
	var err error
	err = FixMiddleStrategySensitivity(ctx, preClickRatio.Uk, preClickRatio, strategyConf)
	if err != nil {
		logger.ErrorCtx(ctx, "[FixMiddleStrategySensitivity] err:%v", err)
	}
}

// GenNewBatchWorkData 计算时异步的，收到多少条数据就触发，而不是等待主线流程执行
// 生成器，时间限制不严格，失败需要告警，进入的条件是曝光数量已经符合触发下发数据生成： 可使用（曝光时间大于当前10s）的曝光数量大于 1w条
func GenNewBatchWorkData(ctx context.Context, uk dto.StrategyUk, strategyConf dto.InteractionStrategy) error {
	rdsCli := config.TrackRedisClient
	lockKey := GetITSTRdsKey("GenNewBatchWorkData_lock_%s_%s", uk)
	err := config.RedisLock(ctx, lockKey, time.Second*10)
	if err != nil {
		logger.InfoCtx(ctx, "[InteractionStrategy] GenNewBatchWorkData lock err:%v", err)
		return nil
	}
	defer config.RedisUnLock(ctx, lockKey)

	clickRatioM := GetExpectClickRatioOrDefault(ctx, uk, strategyConf)

	strategyId := uk.StrategyId
	// 有待下发的数据，就取出来，没有就重新计算再放进去
	curBatchExpectClickRatio := calCurBatchExpectClickRatio(ctx, uk, strategyConf)
	cntL, cntM, cntH := calCurBatchCnts(ctx, clickRatioM, curBatchExpectClickRatio)

	// 计算某一批次的数据，时序问题，多个机器一致性的问题，使用redis，分发当前批次的数据：
	sendDatas := []dto.InteractionStrategySend{}
	// 没有获取到数据则不进行等待，发送
	sensitivityNumMiddle := GetNearySenseNumByClickRatio(ctx, getInitClickRatio(ctx, uk, strategyConf), strategyConf) // 灵敏度目前为固定的初始值
	idx2SensitivityNum := []int{SensitivityNumLow, sensitivityNumMiddle, SensitivityNumHigh}
	idx2SensitivityLevel := []dto.SensitivityLevel{dto.SensitivityLevelLow, dto.SensitivityLevelMiddle, dto.SensitivityLevelHigh}
	idx2ClickRatio := []float64{ClickRatioLow, clickRatioM, ClickRatioHigh}

	err = rdsCli.Set(ctx, GetITSTRdsKey(RdsKeyMiddleCurExpectClickRatio, uk), clickRatioM, longTimeTtl).Err()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] GenNewBatchWorkData expect click ratio set uk:%v, click ratio:%v", uk, clickRatioM)
	}

	batch := fmt.Sprintf("gen_%s", time.Now().Format("20060102_1504"))
	for idx, cnt := range []int{cntL, cntM, cntH} {
		one := dto.InteractionStrategySend{
			StrategyId:        strategyId,
			Sensitivity:       idx2SensitivityLevel[idx],
			SensitivityNum:    idx2SensitivityNum[idx],
			ExpectClickRatio:  idx2ClickRatio[idx],
			Batch:             batch,
			ExposureTimestamp: 0, // 后面实际下发时进行填充
			RequestId:         "",
			TagId:             uk.TagId,
			Cnt:               int64(cnt),
		}
		for i := 0; i < cnt; i++ {
			sendDatas = append(sendDatas, one)
		}
		logger.InfoCtx(ctx, "[InteractionStrategy] gen batch Data:%v", one)
	}

	pushWorkDatas(ctx, uk, sendDatas)
	return nil
}

// pushWorkDatas 保存数据到队列中
func pushWorkDatas(ctx context.Context, uk dto.StrategyUk, datas []dto.InteractionStrategySend) {
	rdsCli := config.TrackRedisClient
	dataItfs := make([]interface{}, 0)
	for _, d := range datas {
		s, _ := sonic.MarshalString(d)
		dataItfs = append(dataItfs, s)
	}
	result := rdsCli.LPush(ctx, fmt.Sprintf(RdsKeyWaitSendData, uk.StrategyId, uk.TagId), dataItfs...)
	err := result.Err()
	if err != nil {
		logger.AlertCtx(ctx, "[InteractionStrategy] pushWorkDatas", "[InteractionStrategy] pushWorkDatas err :%v", err)
	}
}

// getWorkDataLen 获取长度
func getWorkDataLen(ctx context.Context, uk dto.StrategyUk) (uint64, error) {
	rdsCli := config.TrackRedisClient
	l, err := rdsCli.LLen(ctx, GetITSTRdsKey(RdsKeyWaitSendData, uk)).Uint64()
	if err != nil {
		logger.ErrorCtx(ctx, "[InteractionStrategy] getWorkDataLen", "[InteractionStrategy] pushWorkDatas err :%v", err)
	}
	return l, err
}

// GetExpectClickRatioOrDefault , ！！！不能去掉其中设置初始化点击率的逻辑
// fixme 重新设置是需要失效掉频次等数据，等待处理的频次数据需要清空
// fixme 失效掉的预期点击率数据后，功能是否能正常调整？
func GetExpectClickRatioOrDefault(ctx context.Context, uk dto.StrategyUk, strategyConf dto.InteractionStrategy) float64 {
	rdsCli := config.TrackRedisClient
	var err error
	found, f := GetExpectClickRatio(ctx, uk, strategyConf)
	if !found {
		initClickRatio := getInitClickRatio(ctx, uk, strategyConf)
		err = rdsCli.Set(ctx, GetITSTRdsKey(RdsKeyMiddleCurExpectClickRatio, uk),
			initClickRatio, longTimeTtl).Err()
		if err != nil {
			logger.ErrorCtx(ctx, "[InteractionStrategy] getCurMiddleClickRatioOrDefault Set err:%v", err)
		}
		return initClickRatio
	}
	return f
}

func GetExpectClickRatio(ctx context.Context, uk dto.StrategyUk, strategyConf dto.InteractionStrategy) (bool, float64) {
	rdsCli := config.TrackRedisClient
	f, err := rdsCli.Get(ctx, GetITSTRdsKey(RdsKeyMiddleCurExpectClickRatio, uk)).Float64()
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.ErrorCtx(ctx, "[InteractionStrategy] getCurMiddleClickRatioOrDefault err:%v", err)
	} else if errors.Is(err, redis.Nil) {
		return false, f
	}
	return true, f
}

func getSensitivityNumDefault(ctx context.Context, uk dto.StrategyUk, strategyConf dto.InteractionStrategy) int {
	return GetNearySenseNumByClickRatio(ctx, getInitClickRatio(ctx, uk, strategyConf), strategyConf)
}

func getInitClickRatio(ctx context.Context, uk dto.StrategyUk, strategyConf dto.InteractionStrategy) float64 {
	clickRatio := strategyConf.TotalExpectClickRatio
	for _, tp := range strategyConf.TagIdExpectClickRatioTuple {
		if tp.TagId == uk.TagId {
			clickRatio = tp.ExpectClickRatio
		}
	}
	return clickRatio
}

func calCurBatchCnts(ctx context.Context, clickRatioM, curBatchExpectClickRatio float64) (int, int, int) {
	var cntL, cntM, cntH int
	ClickRatioH := ClickRatioHigh
	ClickRatioL := ClickRatioLow

	CntFix := 0
	LowOrHigh := false // false low, true high
	// 决定使用 那个档位
	if math.Abs(curBatchExpectClickRatio-clickRatioM) < FixStrategyStep {
		cntM = batchSize
	} else if curBatchExpectClickRatio < clickRatioM || curBatchExpectClickRatio > clickRatioM { // 使用 L M
		ClickRatio4Fix := ClickRatioL
		if curBatchExpectClickRatio < clickRatioM {
			ClickRatio4Fix = ClickRatioL
			LowOrHigh = false
		} else {
			ClickRatio4Fix = ClickRatioH
			LowOrHigh = true
		}
		cntM = int((float64(batchSize)*curBatchExpectClickRatio - float64(batchSize)*ClickRatio4Fix) / (clickRatioM - ClickRatio4Fix))
		if cntM < 0 {
			cntM = 0
			logger.ErrorCtx(ctx, "[InteractionStrategy] Distribute recal cntM < 0:%v, windowsSize :%v, expectClickRatio:%v ,"+
				"ClickRatioL:%v , clickRatioM:%v ", cntM, batchSize, curBatchExpectClickRatio, ClickRatioL, clickRatioM)
		} else if cntM > batchSize {
			cntM = batchSize
			logger.ErrorCtx(ctx, "[InteractionStrategy] Distribute recal cntM overflow:%v, windowsSize :%v, expectClickRatio:%v ,"+
				"ClickRatioL:%v , clickRatioM:%v ", cntM, batchSize, curBatchExpectClickRatio, ClickRatioL, clickRatioM)
		}
		logger.InfoCtx(ctx, "[InteractionStrategy] Distribute recal cntM res:%v, windowsSize :%v, expectClickRatio:%v ,"+
			"ClickRatioL:%v , clickRatioM:%v ", cntM, batchSize, curBatchExpectClickRatio, ClickRatioL, clickRatioM)

		CntFix = batchSize - cntM
	}
	if !LowOrHigh {
		cntL = CntFix
	} else {
		cntH = CntFix
	}
	// cntM 低于批次的二分之一进行告警
	if cntM < (batchSize / 2) {
		logger.ErrorCtx(ctx, "[InteractionStrategy] Distribute recal cntM too low res:%v, windowsSize :%v, expectClickRatio:%v ,"+
			"ClickRatioL:%v , clickRatioM:%v ", cntM, batchSize, curBatchExpectClickRatio, ClickRatioL, clickRatioM)
	}

	return cntL, cntM, cntH
}

// GetNearySenseNumByClickRatio 添加拟合曲线的函数，安卓和ios不一样
func GetNearySenseNumByClickRatio(ctx context.Context, expectClickRatio float64, conf dto.InteractionStrategy) int {
	var res int
	expectClickRatio *= 100
	if conf.Os == int(dto.OsTypeIos) {
		if expectClickRatio < 1.39 {
			res = 500
		} else if expectClickRatio >= 1.39 && expectClickRatio <= 9.86 {
			res = int(float64(860-810)/(9.86-1.39)*(expectClickRatio-1.39) + 810)
		} else if expectClickRatio >= 9.86 && expectClickRatio <= 20.94 {
			res = int(float64(880-860)/(20.94-9.86)*(expectClickRatio-9.86) + 860)
		} else if expectClickRatio >= 20.94 && expectClickRatio <= 38.38 {
			res = int(float64(898-880)/(38.38-20.94)*(expectClickRatio-20.94) + 880)
		} else if expectClickRatio >= 38.38 && expectClickRatio <= 53.17 {
			res = int(float64(910-898)/(53.17-38.38)*(expectClickRatio-38.38) + 898)
		} else if expectClickRatio >= 53.17 && expectClickRatio <= 95 {
			res = int(float64(950-910)/(95-53.17)*(expectClickRatio-53.17) + 910)
		} else if expectClickRatio >= 95 {
			res = 1000
		}
	} else {
		if expectClickRatio < 1.04 {
			res = 500
		} else if expectClickRatio >= 1.04 && expectClickRatio <= 9.02 {
			res = int(float64(860-810)/(9.02-1.04)*(expectClickRatio-1.04) + 810)
		} else if expectClickRatio >= 9.02 && expectClickRatio <= 20.90 {
			res = int(float64(880-860)/(20.90-9.02)*(expectClickRatio-9.02) + 860)
		} else if expectClickRatio >= 20.90 && expectClickRatio <= 39.09 {
			res = int(float64(898-880)/(39.09-20.90)*(expectClickRatio-20.90) + 880)
		} else if expectClickRatio >= 39.09 && expectClickRatio <= 53.17 {
			res = int(float64(910-898)/(53.17-39.09)*(expectClickRatio-39.09) + 898)
		} else if expectClickRatio >= 53.17 && expectClickRatio <= 95 {
			res = int(float64(950-910)/(95.0-53.17)*(expectClickRatio-53.17) + 910)
		} else if expectClickRatio >= 95 {
			res = 1000
		}
	}

	return res
}
