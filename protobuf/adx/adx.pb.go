// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.29.3
// source: adx.proto

package go_micro_service_adx

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type STATUS int32

const (
	STATUS_OK STATUS = 0 //成功
)

// Enum value maps for STATUS.
var (
	STATUS_name = map[int32]string{
		0: "OK",
	}
	STATUS_value = map[string]int32{
		"OK": 0,
	}
)

func (x STATUS) Enum() *STATUS {
	p := new(STATUS)
	*p = x
	return p
}

func (x STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_adx_proto_enumTypes[0].Descriptor()
}

func (STATUS) Type() protoreflect.EnumType {
	return &file_adx_proto_enumTypes[0]
}

func (x STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use STATUS.Descriptor instead.
func (STATUS) EnumDescriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{0}
}

type ResponseStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code STATUS `protobuf:"varint,1,opt,name=code,proto3,enum=go.micro.service.adx.STATUS" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ResponseStatus) Reset() {
	*x = ResponseStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseStatus) ProtoMessage() {}

func (x *ResponseStatus) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseStatus.ProtoReflect.Descriptor instead.
func (*ResponseStatus) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{0}
}

func (x *ResponseStatus) GetCode() STATUS {
	if x != nil {
		return x.Code
	}
	return STATUS_OK
}

func (x *ResponseStatus) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type InnerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source           string `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	VersionCode      int64  `protobuf:"varint,2,opt,name=versionCode,proto3" json:"versionCode,omitempty"`
	DeviceID         string `protobuf:"bytes,3,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	VersionCodeQuote string `protobuf:"bytes,4,opt,name=versionCodeQuote,proto3" json:"versionCodeQuote,omitempty"`
	UserID           string `protobuf:"bytes,5,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *InnerData) Reset() {
	*x = InnerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InnerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerData) ProtoMessage() {}

func (x *InnerData) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerData.ProtoReflect.Descriptor instead.
func (*InnerData) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{1}
}

func (x *InnerData) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *InnerData) GetVersionCode() int64 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *InnerData) GetDeviceID() string {
	if x != nil {
		return x.DeviceID
	}
	return ""
}

func (x *InnerData) GetVersionCodeQuote() string {
	if x != nil {
		return x.VersionCodeQuote
	}
	return ""
}

func (x *InnerData) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Imp          []*BidRequest_Imp  `protobuf:"bytes,2,rep,name=imp,proto3" json:"imp,omitempty"`
	App          *BidRequest_App    `protobuf:"bytes,3,opt,name=app,proto3" json:"app,omitempty"`
	Device       *BidRequest_Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	User         *BidRequest_User   `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	ApiVersion   string             `protobuf:"bytes,6,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	InstalledApp []string           `protobuf:"bytes,7,rep,name=installedApp,proto3" json:"installedApp,omitempty"`
	Inner        *InnerData         `protobuf:"bytes,8,opt,name=inner,proto3" json:"inner,omitempty"`
	Appendix01   []int64            `protobuf:"varint,9,rep,packed,name=appendix01,proto3" json:"appendix01,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *BidRequest) GetInner() *InnerData {
	if x != nil {
		return x.Inner
	}
	return nil
}

func (x *BidRequest) GetAppendix01() []int64 {
	if x != nil {
		return x.Appendix01
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string                 `protobuf:"bytes,65,opt,name=id,proto3" json:"id,omitempty"`
	Bidid   string                 `protobuf:"bytes,66,opt,name=bidid,proto3" json:"bidid,omitempty"`
	Seatbid []*BidResponse_SeatBid `protobuf:"bytes,67,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

type CaidWithFactors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Factors         *CaidFactors `protobuf:"bytes,1,opt,name=factors,proto3" json:"factors,omitempty"`
	Caid            string       `protobuf:"bytes,2,opt,name=caid,proto3" json:"caid,omitempty"`
	CaidVersion     string       `protobuf:"bytes,3,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	CaidGenTime     string       `protobuf:"bytes,4,opt,name=caidGenTime,proto3" json:"caidGenTime,omitempty"`
	CaidLast        string       `protobuf:"bytes,5,opt,name=caidLast,proto3" json:"caidLast,omitempty"`
	CaidVersionLast string       `protobuf:"bytes,6,opt,name=caidVersionLast,proto3" json:"caidVersionLast,omitempty"`
}

func (x *CaidWithFactors) Reset() {
	*x = CaidWithFactors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaidWithFactors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaidWithFactors) ProtoMessage() {}

func (x *CaidWithFactors) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaidWithFactors.ProtoReflect.Descriptor instead.
func (*CaidWithFactors) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{4}
}

func (x *CaidWithFactors) GetFactors() *CaidFactors {
	if x != nil {
		return x.Factors
	}
	return nil
}

func (x *CaidWithFactors) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *CaidWithFactors) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *CaidWithFactors) GetCaidGenTime() string {
	if x != nil {
		return x.CaidGenTime
	}
	return ""
}

func (x *CaidWithFactors) GetCaidLast() string {
	if x != nil {
		return x.CaidLast
	}
	return ""
}

func (x *CaidWithFactors) GetCaidVersionLast() string {
	if x != nil {
		return x.CaidVersionLast
	}
	return ""
}

type Mixcfw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fs *Fs    `protobuf:"bytes,1,opt,name=fs,proto3" json:"fs,omitempty"`
	C1 string `protobuf:"bytes,2,opt,name=c1,proto3" json:"c1,omitempty"` // caid
	C2 string `protobuf:"bytes,3,opt,name=c2,proto3" json:"c2,omitempty"` // caidLast
	C3 string `protobuf:"bytes,4,opt,name=c3,proto3" json:"c3,omitempty"` // caidVersion
	C4 string `protobuf:"bytes,5,opt,name=c4,proto3" json:"c4,omitempty"` // caidVersionLast
	C5 string `protobuf:"bytes,6,opt,name=c5,proto3" json:"c5,omitempty"` // caidGenTime
}

func (x *Mixcfw) Reset() {
	*x = Mixcfw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mixcfw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mixcfw) ProtoMessage() {}

func (x *Mixcfw) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mixcfw.ProtoReflect.Descriptor instead.
func (*Mixcfw) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{5}
}

func (x *Mixcfw) GetFs() *Fs {
	if x != nil {
		return x.Fs
	}
	return nil
}

func (x *Mixcfw) GetC1() string {
	if x != nil {
		return x.C1
	}
	return ""
}

func (x *Mixcfw) GetC2() string {
	if x != nil {
		return x.C2
	}
	return ""
}

func (x *Mixcfw) GetC3() string {
	if x != nil {
		return x.C3
	}
	return ""
}

func (x *Mixcfw) GetC4() string {
	if x != nil {
		return x.C4
	}
	return ""
}

func (x *Mixcfw) GetC5() string {
	if x != nil {
		return x.C5
	}
	return ""
}

type Fs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	F1  string `protobuf:"bytes,1,opt,name=f1,proto3" json:"f1,omitempty"`    // carrierInfo,运营商 示例值: "中国移动"
	F2  string `protobuf:"bytes,2,opt,name=f2,proto3" json:"f2,omitempty"`    // machine,设备Machine 示例值: "iPhone10,3"
	F3  string `protobuf:"bytes,3,opt,name=f3,proto3" json:"f3,omitempty"`    // sysFileTime,系统更新时间 示例值: "1595214620.383940"
	F4  string `protobuf:"bytes,4,opt,name=f4,proto3" json:"f4,omitempty"`    // countryCode,国家 示例值: "CN"
	F5  string `protobuf:"bytes,5,opt,name=f5,proto3" json:"f5,omitempty"`    // deviceName,设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
	F6  string `protobuf:"bytes,6,opt,name=f6,proto3" json:"f6,omitempty"`    // timeZone,时区 示例值: "28800"
	F7  string `protobuf:"bytes,7,opt,name=f7,proto3" json:"f7,omitempty"`    // memory,物理内存容量 示例值: "3955589120"
	F8  string `protobuf:"bytes,8,opt,name=f8,proto3" json:"f8,omitempty"`    // disk,硬盘容量 示例值: "63900340224"
	F9  string `protobuf:"bytes,9,opt,name=f9,proto3" json:"f9,omitempty"`    // language,语言 示例值: "zh-Hans-CN"
	F10 string `protobuf:"bytes,10,opt,name=f10,proto3" json:"f10,omitempty"` // systemVersion,系统版本 示例值: "14.0"
	F11 string `protobuf:"bytes,11,opt,name=f11,proto3" json:"f11,omitempty"` // bootTimeInSec,设备启动时间（秒） 示例值: "1595643553"
	F12 string `protobuf:"bytes,12,opt,name=f12,proto3" json:"f12,omitempty"` // model,设备Model 示例值: "D22AP"
	F13 string `protobuf:"bytes,13,opt,name=f13,proto3" json:"f13,omitempty"` // mntId,挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
	F14 string `protobuf:"bytes,14,opt,name=f14,proto3" json:"f14,omitempty"` // deviceInitTime,设备初始化时间 示例值: "1632467920.301150749"
}

func (x *Fs) Reset() {
	*x = Fs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fs) ProtoMessage() {}

func (x *Fs) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fs.ProtoReflect.Descriptor instead.
func (*Fs) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{6}
}

func (x *Fs) GetF1() string {
	if x != nil {
		return x.F1
	}
	return ""
}

func (x *Fs) GetF2() string {
	if x != nil {
		return x.F2
	}
	return ""
}

func (x *Fs) GetF3() string {
	if x != nil {
		return x.F3
	}
	return ""
}

func (x *Fs) GetF4() string {
	if x != nil {
		return x.F4
	}
	return ""
}

func (x *Fs) GetF5() string {
	if x != nil {
		return x.F5
	}
	return ""
}

func (x *Fs) GetF6() string {
	if x != nil {
		return x.F6
	}
	return ""
}

func (x *Fs) GetF7() string {
	if x != nil {
		return x.F7
	}
	return ""
}

func (x *Fs) GetF8() string {
	if x != nil {
		return x.F8
	}
	return ""
}

func (x *Fs) GetF9() string {
	if x != nil {
		return x.F9
	}
	return ""
}

func (x *Fs) GetF10() string {
	if x != nil {
		return x.F10
	}
	return ""
}

func (x *Fs) GetF11() string {
	if x != nil {
		return x.F11
	}
	return ""
}

func (x *Fs) GetF12() string {
	if x != nil {
		return x.F12
	}
	return ""
}

func (x *Fs) GetF13() string {
	if x != nil {
		return x.F13
	}
	return ""
}

func (x *Fs) GetF14() string {
	if x != nil {
		return x.F14
	}
	return ""
}

type PromotionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivationType    string  `protobuf:"bytes,1,opt,name=activationType,proto3" json:"activationType,omitempty"`
	UtmSource         string  `protobuf:"bytes,2,opt,name=utmSource,proto3" json:"utmSource,omitempty"`
	ActivationChannel string  `protobuf:"bytes,3,opt,name=activationChannel,proto3" json:"activationChannel,omitempty"`
	AccountId         string  `protobuf:"bytes,4,opt,name=accountId,proto3" json:"accountId,omitempty"`
	ActivateInterval  float32 `protobuf:"fixed32,5,opt,name=activateInterval,proto3" json:"activateInterval,omitempty"`
}

func (x *PromotionInfo) Reset() {
	*x = PromotionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromotionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromotionInfo) ProtoMessage() {}

func (x *PromotionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromotionInfo.ProtoReflect.Descriptor instead.
func (*PromotionInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{7}
}

func (x *PromotionInfo) GetActivationType() string {
	if x != nil {
		return x.ActivationType
	}
	return ""
}

func (x *PromotionInfo) GetUtmSource() string {
	if x != nil {
		return x.UtmSource
	}
	return ""
}

func (x *PromotionInfo) GetActivationChannel() string {
	if x != nil {
		return x.ActivationChannel
	}
	return ""
}

func (x *PromotionInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PromotionInfo) GetActivateInterval() float32 {
	if x != nil {
		return x.ActivateInterval
	}
	return 0
}

type NegativeFeedback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdComplain  []string `protobuf:"bytes,1,rep,name=adComplain,proto3" json:"adComplain,omitempty"`
	ShakeForbid string   `protobuf:"bytes,2,opt,name=shakeForbid,proto3" json:"shakeForbid,omitempty"`
}

func (x *NegativeFeedback) Reset() {
	*x = NegativeFeedback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NegativeFeedback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NegativeFeedback) ProtoMessage() {}

func (x *NegativeFeedback) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NegativeFeedback.ProtoReflect.Descriptor instead.
func (*NegativeFeedback) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{8}
}

func (x *NegativeFeedback) GetAdComplain() []string {
	if x != nil {
		return x.AdComplain
	}
	return nil
}

func (x *NegativeFeedback) GetShakeForbid() string {
	if x != nil {
		return x.ShakeForbid
	}
	return ""
}

type BidRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Imp          []*BidRequestV2_Imp  `protobuf:"bytes,2,rep,name=imp,proto3" json:"imp,omitempty"`
	App          *BidRequestV2_App    `protobuf:"bytes,3,opt,name=app,proto3" json:"app,omitempty"`
	Device       *BidRequestV2_Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	User         *BidRequestV2_User   `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	ApiVersion   string               `protobuf:"bytes,6,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	InstalledApp []string             `protobuf:"bytes,7,rep,name=installedApp,proto3" json:"installedApp,omitempty"`
	Tag          *BidRequestV2_Tag    `protobuf:"bytes,8,opt,name=tag,proto3" json:"tag,omitempty"`
	InnerData    *InnerData           `protobuf:"bytes,9,opt,name=innerData,proto3" json:"innerData,omitempty"`
	Ext          *BidRequestV2_Ext    `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidRequestV2) Reset() {
	*x = BidRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2) ProtoMessage() {}

func (x *BidRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2.ProtoReflect.Descriptor instead.
func (*BidRequestV2) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9}
}

func (x *BidRequestV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequestV2) GetImp() []*BidRequestV2_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequestV2) GetApp() *BidRequestV2_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequestV2) GetDevice() *BidRequestV2_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequestV2) GetUser() *BidRequestV2_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequestV2) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequestV2) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *BidRequestV2) GetTag() *BidRequestV2_Tag {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *BidRequestV2) GetInnerData() *InnerData {
	if x != nil {
		return x.InnerData
	}
	return nil
}

func (x *BidRequestV2) GetExt() *BidRequestV2_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type SDKInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SDKInfo) Reset() {
	*x = SDKInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SDKInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SDKInfo) ProtoMessage() {}

func (x *SDKInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SDKInfo.ProtoReflect.Descriptor instead.
func (*SDKInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{10}
}

func (x *SDKInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SDKInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type BidResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string                   `protobuf:"bytes,65,opt,name=id,proto3" json:"id,omitempty"`
	BidID   string                   `protobuf:"bytes,66,opt,name=bidID,proto3" json:"bidID,omitempty"`
	SeatBid []*BidResponseV2_SeatBid `protobuf:"bytes,67,rep,name=seatBid,proto3" json:"seatBid,omitempty"`
	Ext     *BidResponseV2_Ext       `protobuf:"bytes,78,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidResponseV2) Reset() {
	*x = BidResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2) ProtoMessage() {}

func (x *BidResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2.ProtoReflect.Descriptor instead.
func (*BidResponseV2) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11}
}

func (x *BidResponseV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponseV2) GetBidID() string {
	if x != nil {
		return x.BidID
	}
	return ""
}

func (x *BidResponseV2) GetSeatBid() []*BidResponseV2_SeatBid {
	if x != nil {
		return x.SeatBid
	}
	return nil
}

func (x *BidResponseV2) GetExt() *BidResponseV2_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type SetAppListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId string     `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	UserId   string     `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	AppList  []*AppInfo `protobuf:"bytes,3,rep,name=appList,proto3" json:"appList,omitempty"`
}

func (x *SetAppListRequest) Reset() {
	*x = SetAppListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAppListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAppListRequest) ProtoMessage() {}

func (x *SetAppListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAppListRequest.ProtoReflect.Descriptor instead.
func (*SetAppListRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{12}
}

func (x *SetAppListRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SetAppListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SetAppListRequest) GetAppList() []*AppInfo {
	if x != nil {
		return x.AppList
	}
	return nil
}

type SetAppListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetAppListResponse) Reset() {
	*x = SetAppListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAppListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAppListResponse) ProtoMessage() {}

func (x *SetAppListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAppListResponse.ProtoReflect.Descriptor instead.
func (*SetAppListResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{13}
}

type GetAppListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId string `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	UserId   string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *GetAppListRequest) Reset() {
	*x = GetAppListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppListRequest) ProtoMessage() {}

func (x *GetAppListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppListRequest.ProtoReflect.Descriptor instead.
func (*GetAppListRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{14}
}

func (x *GetAppListRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetAppListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId string `protobuf:"bytes,1,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	SchemaId string `protobuf:"bytes,2,opt,name=schemaId,proto3" json:"schemaId,omitempty"`
	AppId    int64  `protobuf:"varint,3,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{15}
}

func (x *AppInfo) GetBundleId() string {
	if x != nil {
		return x.BundleId
	}
	return ""
}

func (x *AppInfo) GetSchemaId() string {
	if x != nil {
		return x.SchemaId
	}
	return ""
}

func (x *AppInfo) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

type GetAppListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppList []*AppInfo `protobuf:"bytes,1,rep,name=appList,proto3" json:"appList,omitempty"`
}

func (x *GetAppListResponse) Reset() {
	*x = GetAppListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppListResponse) ProtoMessage() {}

func (x *GetAppListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppListResponse.ProtoReflect.Descriptor instead.
func (*GetAppListResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{16}
}

func (x *GetAppListResponse) GetAppList() []*AppInfo {
	if x != nil {
		return x.AppList
	}
	return nil
}

type CaidFactors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BootTimeInSec  string `protobuf:"bytes,1,opt,name=bootTimeInSec,proto3" json:"bootTimeInSec,omitempty"`    // 设备启动时间（秒） 示例值: "1595643553"
	CountryCode    string `protobuf:"bytes,2,opt,name=countryCode,proto3" json:"countryCode,omitempty"`        // 国家 示例值: "CN"
	Language       string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`              // 语言 示例值: "zh-Hans-CN"
	DeviceName     string `protobuf:"bytes,4,opt,name=deviceName,proto3" json:"deviceName,omitempty"`          // 设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
	SystemVersion  string `protobuf:"bytes,5,opt,name=systemVersion,proto3" json:"systemVersion,omitempty"`    // 系统版本 示例值: "14.0"
	Machine        string `protobuf:"bytes,6,opt,name=machine,proto3" json:"machine,omitempty"`                // 设备Machine 示例值: "iPhone10,3"
	CarrierInfo    string `protobuf:"bytes,7,opt,name=carrierInfo,proto3" json:"carrierInfo,omitempty"`        // 运营商 示例值: "中国移动"
	Memory         string `protobuf:"bytes,8,opt,name=memory,proto3" json:"memory,omitempty"`                  // 物理内存容量 示例值: "3955589120"
	Disk           string `protobuf:"bytes,9,opt,name=disk,proto3" json:"disk,omitempty"`                      // 硬盘容量 示例值: "63900340224"
	SysFileTime    string `protobuf:"bytes,10,opt,name=sysFileTime,proto3" json:"sysFileTime,omitempty"`       // 系统更新时间 示例值: "1595214620.383940"
	Model          string `protobuf:"bytes,11,opt,name=model,proto3" json:"model,omitempty"`                   // 设备Model 示例值: "D22AP"
	TimeZone       string `protobuf:"bytes,12,opt,name=timeZone,proto3" json:"timeZone,omitempty"`             // 时区 示例值: "28800"
	MntId          string `protobuf:"bytes,13,opt,name=mntId,proto3" json:"mntId,omitempty"`                   // 挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
	DeviceInitTime string `protobuf:"bytes,14,opt,name=deviceInitTime,proto3" json:"deviceInitTime,omitempty"` // 设备初始化时间 示例值: "1632467920.301150749"
}

func (x *CaidFactors) Reset() {
	*x = CaidFactors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaidFactors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaidFactors) ProtoMessage() {}

func (x *CaidFactors) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaidFactors.ProtoReflect.Descriptor instead.
func (*CaidFactors) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{17}
}

func (x *CaidFactors) GetBootTimeInSec() string {
	if x != nil {
		return x.BootTimeInSec
	}
	return ""
}

func (x *CaidFactors) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *CaidFactors) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CaidFactors) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CaidFactors) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *CaidFactors) GetMachine() string {
	if x != nil {
		return x.Machine
	}
	return ""
}

func (x *CaidFactors) GetCarrierInfo() string {
	if x != nil {
		return x.CarrierInfo
	}
	return ""
}

func (x *CaidFactors) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *CaidFactors) GetDisk() string {
	if x != nil {
		return x.Disk
	}
	return ""
}

func (x *CaidFactors) GetSysFileTime() string {
	if x != nil {
		return x.SysFileTime
	}
	return ""
}

func (x *CaidFactors) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CaidFactors) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *CaidFactors) GetMntId() string {
	if x != nil {
		return x.MntId
	}
	return ""
}

func (x *CaidFactors) GetDeviceInitTime() string {
	if x != nil {
		return x.DeviceInitTime
	}
	return ""
}

type Factor2CaidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaidFactorsSum string       `protobuf:"bytes,1,opt,name=caidFactorsSum,proto3" json:"caidFactorsSum,omitempty"`
	CaidFactors    *CaidFactors `protobuf:"bytes,2,opt,name=caidFactors,proto3" json:"caidFactors,omitempty"`
	DeviceId       string       `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *Factor2CaidRequest) Reset() {
	*x = Factor2CaidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Factor2CaidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Factor2CaidRequest) ProtoMessage() {}

func (x *Factor2CaidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Factor2CaidRequest.ProtoReflect.Descriptor instead.
func (*Factor2CaidRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{18}
}

func (x *Factor2CaidRequest) GetCaidFactorsSum() string {
	if x != nil {
		return x.CaidFactorsSum
	}
	return ""
}

func (x *Factor2CaidRequest) GetCaidFactors() *CaidFactors {
	if x != nil {
		return x.CaidFactors
	}
	return nil
}

func (x *Factor2CaidRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type Factor2CaidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid            string `protobuf:"bytes,1,opt,name=Caid,proto3" json:"Caid,omitempty"`
	CaidVersion     string `protobuf:"bytes,2,opt,name=CaidVersion,proto3" json:"CaidVersion,omitempty"`
	CaidGenTime     string `protobuf:"bytes,3,opt,name=CaidGenTime,proto3" json:"CaidGenTime,omitempty"`
	CaidLast        string `protobuf:"bytes,4,opt,name=CaidLast,proto3" json:"CaidLast,omitempty"`
	CaidVersionLast string `protobuf:"bytes,5,opt,name=CaidVersionLast,proto3" json:"CaidVersionLast,omitempty"`
}

func (x *Factor2CaidResponse) Reset() {
	*x = Factor2CaidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Factor2CaidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Factor2CaidResponse) ProtoMessage() {}

func (x *Factor2CaidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Factor2CaidResponse.ProtoReflect.Descriptor instead.
func (*Factor2CaidResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{19}
}

func (x *Factor2CaidResponse) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *Factor2CaidResponse) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *Factor2CaidResponse) GetCaidGenTime() string {
	if x != nil {
		return x.CaidGenTime
	}
	return ""
}

func (x *Factor2CaidResponse) GetCaidLast() string {
	if x != nil {
		return x.CaidLast
	}
	return ""
}

func (x *Factor2CaidResponse) GetCaidVersionLast() string {
	if x != nil {
		return x.CaidVersionLast
	}
	return ""
}

type AdPosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os               int64                       `protobuf:"varint,1,opt,name=os,proto3" json:"os,omitempty"`
	VersionCode      int64                       `protobuf:"varint,2,opt,name=versionCode,proto3" json:"versionCode,omitempty"`
	DeviceID         string                      `protobuf:"bytes,3,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	PromotionInfo    *AdPosRequest_PromotionInfo `protobuf:"bytes,4,opt,name=promotionInfo,proto3" json:"promotionInfo,omitempty"`
	UserInfo         *AdPosRequest_UserInfo      `protobuf:"bytes,5,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
	NegativeFeedback *NegativeFeedback           `protobuf:"bytes,6,opt,name=negativeFeedback,proto3,oneof" json:"negativeFeedback,omitempty"`
}

func (x *AdPosRequest) Reset() {
	*x = AdPosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosRequest) ProtoMessage() {}

func (x *AdPosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosRequest.ProtoReflect.Descriptor instead.
func (*AdPosRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{20}
}

func (x *AdPosRequest) GetOs() int64 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *AdPosRequest) GetVersionCode() int64 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *AdPosRequest) GetDeviceID() string {
	if x != nil {
		return x.DeviceID
	}
	return ""
}

func (x *AdPosRequest) GetPromotionInfo() *AdPosRequest_PromotionInfo {
	if x != nil {
		return x.PromotionInfo
	}
	return nil
}

func (x *AdPosRequest) GetUserInfo() *AdPosRequest_UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *AdPosRequest) GetNegativeFeedback() *NegativeFeedback {
	if x != nil {
		return x.NegativeFeedback
	}
	return nil
}

type AdPosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdPosList            []*AdPos                          `protobuf:"bytes,1,rep,name=adPosList,proto3" json:"adPosList,omitempty"`
	SdkInfoList          []*AdPosResponseSdkInfo           `protobuf:"bytes,2,rep,name=sdkInfoList,proto3" json:"sdkInfoList,omitempty"`
	GrayScaleControl     int32                             `protobuf:"varint,3,opt,name=grayScaleControl,proto3" json:"grayScaleControl,omitempty"` // 灰度控制:1-自建聚合, 2-topon
	ExposureStrategyList []*AdPosResponse_ExposureStrategy `protobuf:"bytes,4,rep,name=exposureStrategyList,proto3" json:"exposureStrategyList,omitempty"`
	StrategyGroupId      string                            `protobuf:"bytes,5,opt,name=strategyGroupId,proto3" json:"strategyGroupId,omitempty"`
	SdkInfoListV2        []*AdPosResponseSdkInfo           `protobuf:"bytes,6,rep,name=sdkInfoListV2,proto3" json:"sdkInfoListV2,omitempty"`
}

func (x *AdPosResponse) Reset() {
	*x = AdPosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosResponse) ProtoMessage() {}

func (x *AdPosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosResponse.ProtoReflect.Descriptor instead.
func (*AdPosResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{21}
}

func (x *AdPosResponse) GetAdPosList() []*AdPos {
	if x != nil {
		return x.AdPosList
	}
	return nil
}

func (x *AdPosResponse) GetSdkInfoList() []*AdPosResponseSdkInfo {
	if x != nil {
		return x.SdkInfoList
	}
	return nil
}

func (x *AdPosResponse) GetGrayScaleControl() int32 {
	if x != nil {
		return x.GrayScaleControl
	}
	return 0
}

func (x *AdPosResponse) GetExposureStrategyList() []*AdPosResponse_ExposureStrategy {
	if x != nil {
		return x.ExposureStrategyList
	}
	return nil
}

func (x *AdPosResponse) GetStrategyGroupId() string {
	if x != nil {
		return x.StrategyGroupId
	}
	return ""
}

func (x *AdPosResponse) GetSdkInfoListV2() []*AdPosResponseSdkInfo {
	if x != nil {
		return x.SdkInfoListV2
	}
	return nil
}

type AdPos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContainerID     string  `protobuf:"bytes,1,opt,name=containerID,proto3" json:"containerID,omitempty"`
	ContainerType   int32   `protobuf:"varint,2,opt,name=containerType,proto3" json:"containerType,omitempty"`      // 容器类型
	ContainerTypeV2 int32   `protobuf:"varint,19,opt,name=containerTypeV2,proto3" json:"containerTypeV2,omitempty"` // containerTypeV2 与 rtb 保持一致
	ContainerStyle  int32   `protobuf:"varint,3,opt,name=containerStyle,proto3" json:"containerStyle,omitempty"`    // 容器样式
	AdTitleMaxLen   int32   `protobuf:"varint,4,opt,name=adTitleMaxLen,proto3" json:"adTitleMaxLen,omitempty"`
	CloseUnitPos    int32   `protobuf:"varint,5,opt,name=closeUnitPos,proto3" json:"closeUnitPos,omitempty"`       // 关闭组件位置
	FeedbackType    int32   `protobuf:"varint,6,opt,name=feedbackType,proto3" json:"feedbackType,omitempty"`       // 反馈组件
	ChannelShowType int32   `protobuf:"varint,7,opt,name=channelShowType,proto3" json:"channelShowType,omitempty"` // 渠道组件
	DownloadType    int32   `protobuf:"varint,8,opt,name=downloadType,proto3" json:"downloadType,omitempty"`       // 下载类型
	TriggerType     []int32 `protobuf:"varint,9,rep,packed,name=triggerType,proto3" json:"triggerType,omitempty"`  // 按钮组件
	PageID          string  `protobuf:"bytes,10,opt,name=pageID,proto3" json:"pageID,omitempty"`
	Position        int32   `protobuf:"varint,11,opt,name=position,proto3" json:"position,omitempty"`                      // 点位
	AdPosID         string  `protobuf:"bytes,12,opt,name=adPosID,proto3" json:"adPosID,omitempty"`                         // 广告位id
	AdPosStatus     int32   `protobuf:"varint,13,opt,name=adPosStatus,proto3" json:"adPosStatus,omitempty"`                // 广告位状态
	ExposeCondition int32   `protobuf:"varint,14,opt,name=exposeCondition,proto3" json:"exposeCondition,omitempty"`        // 曝光条件
	EndCondition    int32   `protobuf:"varint,15,opt,name=endCondition,proto3" json:"endCondition,omitempty"`              // 结束条件
	MaterialType    []int32 `protobuf:"varint,17,rep,packed,name=materialType,proto3" json:"materialType,omitempty"`       // 物料类型
	InteractionType []int32 `protobuf:"varint,18,rep,packed,name=interactionType,proto3" json:"interactionType,omitempty"` // 交互类型
	AdPositionName  string  `protobuf:"bytes,20,opt,name=adPositionName,proto3" json:"adPositionName,omitempty"`           // 广告位名称
	Place           string  `protobuf:"bytes,21,opt,name=place,proto3" json:"place,omitempty"`                             // 页面位置
}

func (x *AdPos) Reset() {
	*x = AdPos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPos) ProtoMessage() {}

func (x *AdPos) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPos.ProtoReflect.Descriptor instead.
func (*AdPos) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{22}
}

func (x *AdPos) GetContainerID() string {
	if x != nil {
		return x.ContainerID
	}
	return ""
}

func (x *AdPos) GetContainerType() int32 {
	if x != nil {
		return x.ContainerType
	}
	return 0
}

func (x *AdPos) GetContainerTypeV2() int32 {
	if x != nil {
		return x.ContainerTypeV2
	}
	return 0
}

func (x *AdPos) GetContainerStyle() int32 {
	if x != nil {
		return x.ContainerStyle
	}
	return 0
}

func (x *AdPos) GetAdTitleMaxLen() int32 {
	if x != nil {
		return x.AdTitleMaxLen
	}
	return 0
}

func (x *AdPos) GetCloseUnitPos() int32 {
	if x != nil {
		return x.CloseUnitPos
	}
	return 0
}

func (x *AdPos) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *AdPos) GetChannelShowType() int32 {
	if x != nil {
		return x.ChannelShowType
	}
	return 0
}

func (x *AdPos) GetDownloadType() int32 {
	if x != nil {
		return x.DownloadType
	}
	return 0
}

func (x *AdPos) GetTriggerType() []int32 {
	if x != nil {
		return x.TriggerType
	}
	return nil
}

func (x *AdPos) GetPageID() string {
	if x != nil {
		return x.PageID
	}
	return ""
}

func (x *AdPos) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *AdPos) GetAdPosID() string {
	if x != nil {
		return x.AdPosID
	}
	return ""
}

func (x *AdPos) GetAdPosStatus() int32 {
	if x != nil {
		return x.AdPosStatus
	}
	return 0
}

func (x *AdPos) GetExposeCondition() int32 {
	if x != nil {
		return x.ExposeCondition
	}
	return 0
}

func (x *AdPos) GetEndCondition() int32 {
	if x != nil {
		return x.EndCondition
	}
	return 0
}

func (x *AdPos) GetMaterialType() []int32 {
	if x != nil {
		return x.MaterialType
	}
	return nil
}

func (x *AdPos) GetInteractionType() []int32 {
	if x != nil {
		return x.InteractionType
	}
	return nil
}

func (x *AdPos) GetAdPositionName() string {
	if x != nil {
		return x.AdPositionName
	}
	return ""
}

func (x *AdPos) GetPlace() string {
	if x != nil {
		return x.Place
	}
	return ""
}

type ClientBiddingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestID string `protobuf:"bytes,1,opt,name=requestID,proto3" json:"requestID,omitempty"`
}

func (x *ClientBiddingRequest) Reset() {
	*x = ClientBiddingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientBiddingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientBiddingRequest) ProtoMessage() {}

func (x *ClientBiddingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientBiddingRequest.ProtoReflect.Descriptor instead.
func (*ClientBiddingRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{23}
}

func (x *ClientBiddingRequest) GetRequestID() string {
	if x != nil {
		return x.RequestID
	}
	return ""
}

type ClientBiddingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientBiddingTagList  []*ClientBiddingResponse_ClientBiddingTagList `protobuf:"bytes,1,rep,name=clientBiddingTagList,proto3" json:"clientBiddingTagList,omitempty"`
	WaterfallTotalTimeout int64                                         `protobuf:"varint,2,opt,name=waterfallTotalTimeout,proto3" json:"waterfallTotalTimeout,omitempty"`
}

func (x *ClientBiddingResponse) Reset() {
	*x = ClientBiddingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientBiddingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientBiddingResponse) ProtoMessage() {}

func (x *ClientBiddingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientBiddingResponse.ProtoReflect.Descriptor instead.
func (*ClientBiddingResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{24}
}

func (x *ClientBiddingResponse) GetClientBiddingTagList() []*ClientBiddingResponse_ClientBiddingTagList {
	if x != nil {
		return x.ClientBiddingTagList
	}
	return nil
}

func (x *ClientBiddingResponse) GetWaterfallTotalTimeout() int64 {
	if x != nil {
		return x.WaterfallTotalTimeout
	}
	return 0
}

type ExposureStrategyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestID        string                                 `protobuf:"bytes,1,opt,name=requestID,proto3" json:"requestID,omitempty"`
	PromotionInfo    *ExposureStrategyRequest_PromotionInfo `protobuf:"bytes,2,opt,name=promotionInfo,proto3" json:"promotionInfo,omitempty"`
	UserInfo         *ExposureStrategyRequest_UserInfo      `protobuf:"bytes,3,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
	NegativeFeedback *NegativeFeedback                      `protobuf:"bytes,4,opt,name=negativeFeedback,proto3,oneof" json:"negativeFeedback,omitempty"`
}

func (x *ExposureStrategyRequest) Reset() {
	*x = ExposureStrategyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyRequest) ProtoMessage() {}

func (x *ExposureStrategyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyRequest.ProtoReflect.Descriptor instead.
func (*ExposureStrategyRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{25}
}

func (x *ExposureStrategyRequest) GetRequestID() string {
	if x != nil {
		return x.RequestID
	}
	return ""
}

func (x *ExposureStrategyRequest) GetPromotionInfo() *ExposureStrategyRequest_PromotionInfo {
	if x != nil {
		return x.PromotionInfo
	}
	return nil
}

func (x *ExposureStrategyRequest) GetUserInfo() *ExposureStrategyRequest_UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *ExposureStrategyRequest) GetNegativeFeedback() *NegativeFeedback {
	if x != nil {
		return x.NegativeFeedback
	}
	return nil
}

type ExposureStrategyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limit                     bool                                                      `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	StrategyGroupId           string                                                    `protobuf:"bytes,2,opt,name=strategyGroupId,proto3" json:"strategyGroupId,omitempty"`
	StrategyIntervalTupleList []*ExposureStrategyResponse_ExposureStrategyIntervalTuple `protobuf:"bytes,3,rep,name=strategyIntervalTupleList,proto3" json:"strategyIntervalTupleList,omitempty"`
	IntervalTooLarge          bool                                                      `protobuf:"varint,4,opt,name=intervalTooLarge,proto3" json:"intervalTooLarge,omitempty"`
}

func (x *ExposureStrategyResponse) Reset() {
	*x = ExposureStrategyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyResponse) ProtoMessage() {}

func (x *ExposureStrategyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyResponse.ProtoReflect.Descriptor instead.
func (*ExposureStrategyResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{26}
}

func (x *ExposureStrategyResponse) GetLimit() bool {
	if x != nil {
		return x.Limit
	}
	return false
}

func (x *ExposureStrategyResponse) GetStrategyGroupId() string {
	if x != nil {
		return x.StrategyGroupId
	}
	return ""
}

func (x *ExposureStrategyResponse) GetStrategyIntervalTupleList() []*ExposureStrategyResponse_ExposureStrategyIntervalTuple {
	if x != nil {
		return x.StrategyIntervalTupleList
	}
	return nil
}

func (x *ExposureStrategyResponse) GetIntervalTooLarge() bool {
	if x != nil {
		return x.IntervalTooLarge
	}
	return false
}

type GetExpectClickRatioRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId string `protobuf:"bytes,1,opt,name=strategyId,proto3" json:"strategyId,omitempty"`
}

func (x *GetExpectClickRatioRequest) Reset() {
	*x = GetExpectClickRatioRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpectClickRatioRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpectClickRatioRequest) ProtoMessage() {}

func (x *GetExpectClickRatioRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpectClickRatioRequest.ProtoReflect.Descriptor instead.
func (*GetExpectClickRatioRequest) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{27}
}

func (x *GetExpectClickRatioRequest) GetStrategyId() string {
	if x != nil {
		return x.StrategyId
	}
	return ""
}

type GetExpectClickRatioResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpectClickRatioList []*GetExpectClickRatioResponse_ExpectClickRatio `protobuf:"bytes,1,rep,name=expectClickRatioList,proto3" json:"expectClickRatioList,omitempty"`
}

func (x *GetExpectClickRatioResponse) Reset() {
	*x = GetExpectClickRatioResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpectClickRatioResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpectClickRatioResponse) ProtoMessage() {}

func (x *GetExpectClickRatioResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpectClickRatioResponse.ProtoReflect.Descriptor instead.
func (*GetExpectClickRatioResponse) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{28}
}

func (x *GetExpectClickRatioResponse) GetExpectClickRatioList() []*GetExpectClickRatioResponse_ExpectClickRatio {
	if x != nil {
		return x.ExpectClickRatioList
	}
	return nil
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                 `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	TagId      string                 `protobuf:"bytes,8,opt,name=tagId,proto3" json:"tagId,omitempty"`
	AdslotSize *BidRequest_AdslotSize `protobuf:"bytes,9,opt,name=adslotSize,proto3" json:"adslotSize,omitempty"`
	Bidfloor   float64                `protobuf:"fixed64,10,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Deeplink   bool                   `protobuf:"varint,11,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Secure     int32                  `protobuf:"varint,12,opt,name=secure,proto3" json:"secure,omitempty"`
	Deals      []*BidRequest_Deal     `protobuf:"bytes,13,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 0}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdslotSize() *BidRequest_AdslotSize {
	if x != nil {
		return x.AdslotSize
	}
	return nil
}

func (x *BidRequest_Imp) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetDeeplink() bool {
	if x != nil {
		return x.Deeplink
	}
	return false
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequest_Imp) GetDeals() []*BidRequest_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type BidRequest_AdslotSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width       int32    `protobuf:"varint,14,opt,name=width,proto3" json:"width,omitempty"`
	Height      int32    `protobuf:"varint,15,opt,name=height,proto3" json:"height,omitempty"`
	Mimes       []string `protobuf:"bytes,16,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Size        int32    `protobuf:"varint,17,opt,name=size,proto3" json:"size,omitempty"`
	TitleLength int32    `protobuf:"varint,18,opt,name=titleLength,proto3" json:"titleLength,omitempty"`
	DescLength  int32    `protobuf:"varint,19,opt,name=descLength,proto3" json:"descLength,omitempty"`
	MinDuration int32    `protobuf:"varint,20,opt,name=minDuration,proto3" json:"minDuration,omitempty"`
	MaxDuration int32    `protobuf:"varint,21,opt,name=maxDuration,proto3" json:"maxDuration,omitempty"`
}

func (x *BidRequest_AdslotSize) Reset() {
	*x = BidRequest_AdslotSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdslotSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdslotSize) ProtoMessage() {}

func (x *BidRequest_AdslotSize) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdslotSize.ProtoReflect.Descriptor instead.
func (*BidRequest_AdslotSize) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 1}
}

func (x *BidRequest_AdslotSize) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_AdslotSize) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetTitleLength() int32 {
	if x != nil {
		return x.TitleLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetDescLength() int32 {
	if x != nil {
		return x.DescLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMinDuration() int32 {
	if x != nil {
		return x.MinDuration
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,22,opt,name=appName,proto3" json:"appName,omitempty"`
	Bundle     string `protobuf:"bytes,23,opt,name=bundle,proto3" json:"bundle,omitempty"`
	AppVersion string `protobuf:"bytes,24,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 2}
}

func (x *BidRequest_App) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os              string          `protobuf:"bytes,25,opt,name=os,proto3" json:"os,omitempty"`
	Osv             string          `protobuf:"bytes,26,opt,name=osv,proto3" json:"osv,omitempty"`
	Imei            string          `protobuf:"bytes,27,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5         string          `protobuf:"bytes,28,opt,name=imeiMd5,proto3" json:"imeiMd5,omitempty"`
	Oaid            string          `protobuf:"bytes,29,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5         string          `protobuf:"bytes,30,opt,name=oaidMd5,proto3" json:"oaidMd5,omitempty"`
	AndroidId       string          `protobuf:"bytes,31,opt,name=androidId,proto3" json:"androidId,omitempty"`
	Idfa            string          `protobuf:"bytes,32,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5         string          `protobuf:"bytes,33,opt,name=idfaMd5,proto3" json:"idfaMd5,omitempty"`
	Mac             string          `protobuf:"bytes,34,opt,name=mac,proto3" json:"mac,omitempty"`
	MacMd5          string          `protobuf:"bytes,35,opt,name=macMd5,proto3" json:"macMd5,omitempty"`
	Ip              string          `protobuf:"bytes,36,opt,name=ip,proto3" json:"ip,omitempty"`
	IpV6            string          `protobuf:"bytes,37,opt,name=ipV6,proto3" json:"ipV6,omitempty"`
	Ua              string          `protobuf:"bytes,38,opt,name=ua,proto3" json:"ua,omitempty"`
	ConnectionType  int32           `protobuf:"varint,39,opt,name=connectionType,proto3" json:"connectionType,omitempty"`
	Brand           string          `protobuf:"bytes,40,opt,name=brand,proto3" json:"brand,omitempty"`
	Make            string          `protobuf:"bytes,41,opt,name=make,proto3" json:"make,omitempty"`
	Model           string          `protobuf:"bytes,42,opt,name=model,proto3" json:"model,omitempty"`
	Hwv             string          `protobuf:"bytes,43,opt,name=hwv,proto3" json:"hwv,omitempty"`
	Carrier         int32           `protobuf:"varint,44,opt,name=carrier,proto3" json:"carrier,omitempty"`
	MccMnc          string          `protobuf:"bytes,45,opt,name=mccMnc,proto3" json:"mccMnc,omitempty"`
	ScreenHeight    int32           `protobuf:"varint,46,opt,name=screenHeight,proto3" json:"screenHeight,omitempty"`
	ScreenWidth     int32           `protobuf:"varint,47,opt,name=screenWidth,proto3" json:"screenWidth,omitempty"`
	Ppi             int32           `protobuf:"varint,48,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo             *BidRequest_Geo `protobuf:"bytes,49,opt,name=geo,proto3" json:"geo,omitempty"`
	AppList         string          `protobuf:"bytes,50,opt,name=appList,proto3" json:"appList,omitempty"`
	BootMark        string          `protobuf:"bytes,51,opt,name=bootMark,proto3" json:"bootMark,omitempty"`
	UpdateMark      string          `protobuf:"bytes,52,opt,name=updateMark,proto3" json:"updateMark,omitempty"`
	VerCodeOfHms    string          `protobuf:"bytes,53,opt,name=verCodeOfHms,proto3" json:"verCodeOfHms,omitempty"`
	VerCodeOfAG     string          `protobuf:"bytes,54,opt,name=verCodeOfAG,proto3" json:"verCodeOfAG,omitempty"`
	RomVersion      string          `protobuf:"bytes,55,opt,name=romVersion,proto3" json:"romVersion,omitempty"`
	Orientation     int32           `protobuf:"varint,56,opt,name=orientation,proto3" json:"orientation,omitempty"`
	BootTimeSec     string          `protobuf:"bytes,57,opt,name=bootTimeSec,proto3" json:"bootTimeSec,omitempty"`
	PhoneName       string          `protobuf:"bytes,58,opt,name=phoneName,proto3" json:"phoneName,omitempty"`
	MemorySize      int64           `protobuf:"varint,59,opt,name=memorySize,proto3" json:"memorySize,omitempty"`
	DiskSize        int64           `protobuf:"varint,60,opt,name=diskSize,proto3" json:"diskSize,omitempty"`
	OsUpdateTimeSec string          `protobuf:"bytes,61,opt,name=osUpdateTimeSec,proto3" json:"osUpdateTimeSec,omitempty"`
	ModelCode       string          `protobuf:"bytes,62,opt,name=modelCode,proto3" json:"modelCode,omitempty"`
	TimeZone        string          `protobuf:"bytes,63,opt,name=timeZone,proto3" json:"timeZone,omitempty"`
	UpdateMark1     string          `protobuf:"bytes,74,opt,name=updateMark1,proto3" json:"updateMark1,omitempty"` // 爱奇艺专用
	// 废弃
	FileTime        string           `protobuf:"bytes,64,opt,name=fileTime,proto3" json:"fileTime,omitempty"`
	DeviceBirthTime string           `protobuf:"bytes,65,opt,name=deviceBirthTime,proto3" json:"deviceBirthTime,omitempty"`
	CaidVersion     string           `protobuf:"bytes,66,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	Caid            string           `protobuf:"bytes,67,opt,name=caid,proto3" json:"caid,omitempty"`
	Paid            string           `protobuf:"bytes,68,opt,name=paid,proto3" json:"paid,omitempty"`
	CaidWithFactors *CaidWithFactors `protobuf:"bytes,69,opt,name=caidWithFactors,proto3" json:"caidWithFactors,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 3}
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetIpV6() string {
	if x != nil {
		return x.IpV6
	}
	return ""
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil {
		return x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil {
		return x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMccMnc() string {
	if x != nil {
		return x.MccMnc
	}
	return ""
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetAppList() string {
	if x != nil {
		return x.AppList
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfHms() string {
	if x != nil {
		return x.VerCodeOfHms
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfAG() string {
	if x != nil {
		return x.VerCodeOfAG
	}
	return ""
}

func (x *BidRequest_Device) GetRomVersion() string {
	if x != nil {
		return x.RomVersion
	}
	return ""
}

func (x *BidRequest_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *BidRequest_Device) GetBootTimeSec() string {
	if x != nil {
		return x.BootTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *BidRequest_Device) GetMemorySize() int64 {
	if x != nil {
		return x.MemorySize
	}
	return 0
}

func (x *BidRequest_Device) GetDiskSize() int64 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *BidRequest_Device) GetOsUpdateTimeSec() string {
	if x != nil {
		return x.OsUpdateTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetModelCode() string {
	if x != nil {
		return x.ModelCode
	}
	return ""
}

func (x *BidRequest_Device) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark1() string {
	if x != nil {
		return x.UpdateMark1
	}
	return ""
}

func (x *BidRequest_Device) GetFileTime() string {
	if x != nil {
		return x.FileTime
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceBirthTime() string {
	if x != nil {
		return x.DeviceBirthTime
	}
	return ""
}

func (x *BidRequest_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *BidRequest_Device) GetCaidWithFactors() *CaidWithFactors {
	if x != nil {
		return x.CaidWithFactors
	}
	return nil
}

type BidRequest_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string  `protobuf:"bytes,57,opt,name=id,proto3" json:"id,omitempty"`
	Bidfloor float64 `protobuf:"fixed64,58,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
}

func (x *BidRequest_Deal) Reset() {
	*x = BidRequest_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Deal) ProtoMessage() {}

func (x *BidRequest_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Deal) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 4}
}

func (x *BidRequest_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Deal) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32   `protobuf:"varint,59,opt,name=type,proto3" json:"type,omitempty"`
	Lat     float64 `protobuf:"fixed64,60,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon     float64 `protobuf:"fixed64,61,opt,name=lon,proto3" json:"lon,omitempty"`
	Country string  `protobuf:"bytes,62,opt,name=country,proto3" json:"country,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 5}
}

func (x *BidRequest_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Age    int32 `protobuf:"varint,63,opt,name=age,proto3" json:"age,omitempty"`
	Gender int32 `protobuf:"varint,64,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{2, 6}
}

func (x *BidRequest_User) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *BidRequest_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_Bid `protobuf:"bytes,68,rep,name=bid,proto3" json:"bid,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid        string                    `protobuf:"bytes,69,opt,name=impid,proto3" json:"impid,omitempty"`
	AdType       int32                     `protobuf:"varint,70,opt,name=adType,proto3" json:"adType,omitempty"`
	AdStyle      int32                     `protobuf:"varint,71,opt,name=adStyle,proto3" json:"adStyle,omitempty"`
	Item         *BidResponse_Item         `protobuf:"bytes,72,opt,name=item,proto3" json:"item,omitempty"`
	Price        float64                   `protobuf:"fixed64,73,opt,name=price,proto3" json:"price,omitempty"`
	Nurl         string                    `protobuf:"bytes,74,opt,name=nurl,proto3" json:"nurl,omitempty"`
	Crid         string                    `protobuf:"bytes,99,opt,name=crid,proto3" json:"crid,omitempty"`
	ApiName      string                    `protobuf:"bytes,104,opt,name=apiName,proto3" json:"apiName,omitempty"`
	Cid          string                    `protobuf:"bytes,107,opt,name=cid,proto3" json:"cid,omitempty"`
	ShakeSetting *BidResponse_ShakeSetting `protobuf:"bytes,600,opt,name=shakeSetting,proto3" json:"shakeSetting,omitempty"`
	LogoUrl      string                    `protobuf:"bytes,601,opt,name=logoUrl,proto3" json:"logoUrl,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 1}
}

func (x *BidResponse_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_Bid) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidResponse_Bid) GetAdStyle() int32 {
	if x != nil {
		return x.AdStyle
	}
	return 0
}

func (x *BidResponse_Bid) GetItem() *BidResponse_Item {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *BidResponse_Bid) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *BidResponse_Bid) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *BidResponse_Bid) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *BidResponse_Bid) GetShakeSetting() *BidResponse_ShakeSetting {
	if x != nil {
		return x.ShakeSetting
	}
	return nil
}

func (x *BidResponse_Bid) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

type BidResponse_ShakeSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShakeSwitch      string `protobuf:"bytes,601,opt,name=shakeSwitch,proto3" json:"shakeSwitch,omitempty"`
	ShakeSensitivity string `protobuf:"bytes,602,opt,name=shakeSensitivity,proto3" json:"shakeSensitivity,omitempty"`
}

func (x *BidResponse_ShakeSetting) Reset() {
	*x = BidResponse_ShakeSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_ShakeSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_ShakeSetting) ProtoMessage() {}

func (x *BidResponse_ShakeSetting) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_ShakeSetting.ProtoReflect.Descriptor instead.
func (*BidResponse_ShakeSetting) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 2}
}

func (x *BidResponse_ShakeSetting) GetShakeSwitch() string {
	if x != nil {
		return x.ShakeSwitch
	}
	return ""
}

func (x *BidResponse_ShakeSetting) GetShakeSensitivity() string {
	if x != nil {
		return x.ShakeSensitivity
	}
	return ""
}

type BidResponse_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                       string                       `protobuf:"bytes,75,opt,name=title,proto3" json:"title,omitempty"`
	Desc                        string                       `protobuf:"bytes,76,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                        string                       `protobuf:"bytes,77,opt,name=icon,proto3" json:"icon,omitempty"`
	Html                        string                       `protobuf:"bytes,78,opt,name=html,proto3" json:"html,omitempty"`
	MediaStyle                  int32                        `protobuf:"varint,79,opt,name=mediaStyle,proto3" json:"mediaStyle,omitempty"`
	DownloadUrl                 string                       `protobuf:"bytes,80,opt,name=downloadUrl,proto3" json:"downloadUrl,omitempty"`
	DownloadAppInfo             *BidResponse_DownloadAppInfo `protobuf:"bytes,81,opt,name=downloadAppInfo,proto3" json:"downloadAppInfo,omitempty"`
	ClickUrl                    string                       `protobuf:"bytes,82,opt,name=clickUrl,proto3" json:"clickUrl,omitempty"`
	DplUrl                      string                       `protobuf:"bytes,83,opt,name=dplUrl,proto3" json:"dplUrl,omitempty"`
	Imgs                        []string                     `protobuf:"bytes,84,rep,name=imgs,proto3" json:"imgs,omitempty"`
	ExposalUrls                 []string                     `protobuf:"bytes,85,rep,name=exposalUrls,proto3" json:"exposalUrls,omitempty"`
	ClickMonitorUrls            []string                     `protobuf:"bytes,86,rep,name=clickMonitorUrls,proto3" json:"clickMonitorUrls,omitempty"`
	Video                       *BidResponse_Video           `protobuf:"bytes,87,opt,name=video,proto3" json:"video,omitempty"`
	MiniProgramId               string                       `protobuf:"bytes,88,opt,name=miniProgramId,proto3" json:"miniProgramId,omitempty"`
	MiniProgramPath             string                       `protobuf:"bytes,89,opt,name=miniProgramPath,proto3" json:"miniProgramPath,omitempty"`
	MiniProgramType             int32                        `protobuf:"varint,90,opt,name=miniProgramType,proto3" json:"miniProgramType,omitempty"`
	MiniProgramSuccessTrackUrls []string                     `protobuf:"bytes,91,rep,name=miniProgramSuccessTrackUrls,proto3" json:"miniProgramSuccessTrackUrls,omitempty"`
	DownloadTrackUrls           []string                     `protobuf:"bytes,92,rep,name=downloadTrackUrls,proto3" json:"downloadTrackUrls,omitempty"`
	DownloadedTrackUrls         []string                     `protobuf:"bytes,93,rep,name=downloadedTrackUrls,proto3" json:"downloadedTrackUrls,omitempty"`
	InstalledTrackUrls          []string                     `protobuf:"bytes,94,rep,name=installedTrackUrls,proto3" json:"installedTrackUrls,omitempty"`
	DpSuccessTrackUrls          []string                     `protobuf:"bytes,95,rep,name=dpSuccessTrackUrls,proto3" json:"dpSuccessTrackUrls,omitempty"`
	ActionTrackUrls             []string                     `protobuf:"bytes,96,rep,name=actionTrackUrls,proto3" json:"actionTrackUrls,omitempty"`
	PackageName                 string                       `protobuf:"bytes,97,opt,name=packageName,proto3" json:"packageName,omitempty"`
}

func (x *BidResponse_Item) Reset() {
	*x = BidResponse_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Item) ProtoMessage() {}

func (x *BidResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Item.ProtoReflect.Descriptor instead.
func (*BidResponse_Item) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 3}
}

func (x *BidResponse_Item) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Item) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponse_Item) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

func (x *BidResponse_Item) GetMediaStyle() int32 {
	if x != nil {
		return x.MediaStyle
	}
	return 0
}

func (x *BidResponse_Item) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDownloadAppInfo() *BidResponse_DownloadAppInfo {
	if x != nil {
		return x.DownloadAppInfo
	}
	return nil
}

func (x *BidResponse_Item) GetClickUrl() string {
	if x != nil {
		return x.ClickUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDplUrl() string {
	if x != nil {
		return x.DplUrl
	}
	return ""
}

func (x *BidResponse_Item) GetImgs() []string {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidResponse_Item) GetExposalUrls() []string {
	if x != nil {
		return x.ExposalUrls
	}
	return nil
}

func (x *BidResponse_Item) GetClickMonitorUrls() []string {
	if x != nil {
		return x.ClickMonitorUrls
	}
	return nil
}

func (x *BidResponse_Item) GetVideo() *BidResponse_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Item) GetMiniProgramId() string {
	if x != nil {
		return x.MiniProgramId
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramPath() string {
	if x != nil {
		return x.MiniProgramPath
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramType() int32 {
	if x != nil {
		return x.MiniProgramType
	}
	return 0
}

func (x *BidResponse_Item) GetMiniProgramSuccessTrackUrls() []string {
	if x != nil {
		return x.MiniProgramSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadTrackUrls() []string {
	if x != nil {
		return x.DownloadTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadedTrackUrls() []string {
	if x != nil {
		return x.DownloadedTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetInstalledTrackUrls() []string {
	if x != nil {
		return x.InstalledTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDpSuccessTrackUrls() []string {
	if x != nil {
		return x.DpSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetActionTrackUrls() []string {
	if x != nil {
		return x.ActionTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

type BidResponse_DownloadAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,88,opt,name=appName,proto3" json:"appName,omitempty"`
	Developer  string `protobuf:"bytes,89,opt,name=developer,proto3" json:"developer,omitempty"`
	Version    string `protobuf:"bytes,90,opt,name=version,proto3" json:"version,omitempty"`
	PacketSize string `protobuf:"bytes,91,opt,name=packetSize,proto3" json:"packetSize,omitempty"`
	Privacy    string `protobuf:"bytes,92,opt,name=privacy,proto3" json:"privacy,omitempty"`
	Permission string `protobuf:"bytes,93,opt,name=permission,proto3" json:"permission,omitempty"`
	Desc       string `protobuf:"bytes,101,opt,name=desc,proto3" json:"desc,omitempty"`
	DescURL    string `protobuf:"bytes,102,opt,name=descURL,proto3" json:"descURL,omitempty"`
}

func (x *BidResponse_DownloadAppInfo) Reset() {
	*x = BidResponse_DownloadAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_DownloadAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_DownloadAppInfo) ProtoMessage() {}

func (x *BidResponse_DownloadAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_DownloadAppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_DownloadAppInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 4}
}

func (x *BidResponse_DownloadAppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPacketSize() string {
	if x != nil {
		return x.PacketSize
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPrivacy() string {
	if x != nil {
		return x.Privacy
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDescURL() string {
	if x != nil {
		return x.DescURL
	}
	return ""
}

type BidResponse_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl       string `protobuf:"bytes,94,opt,name=videoUrl,proto3" json:"videoUrl,omitempty"`
	VideoDuration  int32  `protobuf:"varint,95,opt,name=videoDuration,proto3" json:"videoDuration,omitempty"`
	VideoStartUrl  string `protobuf:"bytes,96,opt,name=videoStartUrl,proto3" json:"videoStartUrl,omitempty"`
	VideoFinishUrl string `protobuf:"bytes,97,opt,name=videoFinishUrl,proto3" json:"videoFinishUrl,omitempty"`
	VideoVastXml   string `protobuf:"bytes,98,opt,name=videoVastXml,proto3" json:"videoVastXml,omitempty"`
	VideoEndImgurl string `protobuf:"bytes,99,opt,name=videoEndImgurl,proto3" json:"videoEndImgurl,omitempty"`
	VideoPreImgurl string `protobuf:"bytes,100,opt,name=videoPreImgurl,proto3" json:"videoPreImgurl,omitempty"`
}

func (x *BidResponse_Video) Reset() {
	*x = BidResponse_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Video) ProtoMessage() {}

func (x *BidResponse_Video) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Video) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{3, 5}
}

func (x *BidResponse_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Video) GetVideoStartUrl() string {
	if x != nil {
		return x.VideoStartUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoFinishUrl() string {
	if x != nil {
		return x.VideoFinishUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoVastXml() string {
	if x != nil {
		return x.VideoVastXml
	}
	return ""
}

func (x *BidResponse_Video) GetVideoEndImgurl() string {
	if x != nil {
		return x.VideoEndImgurl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoPreImgurl() string {
	if x != nil {
		return x.VideoPreImgurl
	}
	return ""
}

type BidRequestV2_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                   `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	TagId      string                   `protobuf:"bytes,8,opt,name=tagId,proto3" json:"tagId,omitempty"`
	AdslotSize *BidRequestV2_AdSlotSize `protobuf:"bytes,9,opt,name=adslotSize,proto3" json:"adslotSize,omitempty"`
	Bidfloor   float64                  `protobuf:"fixed64,10,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Deeplink   bool                     `protobuf:"varint,11,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Secure     int32                    `protobuf:"varint,12,opt,name=secure,proto3" json:"secure,omitempty"`
	DealList   []*BidRequestV2_Deal     `protobuf:"bytes,13,rep,name=dealList,proto3" json:"dealList,omitempty"`
}

func (x *BidRequestV2_Imp) Reset() {
	*x = BidRequestV2_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Imp) ProtoMessage() {}

func (x *BidRequestV2_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Imp.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Imp) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 0}
}

func (x *BidRequestV2_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequestV2_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequestV2_Imp) GetAdslotSize() *BidRequestV2_AdSlotSize {
	if x != nil {
		return x.AdslotSize
	}
	return nil
}

func (x *BidRequestV2_Imp) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequestV2_Imp) GetDeeplink() bool {
	if x != nil {
		return x.Deeplink
	}
	return false
}

func (x *BidRequestV2_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequestV2_Imp) GetDealList() []*BidRequestV2_Deal {
	if x != nil {
		return x.DealList
	}
	return nil
}

type BidRequestV2_AdSlotSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width       int32    `protobuf:"varint,14,opt,name=width,proto3" json:"width,omitempty"`
	Height      int32    `protobuf:"varint,15,opt,name=height,proto3" json:"height,omitempty"`
	Mimes       []string `protobuf:"bytes,16,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Size        int32    `protobuf:"varint,17,opt,name=size,proto3" json:"size,omitempty"`
	TitleLength int32    `protobuf:"varint,18,opt,name=titleLength,proto3" json:"titleLength,omitempty"`
	DescLength  int32    `protobuf:"varint,19,opt,name=descLength,proto3" json:"descLength,omitempty"`
	MinDuration int32    `protobuf:"varint,20,opt,name=minDuration,proto3" json:"minDuration,omitempty"`
	MaxDuration int32    `protobuf:"varint,21,opt,name=maxDuration,proto3" json:"maxDuration,omitempty"`
}

func (x *BidRequestV2_AdSlotSize) Reset() {
	*x = BidRequestV2_AdSlotSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_AdSlotSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_AdSlotSize) ProtoMessage() {}

func (x *BidRequestV2_AdSlotSize) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_AdSlotSize.ProtoReflect.Descriptor instead.
func (*BidRequestV2_AdSlotSize) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 1}
}

func (x *BidRequestV2_AdSlotSize) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequestV2_AdSlotSize) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetTitleLength() int32 {
	if x != nil {
		return x.TitleLength
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetDescLength() int32 {
	if x != nil {
		return x.DescLength
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetMinDuration() int32 {
	if x != nil {
		return x.MinDuration
	}
	return 0
}

func (x *BidRequestV2_AdSlotSize) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

type BidRequestV2_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,22,opt,name=appName,proto3" json:"appName,omitempty"`
	Bundle     string `protobuf:"bytes,23,opt,name=bundle,proto3" json:"bundle,omitempty"`
	AppVersion string `protobuf:"bytes,24,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
}

func (x *BidRequestV2_App) Reset() {
	*x = BidRequestV2_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_App) ProtoMessage() {}

func (x *BidRequestV2_App) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_App.ProtoReflect.Descriptor instead.
func (*BidRequestV2_App) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 2}
}

func (x *BidRequestV2_App) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidRequestV2_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequestV2_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type BidRequestV2_PosIdClicked struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PosId      string `protobuf:"bytes,1,opt,name=posId,proto3" json:"posId,omitempty"`
	ClickedNum int32  `protobuf:"varint,2,opt,name=clickedNum,proto3" json:"clickedNum,omitempty"`
}

func (x *BidRequestV2_PosIdClicked) Reset() {
	*x = BidRequestV2_PosIdClicked{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_PosIdClicked) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_PosIdClicked) ProtoMessage() {}

func (x *BidRequestV2_PosIdClicked) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_PosIdClicked.ProtoReflect.Descriptor instead.
func (*BidRequestV2_PosIdClicked) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 3}
}

func (x *BidRequestV2_PosIdClicked) GetPosId() string {
	if x != nil {
		return x.PosId
	}
	return ""
}

func (x *BidRequestV2_PosIdClicked) GetClickedNum() int32 {
	if x != nil {
		return x.ClickedNum
	}
	return 0
}

type BidRequestV2_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	City                string                       `protobuf:"bytes,22,opt,name=city,proto3" json:"city,omitempty"`
	Versioncode         string                       `protobuf:"bytes,23,opt,name=versioncode,proto3" json:"versioncode,omitempty"`
	Band                string                       `protobuf:"bytes,24,opt,name=band,proto3" json:"band,omitempty"`
	IdfaFill            bool                         `protobuf:"varint,25,opt,name=idfa_fill,json=idfaFill,proto3" json:"idfa_fill,omitempty"`
	AndroidIdFill       bool                         `protobuf:"varint,26,opt,name=android_id_fill,json=androidIdFill,proto3" json:"android_id_fill,omitempty"`
	Network             string                       `protobuf:"bytes,27,opt,name=network,proto3" json:"network,omitempty"`
	InstalledTime       int32                        `protobuf:"varint,28,opt,name=installed_time,json=installedTime,proto3" json:"installed_time,omitempty"`
	StartType           string                       `protobuf:"bytes,29,opt,name=start_type,json=startType,proto3" json:"start_type,omitempty"`
	HotStartExposalNum  int32                        `protobuf:"varint,30,opt,name=hot_start_exposal_num,json=hotStartExposalNum,proto3" json:"hot_start_exposal_num,omitempty"`
	ColdStartExposalNum int32                        `protobuf:"varint,31,opt,name=cold_start_exposal_num,json=coldStartExposalNum,proto3" json:"cold_start_exposal_num,omitempty"`
	HotStartClickNum    int32                        `protobuf:"varint,32,opt,name=hot_start_click_num,json=hotStartClickNum,proto3" json:"hot_start_click_num,omitempty"`
	ColdStartClickNum   int32                        `protobuf:"varint,33,opt,name=cold_start_click_num,json=coldStartClickNum,proto3" json:"cold_start_click_num,omitempty"`
	UserValue           string                       `protobuf:"bytes,35,opt,name=user_value,json=userValue,proto3" json:"user_value,omitempty"`
	ForceOld            bool                         `protobuf:"varint,36,opt,name=force_old,json=forceOld,proto3" json:"force_old,omitempty"`
	Label               string                       `protobuf:"bytes,38,opt,name=label,proto3" json:"label,omitempty"`
	CustomParameter     string                       `protobuf:"bytes,39,opt,name=custom_parameter,json=customParameter,proto3" json:"custom_parameter,omitempty"`
	PromotionInfo       *PromotionInfo               `protobuf:"bytes,40,opt,name=promotionInfo,proto3" json:"promotionInfo,omitempty"`
	PosIdClickedList    []*BidRequestV2_PosIdClicked `protobuf:"bytes,41,rep,name=posIdClickedList,proto3" json:"posIdClickedList,omitempty"`
	Arpu                float32                      `protobuf:"fixed32,42,opt,name=arpu,proto3" json:"arpu,omitempty"`
	Os                  int32                        `protobuf:"varint,43,opt,name=os,proto3" json:"os,omitempty"`
	NegativeFeedback    *NegativeFeedback            `protobuf:"bytes,44,opt,name=negativeFeedback,proto3,oneof" json:"negativeFeedback,omitempty"`
}

func (x *BidRequestV2_Tag) Reset() {
	*x = BidRequestV2_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Tag) ProtoMessage() {}

func (x *BidRequestV2_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Tag.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Tag) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 4}
}

func (x *BidRequestV2_Tag) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *BidRequestV2_Tag) GetVersioncode() string {
	if x != nil {
		return x.Versioncode
	}
	return ""
}

func (x *BidRequestV2_Tag) GetBand() string {
	if x != nil {
		return x.Band
	}
	return ""
}

func (x *BidRequestV2_Tag) GetIdfaFill() bool {
	if x != nil {
		return x.IdfaFill
	}
	return false
}

func (x *BidRequestV2_Tag) GetAndroidIdFill() bool {
	if x != nil {
		return x.AndroidIdFill
	}
	return false
}

func (x *BidRequestV2_Tag) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *BidRequestV2_Tag) GetInstalledTime() int32 {
	if x != nil {
		return x.InstalledTime
	}
	return 0
}

func (x *BidRequestV2_Tag) GetStartType() string {
	if x != nil {
		return x.StartType
	}
	return ""
}

func (x *BidRequestV2_Tag) GetHotStartExposalNum() int32 {
	if x != nil {
		return x.HotStartExposalNum
	}
	return 0
}

func (x *BidRequestV2_Tag) GetColdStartExposalNum() int32 {
	if x != nil {
		return x.ColdStartExposalNum
	}
	return 0
}

func (x *BidRequestV2_Tag) GetHotStartClickNum() int32 {
	if x != nil {
		return x.HotStartClickNum
	}
	return 0
}

func (x *BidRequestV2_Tag) GetColdStartClickNum() int32 {
	if x != nil {
		return x.ColdStartClickNum
	}
	return 0
}

func (x *BidRequestV2_Tag) GetUserValue() string {
	if x != nil {
		return x.UserValue
	}
	return ""
}

func (x *BidRequestV2_Tag) GetForceOld() bool {
	if x != nil {
		return x.ForceOld
	}
	return false
}

func (x *BidRequestV2_Tag) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *BidRequestV2_Tag) GetCustomParameter() string {
	if x != nil {
		return x.CustomParameter
	}
	return ""
}

func (x *BidRequestV2_Tag) GetPromotionInfo() *PromotionInfo {
	if x != nil {
		return x.PromotionInfo
	}
	return nil
}

func (x *BidRequestV2_Tag) GetPosIdClickedList() []*BidRequestV2_PosIdClicked {
	if x != nil {
		return x.PosIdClickedList
	}
	return nil
}

func (x *BidRequestV2_Tag) GetArpu() float32 {
	if x != nil {
		return x.Arpu
	}
	return 0
}

func (x *BidRequestV2_Tag) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *BidRequestV2_Tag) GetNegativeFeedback() *NegativeFeedback {
	if x != nil {
		return x.NegativeFeedback
	}
	return nil
}

type BidRequestV2_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os              string            `protobuf:"bytes,25,opt,name=os,proto3" json:"os,omitempty"`
	Osv             string            `protobuf:"bytes,26,opt,name=osv,proto3" json:"osv,omitempty"`
	Imei            string            `protobuf:"bytes,27,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5         string            `protobuf:"bytes,28,opt,name=imeiMd5,proto3" json:"imeiMd5,omitempty"`
	Oaid            string            `protobuf:"bytes,29,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5         string            `protobuf:"bytes,30,opt,name=oaidMd5,proto3" json:"oaidMd5,omitempty"`
	AndroidId       string            `protobuf:"bytes,31,opt,name=androidId,proto3" json:"androidId,omitempty"`
	Idfa            string            `protobuf:"bytes,32,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5         string            `protobuf:"bytes,33,opt,name=idfaMd5,proto3" json:"idfaMd5,omitempty"`
	Mac             string            `protobuf:"bytes,34,opt,name=mac,proto3" json:"mac,omitempty"`
	MacMd5          string            `protobuf:"bytes,35,opt,name=macMd5,proto3" json:"macMd5,omitempty"`
	Ip              string            `protobuf:"bytes,36,opt,name=ip,proto3" json:"ip,omitempty"`
	IpV6            string            `protobuf:"bytes,37,opt,name=ipV6,proto3" json:"ipV6,omitempty"`
	Ua              string            `protobuf:"bytes,38,opt,name=ua,proto3" json:"ua,omitempty"`
	ConnectionType  int32             `protobuf:"varint,39,opt,name=connectionType,proto3" json:"connectionType,omitempty"`
	Brand           string            `protobuf:"bytes,40,opt,name=brand,proto3" json:"brand,omitempty"`
	Make            string            `protobuf:"bytes,41,opt,name=make,proto3" json:"make,omitempty"`
	Model           string            `protobuf:"bytes,42,opt,name=model,proto3" json:"model,omitempty"`
	Hwv             string            `protobuf:"bytes,43,opt,name=hwv,proto3" json:"hwv,omitempty"`
	Carrier         int32             `protobuf:"varint,44,opt,name=carrier,proto3" json:"carrier,omitempty"`
	MccMnc          string            `protobuf:"bytes,45,opt,name=mccMnc,proto3" json:"mccMnc,omitempty"`
	ScreenHeight    int32             `protobuf:"varint,46,opt,name=screenHeight,proto3" json:"screenHeight,omitempty"`
	ScreenWidth     int32             `protobuf:"varint,47,opt,name=screenWidth,proto3" json:"screenWidth,omitempty"`
	Ppi             int32             `protobuf:"varint,48,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo             *BidRequestV2_Geo `protobuf:"bytes,49,opt,name=geo,proto3" json:"geo,omitempty"`
	AppList         string            `protobuf:"bytes,50,opt,name=appList,proto3" json:"appList,omitempty"`
	BootMark        string            `protobuf:"bytes,51,opt,name=bootMark,proto3" json:"bootMark,omitempty"`
	UpdateMark      string            `protobuf:"bytes,52,opt,name=updateMark,proto3" json:"updateMark,omitempty"`
	VerCodeOfHms    string            `protobuf:"bytes,53,opt,name=verCodeOfHms,proto3" json:"verCodeOfHms,omitempty"`
	VerCodeOfAG     string            `protobuf:"bytes,54,opt,name=verCodeOfAG,proto3" json:"verCodeOfAG,omitempty"`
	RomVersion      string            `protobuf:"bytes,55,opt,name=romVersion,proto3" json:"romVersion,omitempty"`
	Orientation     int32             `protobuf:"varint,56,opt,name=orientation,proto3" json:"orientation,omitempty"`
	BootTimeSec     string            `protobuf:"bytes,57,opt,name=bootTimeSec,proto3" json:"bootTimeSec,omitempty"`
	PhoneName       string            `protobuf:"bytes,58,opt,name=phoneName,proto3" json:"phoneName,omitempty"`
	MemorySize      int64             `protobuf:"varint,59,opt,name=memorySize,proto3" json:"memorySize,omitempty"`
	DiskSize        int64             `protobuf:"varint,60,opt,name=diskSize,proto3" json:"diskSize,omitempty"`
	OsUpdateTimeSec string            `protobuf:"bytes,61,opt,name=osUpdateTimeSec,proto3" json:"osUpdateTimeSec,omitempty"`
	ModelCode       string            `protobuf:"bytes,62,opt,name=modelCode,proto3" json:"modelCode,omitempty"`
	TimeZone        string            `protobuf:"bytes,63,opt,name=timeZone,proto3" json:"timeZone,omitempty"`
	CaidWithFactors *CaidWithFactors  `protobuf:"bytes,69,opt,name=caidWithFactors,proto3" json:"caidWithFactors,omitempty"`
	WxInstalled     bool              `protobuf:"varint,70,opt,name=wxInstalled,proto3" json:"wxInstalled,omitempty"`
	Mixcfw          *Mixcfw           `protobuf:"bytes,71,opt,name=mixcfw,proto3" json:"mixcfw,omitempty"`
	UpdateMark1     string            `protobuf:"bytes,74,opt,name=updateMark1,proto3" json:"updateMark1,omitempty"` // 爱奇艺专用
	// 废弃
	FileTime        string `protobuf:"bytes,64,opt,name=fileTime,proto3" json:"fileTime,omitempty"`
	DeviceBirthTime string `protobuf:"bytes,65,opt,name=deviceBirthTime,proto3" json:"deviceBirthTime,omitempty"`
	CaidVersion     string `protobuf:"bytes,66,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	Caid            string `protobuf:"bytes,67,opt,name=caid,proto3" json:"caid,omitempty"`
	Paid            string `protobuf:"bytes,68,opt,name=paid,proto3" json:"paid,omitempty"`
	C3              string `protobuf:"bytes,72,opt,name=c3,proto3" json:"c3,omitempty"` // caidVersion mix
	C1              string `protobuf:"bytes,73,opt,name=c1,proto3" json:"c1,omitempty"` // caid mix
}

func (x *BidRequestV2_Device) Reset() {
	*x = BidRequestV2_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Device) ProtoMessage() {}

func (x *BidRequestV2_Device) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Device.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Device) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 5}
}

func (x *BidRequestV2_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequestV2_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequestV2_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequestV2_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequestV2_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequestV2_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *BidRequestV2_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequestV2_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequestV2_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *BidRequestV2_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequestV2_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *BidRequestV2_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequestV2_Device) GetIpV6() string {
	if x != nil {
		return x.IpV6
	}
	return ""
}

func (x *BidRequestV2_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequestV2_Device) GetConnectionType() int32 {
	if x != nil {
		return x.ConnectionType
	}
	return 0
}

func (x *BidRequestV2_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequestV2_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequestV2_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequestV2_Device) GetHwv() string {
	if x != nil {
		return x.Hwv
	}
	return ""
}

func (x *BidRequestV2_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequestV2_Device) GetMccMnc() string {
	if x != nil {
		return x.MccMnc
	}
	return ""
}

func (x *BidRequestV2_Device) GetScreenHeight() int32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *BidRequestV2_Device) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *BidRequestV2_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequestV2_Device) GetGeo() *BidRequestV2_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequestV2_Device) GetAppList() string {
	if x != nil {
		return x.AppList
	}
	return ""
}

func (x *BidRequestV2_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequestV2_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequestV2_Device) GetVerCodeOfHms() string {
	if x != nil {
		return x.VerCodeOfHms
	}
	return ""
}

func (x *BidRequestV2_Device) GetVerCodeOfAG() string {
	if x != nil {
		return x.VerCodeOfAG
	}
	return ""
}

func (x *BidRequestV2_Device) GetRomVersion() string {
	if x != nil {
		return x.RomVersion
	}
	return ""
}

func (x *BidRequestV2_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *BidRequestV2_Device) GetBootTimeSec() string {
	if x != nil {
		return x.BootTimeSec
	}
	return ""
}

func (x *BidRequestV2_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *BidRequestV2_Device) GetMemorySize() int64 {
	if x != nil {
		return x.MemorySize
	}
	return 0
}

func (x *BidRequestV2_Device) GetDiskSize() int64 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *BidRequestV2_Device) GetOsUpdateTimeSec() string {
	if x != nil {
		return x.OsUpdateTimeSec
	}
	return ""
}

func (x *BidRequestV2_Device) GetModelCode() string {
	if x != nil {
		return x.ModelCode
	}
	return ""
}

func (x *BidRequestV2_Device) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *BidRequestV2_Device) GetCaidWithFactors() *CaidWithFactors {
	if x != nil {
		return x.CaidWithFactors
	}
	return nil
}

func (x *BidRequestV2_Device) GetWxInstalled() bool {
	if x != nil {
		return x.WxInstalled
	}
	return false
}

func (x *BidRequestV2_Device) GetMixcfw() *Mixcfw {
	if x != nil {
		return x.Mixcfw
	}
	return nil
}

func (x *BidRequestV2_Device) GetUpdateMark1() string {
	if x != nil {
		return x.UpdateMark1
	}
	return ""
}

func (x *BidRequestV2_Device) GetFileTime() string {
	if x != nil {
		return x.FileTime
	}
	return ""
}

func (x *BidRequestV2_Device) GetDeviceBirthTime() string {
	if x != nil {
		return x.DeviceBirthTime
	}
	return ""
}

func (x *BidRequestV2_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *BidRequestV2_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BidRequestV2_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *BidRequestV2_Device) GetC3() string {
	if x != nil {
		return x.C3
	}
	return ""
}

func (x *BidRequestV2_Device) GetC1() string {
	if x != nil {
		return x.C1
	}
	return ""
}

type BidRequestV2_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string  `protobuf:"bytes,57,opt,name=id,proto3" json:"id,omitempty"`
	BidFloor float64 `protobuf:"fixed64,58,opt,name=bidFloor,proto3" json:"bidFloor,omitempty"`
}

func (x *BidRequestV2_Deal) Reset() {
	*x = BidRequestV2_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Deal) ProtoMessage() {}

func (x *BidRequestV2_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Deal.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Deal) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 6}
}

func (x *BidRequestV2_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequestV2_Deal) GetBidFloor() float64 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

type BidRequestV2_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32   `protobuf:"varint,59,opt,name=type,proto3" json:"type,omitempty"`
	Lat     float64 `protobuf:"fixed64,60,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon     float64 `protobuf:"fixed64,61,opt,name=lon,proto3" json:"lon,omitempty"`
	Country string  `protobuf:"bytes,62,opt,name=country,proto3" json:"country,omitempty"`
}

func (x *BidRequestV2_Geo) Reset() {
	*x = BidRequestV2_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Geo) ProtoMessage() {}

func (x *BidRequestV2_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Geo.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Geo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 7}
}

func (x *BidRequestV2_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BidRequestV2_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequestV2_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequestV2_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

type BidRequestV2_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Age    int32 `protobuf:"varint,63,opt,name=age,proto3" json:"age,omitempty"`
	Gender int32 `protobuf:"varint,64,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *BidRequestV2_User) Reset() {
	*x = BidRequestV2_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_User) ProtoMessage() {}

func (x *BidRequestV2_User) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_User.ProtoReflect.Descriptor instead.
func (*BidRequestV2_User) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 8}
}

func (x *BidRequestV2_User) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *BidRequestV2_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type BidRequestV2_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkBidExt *BidRequestV2_SdkBidExt    `protobuf:"bytes,1,opt,name=sdkBidExt,proto3" json:"sdkBidExt,omitempty"`
	SdkExt    []*BidRequestV2_Ext_SDKExt `protobuf:"bytes,2,rep,name=sdkExt,proto3" json:"sdkExt,omitempty"`
}

func (x *BidRequestV2_Ext) Reset() {
	*x = BidRequestV2_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Ext) ProtoMessage() {}

func (x *BidRequestV2_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Ext.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Ext) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 9}
}

func (x *BidRequestV2_Ext) GetSdkBidExt() *BidRequestV2_SdkBidExt {
	if x != nil {
		return x.SdkBidExt
	}
	return nil
}

func (x *BidRequestV2_Ext) GetSdkExt() []*BidRequestV2_Ext_SDKExt {
	if x != nil {
		return x.SdkExt
	}
	return nil
}

type BidRequestV2_SdkBidExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	YlhBuyerId              string `protobuf:"bytes,1,opt,name=ylhBuyerId,proto3" json:"ylhBuyerId,omitempty"`                            // 优量汇SDK获取的buyerID
	YlhOpensdkVer           string `protobuf:"bytes,3,opt,name=ylhOpensdkVer,proto3" json:"ylhOpensdkVer,omitempty"`                      // 优量汇SDKopenSDK 版本，用于判断是否能支持微信小程序广告
	YlhSupportSplashZoomout bool   `protobuf:"varint,5,opt,name=ylhSupportSplashZoomout,proto3" json:"ylhSupportSplashZoomout,omitempty"` // 优量汇SDK是否支持开屏 V+
	YlhSdkInfo              string `protobuf:"bytes,6,opt,name=ylhSdkInfo,proto3" json:"ylhSdkInfo,omitempty"`                            // 优量汇SDK获取的SDKInfo，缺失时将影响广告推荐效果，强烈建议开发者全量上传
}

func (x *BidRequestV2_SdkBidExt) Reset() {
	*x = BidRequestV2_SdkBidExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_SdkBidExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_SdkBidExt) ProtoMessage() {}

func (x *BidRequestV2_SdkBidExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_SdkBidExt.ProtoReflect.Descriptor instead.
func (*BidRequestV2_SdkBidExt) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 10}
}

func (x *BidRequestV2_SdkBidExt) GetYlhBuyerId() string {
	if x != nil {
		return x.YlhBuyerId
	}
	return ""
}

func (x *BidRequestV2_SdkBidExt) GetYlhOpensdkVer() string {
	if x != nil {
		return x.YlhOpensdkVer
	}
	return ""
}

func (x *BidRequestV2_SdkBidExt) GetYlhSupportSplashZoomout() bool {
	if x != nil {
		return x.YlhSupportSplashZoomout
	}
	return false
}

func (x *BidRequestV2_SdkBidExt) GetYlhSdkInfo() string {
	if x != nil {
		return x.YlhSdkInfo
	}
	return ""
}

type BidRequestV2_Ext_SDKExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagID   string     `protobuf:"bytes,1,opt,name=tagID,proto3" json:"tagID,omitempty"`
	SdkType string     `protobuf:"bytes,2,opt,name=sdkType,proto3" json:"sdkType,omitempty"`
	SdkInfo []*SDKInfo `protobuf:"bytes,3,rep,name=sdkInfo,proto3" json:"sdkInfo,omitempty"`
}

func (x *BidRequestV2_Ext_SDKExt) Reset() {
	*x = BidRequestV2_Ext_SDKExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequestV2_Ext_SDKExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequestV2_Ext_SDKExt) ProtoMessage() {}

func (x *BidRequestV2_Ext_SDKExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequestV2_Ext_SDKExt.ProtoReflect.Descriptor instead.
func (*BidRequestV2_Ext_SDKExt) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{9, 9, 0}
}

func (x *BidRequestV2_Ext_SDKExt) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *BidRequestV2_Ext_SDKExt) GetSdkType() string {
	if x != nil {
		return x.SdkType
	}
	return ""
}

func (x *BidRequestV2_Ext_SDKExt) GetSdkInfo() []*SDKInfo {
	if x != nil {
		return x.SdkInfo
	}
	return nil
}

type BidResponseV2_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidList []*BidResponseV2_Bid `protobuf:"bytes,68,rep,name=bidList,proto3" json:"bidList,omitempty"`
}

func (x *BidResponseV2_SeatBid) Reset() {
	*x = BidResponseV2_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_SeatBid) ProtoMessage() {}

func (x *BidResponseV2_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponseV2_SeatBid) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 0}
}

func (x *BidResponseV2_SeatBid) GetBidList() []*BidResponseV2_Bid {
	if x != nil {
		return x.BidList
	}
	return nil
}

type BidResponseV2_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImpID        string                      `protobuf:"bytes,69,opt,name=impID,proto3" json:"impID,omitempty"`
	AdType       int32                       `protobuf:"varint,70,opt,name=adType,proto3" json:"adType,omitempty"`
	AdStyle      int32                       `protobuf:"varint,71,opt,name=adStyle,proto3" json:"adStyle,omitempty"`
	Item         *BidResponseV2_Item         `protobuf:"bytes,72,opt,name=item,proto3" json:"item,omitempty"`
	Price        float64                     `protobuf:"fixed64,73,opt,name=price,proto3" json:"price,omitempty"`
	NURL         string                      `protobuf:"bytes,74,opt,name=nURL,proto3" json:"nURL,omitempty"`
	LURL         string                      `protobuf:"bytes,77,opt,name=lURL,proto3" json:"lURL,omitempty"`
	DealType     int64                       `protobuf:"varint,78,opt,name=dealType,proto3" json:"dealType,omitempty"`
	Crid         string                      `protobuf:"bytes,99,opt,name=crid,proto3" json:"crid,omitempty"`
	ApiName      string                      `protobuf:"bytes,104,opt,name=apiName,proto3" json:"apiName,omitempty"`
	BidTrace     *BidResponseV2_BidTrace     `protobuf:"bytes,105,opt,name=bidTrace,proto3" json:"bidTrace,omitempty"`
	InnerData    *BidResponseV2_InnerData    `protobuf:"bytes,106,opt,name=innerData,proto3" json:"innerData,omitempty"`
	Cid          string                      `protobuf:"bytes,107,opt,name=cid,proto3" json:"cid,omitempty"`
	ShakeSetting *BidResponseV2_ShakeSetting `protobuf:"bytes,600,opt,name=shakeSetting,proto3" json:"shakeSetting,omitempty"`
	LogoURL      string                      `protobuf:"bytes,601,opt,name=logoURL,proto3" json:"logoURL,omitempty"`
}

func (x *BidResponseV2_Bid) Reset() {
	*x = BidResponseV2_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_Bid) ProtoMessage() {}

func (x *BidResponseV2_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_Bid.ProtoReflect.Descriptor instead.
func (*BidResponseV2_Bid) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 1}
}

func (x *BidResponseV2_Bid) GetImpID() string {
	if x != nil {
		return x.ImpID
	}
	return ""
}

func (x *BidResponseV2_Bid) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidResponseV2_Bid) GetAdStyle() int32 {
	if x != nil {
		return x.AdStyle
	}
	return 0
}

func (x *BidResponseV2_Bid) GetItem() *BidResponseV2_Item {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *BidResponseV2_Bid) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponseV2_Bid) GetNURL() string {
	if x != nil {
		return x.NURL
	}
	return ""
}

func (x *BidResponseV2_Bid) GetLURL() string {
	if x != nil {
		return x.LURL
	}
	return ""
}

func (x *BidResponseV2_Bid) GetDealType() int64 {
	if x != nil {
		return x.DealType
	}
	return 0
}

func (x *BidResponseV2_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *BidResponseV2_Bid) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *BidResponseV2_Bid) GetBidTrace() *BidResponseV2_BidTrace {
	if x != nil {
		return x.BidTrace
	}
	return nil
}

func (x *BidResponseV2_Bid) GetInnerData() *BidResponseV2_InnerData {
	if x != nil {
		return x.InnerData
	}
	return nil
}

func (x *BidResponseV2_Bid) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *BidResponseV2_Bid) GetShakeSetting() *BidResponseV2_ShakeSetting {
	if x != nil {
		return x.ShakeSetting
	}
	return nil
}

func (x *BidResponseV2_Bid) GetLogoURL() string {
	if x != nil {
		return x.LogoURL
	}
	return ""
}

type BidResponseV2_InnerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InteractionType   []int32 `protobuf:"varint,107,rep,packed,name=interactionType,proto3" json:"interactionType,omitempty"`
	StrategyId        string  `protobuf:"bytes,108,opt,name=StrategyId,proto3" json:"StrategyId,omitempty"`
	TagId             string  `protobuf:"bytes,109,opt,name=TagId,proto3" json:"TagId,omitempty"`
	Sensitivity       string  `protobuf:"bytes,110,opt,name=Sensitivity,proto3" json:"Sensitivity,omitempty"`
	SensitivityNum    int64   `protobuf:"varint,111,opt,name=SensitivityNum,proto3" json:"SensitivityNum,omitempty"`
	ExpectClickRatio  float32 `protobuf:"fixed32,112,opt,name=ExpectClickRatio,proto3" json:"ExpectClickRatio,omitempty"`
	Batch             string  `protobuf:"bytes,113,opt,name=Batch,proto3" json:"Batch,omitempty"`
	ExposureTimestamp int64   `protobuf:"varint,114,opt,name=ExposureTimestamp,proto3" json:"ExposureTimestamp,omitempty"`
	RequestId         string  `protobuf:"bytes,115,opt,name=RequestId,proto3" json:"RequestId,omitempty"`
}

func (x *BidResponseV2_InnerData) Reset() {
	*x = BidResponseV2_InnerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_InnerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_InnerData) ProtoMessage() {}

func (x *BidResponseV2_InnerData) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_InnerData.ProtoReflect.Descriptor instead.
func (*BidResponseV2_InnerData) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 2}
}

func (x *BidResponseV2_InnerData) GetInteractionType() []int32 {
	if x != nil {
		return x.InteractionType
	}
	return nil
}

func (x *BidResponseV2_InnerData) GetStrategyId() string {
	if x != nil {
		return x.StrategyId
	}
	return ""
}

func (x *BidResponseV2_InnerData) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidResponseV2_InnerData) GetSensitivity() string {
	if x != nil {
		return x.Sensitivity
	}
	return ""
}

func (x *BidResponseV2_InnerData) GetSensitivityNum() int64 {
	if x != nil {
		return x.SensitivityNum
	}
	return 0
}

func (x *BidResponseV2_InnerData) GetExpectClickRatio() float32 {
	if x != nil {
		return x.ExpectClickRatio
	}
	return 0
}

func (x *BidResponseV2_InnerData) GetBatch() string {
	if x != nil {
		return x.Batch
	}
	return ""
}

func (x *BidResponseV2_InnerData) GetExposureTimestamp() int64 {
	if x != nil {
		return x.ExposureTimestamp
	}
	return 0
}

func (x *BidResponseV2_InnerData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type BidResponseV2_ShakeSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShakeSwitch                    string `protobuf:"bytes,601,opt,name=shakeSwitch,proto3" json:"shakeSwitch,omitempty"`
	ShakeSensitivity               string `protobuf:"bytes,602,opt,name=shakeSensitivity,proto3" json:"shakeSensitivity,omitempty"`
	ShakeOneOrTwoSides             int64  `protobuf:"varint,603,opt,name=shake_one_or_two_sides,json=shakeOneOrTwoSides,proto3" json:"shake_one_or_two_sides,omitempty"`
	UseNewShakeConfig              bool   `protobuf:"varint,604,opt,name=use_new_shake_config,json=useNewShakeConfig,proto3" json:"use_new_shake_config,omitempty"`
	ShakeSensitivityDensity        int64  `protobuf:"varint,605,opt,name=shake_sensitivity_density,json=shakeSensitivityDensity,proto3" json:"shake_sensitivity_density,omitempty"`
	ShakeAccelerationMaxX          int64  `protobuf:"varint,606,opt,name=shake_acceleration_max_x,json=shakeAccelerationMaxX,proto3" json:"shake_acceleration_max_x,omitempty"`
	ShakeAccelerationMaxY          int64  `protobuf:"varint,607,opt,name=shake_acceleration_max_y,json=shakeAccelerationMaxY,proto3" json:"shake_acceleration_max_y,omitempty"`
	ShakeAccelerationMaxZ          int64  `protobuf:"varint,608,opt,name=shake_acceleration_max_z,json=shakeAccelerationMaxZ,proto3" json:"shake_acceleration_max_z,omitempty"`
	ShakeClickSensitivityLevel     int64  `protobuf:"varint,609,opt,name=shake_click_sensitivity_level,json=shakeClickSensitivityLevel,proto3" json:"shake_click_sensitivity_level,omitempty"`
	ShakeClickTimeLengthMin        int64  `protobuf:"varint,610,opt,name=shake_click_time_length_min,json=shakeClickTimeLengthMin,proto3" json:"shake_click_time_length_min,omitempty"`
	TimeCountShakeSensitivityLevel int64  `protobuf:"varint,611,opt,name=time_count_shake_sensitivity_level,json=timeCountShakeSensitivityLevel,proto3" json:"time_count_shake_sensitivity_level,omitempty"`
	ShakeSensorCallbackInterval    int64  `protobuf:"varint,612,opt,name=shake_sensor_callback_interval,json=shakeSensorCallbackInterval,proto3" json:"shake_sensor_callback_interval,omitempty"`
}

func (x *BidResponseV2_ShakeSetting) Reset() {
	*x = BidResponseV2_ShakeSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_ShakeSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_ShakeSetting) ProtoMessage() {}

func (x *BidResponseV2_ShakeSetting) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_ShakeSetting.ProtoReflect.Descriptor instead.
func (*BidResponseV2_ShakeSetting) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 3}
}

func (x *BidResponseV2_ShakeSetting) GetShakeSwitch() string {
	if x != nil {
		return x.ShakeSwitch
	}
	return ""
}

func (x *BidResponseV2_ShakeSetting) GetShakeSensitivity() string {
	if x != nil {
		return x.ShakeSensitivity
	}
	return ""
}

func (x *BidResponseV2_ShakeSetting) GetShakeOneOrTwoSides() int64 {
	if x != nil {
		return x.ShakeOneOrTwoSides
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetUseNewShakeConfig() bool {
	if x != nil {
		return x.UseNewShakeConfig
	}
	return false
}

func (x *BidResponseV2_ShakeSetting) GetShakeSensitivityDensity() int64 {
	if x != nil {
		return x.ShakeSensitivityDensity
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeAccelerationMaxX() int64 {
	if x != nil {
		return x.ShakeAccelerationMaxX
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeAccelerationMaxY() int64 {
	if x != nil {
		return x.ShakeAccelerationMaxY
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeAccelerationMaxZ() int64 {
	if x != nil {
		return x.ShakeAccelerationMaxZ
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeClickSensitivityLevel() int64 {
	if x != nil {
		return x.ShakeClickSensitivityLevel
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeClickTimeLengthMin() int64 {
	if x != nil {
		return x.ShakeClickTimeLengthMin
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetTimeCountShakeSensitivityLevel() int64 {
	if x != nil {
		return x.TimeCountShakeSensitivityLevel
	}
	return 0
}

func (x *BidResponseV2_ShakeSetting) GetShakeSensorCallbackInterval() int64 {
	if x != nil {
		return x.ShakeSensorCallbackInterval
	}
	return 0
}

type BidResponseV2_BidTrace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrafficGroupID        string  `protobuf:"bytes,1,opt,name=trafficGroupID,proto3" json:"trafficGroupID,omitempty"`
	TrafficID             string  `protobuf:"bytes,2,opt,name=trafficID,proto3" json:"trafficID,omitempty"`
	AdGroupID             string  `protobuf:"bytes,3,opt,name=adGroupID,proto3" json:"adGroupID,omitempty"`
	AdPositionID          string  `protobuf:"bytes,4,opt,name=adPositionID,proto3" json:"adPositionID,omitempty"`
	TagID                 string  `protobuf:"bytes,5,opt,name=tagID,proto3" json:"tagID,omitempty"`
	DspName               string  `protobuf:"bytes,6,opt,name=dspName,proto3" json:"dspName,omitempty"`
	DspID                 string  `protobuf:"bytes,7,opt,name=dspID,proto3" json:"dspID,omitempty"`
	InteractionTypes      []int64 `protobuf:"varint,8,rep,packed,name=interactionTypes,proto3" json:"interactionTypes,omitempty"`
	InteractionStrategyID string  `protobuf:"bytes,9,opt,name=interactionStrategyID,proto3" json:"interactionStrategyID,omitempty"`
}

func (x *BidResponseV2_BidTrace) Reset() {
	*x = BidResponseV2_BidTrace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_BidTrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_BidTrace) ProtoMessage() {}

func (x *BidResponseV2_BidTrace) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_BidTrace.ProtoReflect.Descriptor instead.
func (*BidResponseV2_BidTrace) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 4}
}

func (x *BidResponseV2_BidTrace) GetTrafficGroupID() string {
	if x != nil {
		return x.TrafficGroupID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetTrafficID() string {
	if x != nil {
		return x.TrafficID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetAdGroupID() string {
	if x != nil {
		return x.AdGroupID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetAdPositionID() string {
	if x != nil {
		return x.AdPositionID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetDspName() string {
	if x != nil {
		return x.DspName
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetDspID() string {
	if x != nil {
		return x.DspID
	}
	return ""
}

func (x *BidResponseV2_BidTrace) GetInteractionTypes() []int64 {
	if x != nil {
		return x.InteractionTypes
	}
	return nil
}

func (x *BidResponseV2_BidTrace) GetInteractionStrategyID() string {
	if x != nil {
		return x.InteractionStrategyID
	}
	return ""
}

type BidResponseV2_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                          string                         `protobuf:"bytes,75,opt,name=title,proto3" json:"title,omitempty"`
	Desc                           string                         `protobuf:"bytes,76,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                           string                         `protobuf:"bytes,77,opt,name=icon,proto3" json:"icon,omitempty"`
	Html                           string                         `protobuf:"bytes,78,opt,name=html,proto3" json:"html,omitempty"`
	MediaStyle                     int32                          `protobuf:"varint,79,opt,name=mediaStyle,proto3" json:"mediaStyle,omitempty"`
	DownloadURL                    string                         `protobuf:"bytes,80,opt,name=downloadURL,proto3" json:"downloadURL,omitempty"`
	DownloadAppInfo                *BidResponseV2_DownloadAppInfo `protobuf:"bytes,81,opt,name=downloadAppInfo,proto3" json:"downloadAppInfo,omitempty"`
	ClickURL                       string                         `protobuf:"bytes,82,opt,name=clickURL,proto3" json:"clickURL,omitempty"`
	DplURL                         string                         `protobuf:"bytes,83,opt,name=dplURL,proto3" json:"dplURL,omitempty"`
	ImgList                        []string                       `protobuf:"bytes,84,rep,name=imgList,proto3" json:"imgList,omitempty"`
	ExposalURLList                 []string                       `protobuf:"bytes,85,rep,name=exposalURLList,proto3" json:"exposalURLList,omitempty"`
	ClickMonitorURLList            []string                       `protobuf:"bytes,86,rep,name=clickMonitorURLList,proto3" json:"clickMonitorURLList,omitempty"`
	Video                          *BidResponseV2_Video           `protobuf:"bytes,87,opt,name=video,proto3" json:"video,omitempty"`
	MiniProgramID                  string                         `protobuf:"bytes,88,opt,name=miniProgramID,proto3" json:"miniProgramID,omitempty"`
	MiniProgramPath                string                         `protobuf:"bytes,89,opt,name=miniProgramPath,proto3" json:"miniProgramPath,omitempty"`
	MiniProgramType                int32                          `protobuf:"varint,90,opt,name=miniProgramType,proto3" json:"miniProgramType,omitempty"`
	MiniProgramExtData             string                         `protobuf:"bytes,99,opt,name=miniProgramExtData,proto3" json:"miniProgramExtData,omitempty"`
	MiniProgramSuccessTrackURLList []string                       `protobuf:"bytes,91,rep,name=miniProgramSuccessTrackURLList,proto3" json:"miniProgramSuccessTrackURLList,omitempty"`
	DownloadTrackURLList           []string                       `protobuf:"bytes,92,rep,name=downloadTrackURLList,proto3" json:"downloadTrackURLList,omitempty"`
	DownloadedTrackURLList         []string                       `protobuf:"bytes,93,rep,name=downloadedTrackURLList,proto3" json:"downloadedTrackURLList,omitempty"`
	InstalledTrackURLList          []string                       `protobuf:"bytes,94,rep,name=installedTrackURLList,proto3" json:"installedTrackURLList,omitempty"`
	DpSuccessTrackURLList          []string                       `protobuf:"bytes,95,rep,name=dpSuccessTrackURLList,proto3" json:"dpSuccessTrackURLList,omitempty"`
	ActionTrackURLList             []string                       `protobuf:"bytes,96,rep,name=actionTrackURLList,proto3" json:"actionTrackURLList,omitempty"`
	PackageName                    string                         `protobuf:"bytes,97,opt,name=packageName,proto3" json:"packageName,omitempty"`
	MonitorURLList                 []*BidResponseV2_MonitorURL    `protobuf:"bytes,98,rep,name=monitorURLList,proto3" json:"monitorURLList,omitempty"`
}

func (x *BidResponseV2_Item) Reset() {
	*x = BidResponseV2_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_Item) ProtoMessage() {}

func (x *BidResponseV2_Item) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_Item.ProtoReflect.Descriptor instead.
func (*BidResponseV2_Item) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 5}
}

func (x *BidResponseV2_Item) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponseV2_Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponseV2_Item) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponseV2_Item) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

func (x *BidResponseV2_Item) GetMediaStyle() int32 {
	if x != nil {
		return x.MediaStyle
	}
	return 0
}

func (x *BidResponseV2_Item) GetDownloadURL() string {
	if x != nil {
		return x.DownloadURL
	}
	return ""
}

func (x *BidResponseV2_Item) GetDownloadAppInfo() *BidResponseV2_DownloadAppInfo {
	if x != nil {
		return x.DownloadAppInfo
	}
	return nil
}

func (x *BidResponseV2_Item) GetClickURL() string {
	if x != nil {
		return x.ClickURL
	}
	return ""
}

func (x *BidResponseV2_Item) GetDplURL() string {
	if x != nil {
		return x.DplURL
	}
	return ""
}

func (x *BidResponseV2_Item) GetImgList() []string {
	if x != nil {
		return x.ImgList
	}
	return nil
}

func (x *BidResponseV2_Item) GetExposalURLList() []string {
	if x != nil {
		return x.ExposalURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetClickMonitorURLList() []string {
	if x != nil {
		return x.ClickMonitorURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetVideo() *BidResponseV2_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponseV2_Item) GetMiniProgramID() string {
	if x != nil {
		return x.MiniProgramID
	}
	return ""
}

func (x *BidResponseV2_Item) GetMiniProgramPath() string {
	if x != nil {
		return x.MiniProgramPath
	}
	return ""
}

func (x *BidResponseV2_Item) GetMiniProgramType() int32 {
	if x != nil {
		return x.MiniProgramType
	}
	return 0
}

func (x *BidResponseV2_Item) GetMiniProgramExtData() string {
	if x != nil {
		return x.MiniProgramExtData
	}
	return ""
}

func (x *BidResponseV2_Item) GetMiniProgramSuccessTrackURLList() []string {
	if x != nil {
		return x.MiniProgramSuccessTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetDownloadTrackURLList() []string {
	if x != nil {
		return x.DownloadTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetDownloadedTrackURLList() []string {
	if x != nil {
		return x.DownloadedTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetInstalledTrackURLList() []string {
	if x != nil {
		return x.InstalledTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetDpSuccessTrackURLList() []string {
	if x != nil {
		return x.DpSuccessTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetActionTrackURLList() []string {
	if x != nil {
		return x.ActionTrackURLList
	}
	return nil
}

func (x *BidResponseV2_Item) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *BidResponseV2_Item) GetMonitorURLList() []*BidResponseV2_MonitorURL {
	if x != nil {
		return x.MonitorURLList
	}
	return nil
}

type BidResponseV2_MonitorURL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventType string   `protobuf:"bytes,1,opt,name=eventType,proto3" json:"eventType,omitempty"`
	URLList   []string `protobuf:"bytes,2,rep,name=URLList,proto3" json:"URLList,omitempty"`
}

func (x *BidResponseV2_MonitorURL) Reset() {
	*x = BidResponseV2_MonitorURL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_MonitorURL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_MonitorURL) ProtoMessage() {}

func (x *BidResponseV2_MonitorURL) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_MonitorURL.ProtoReflect.Descriptor instead.
func (*BidResponseV2_MonitorURL) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 6}
}

func (x *BidResponseV2_MonitorURL) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *BidResponseV2_MonitorURL) GetURLList() []string {
	if x != nil {
		return x.URLList
	}
	return nil
}

type BidResponseV2_DownloadAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,88,opt,name=appName,proto3" json:"appName,omitempty"`
	Developer  string `protobuf:"bytes,89,opt,name=developer,proto3" json:"developer,omitempty"`
	Version    string `protobuf:"bytes,90,opt,name=version,proto3" json:"version,omitempty"`
	PacketSize string `protobuf:"bytes,91,opt,name=packetSize,proto3" json:"packetSize,omitempty"`
	Privacy    string `protobuf:"bytes,92,opt,name=privacy,proto3" json:"privacy,omitempty"`
	Permission string `protobuf:"bytes,93,opt,name=permission,proto3" json:"permission,omitempty"`
	Desc       string `protobuf:"bytes,101,opt,name=desc,proto3" json:"desc,omitempty"`
	DescURL    string `protobuf:"bytes,102,opt,name=descURL,proto3" json:"descURL,omitempty"`
}

func (x *BidResponseV2_DownloadAppInfo) Reset() {
	*x = BidResponseV2_DownloadAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_DownloadAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_DownloadAppInfo) ProtoMessage() {}

func (x *BidResponseV2_DownloadAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_DownloadAppInfo.ProtoReflect.Descriptor instead.
func (*BidResponseV2_DownloadAppInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 7}
}

func (x *BidResponseV2_DownloadAppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetPacketSize() string {
	if x != nil {
		return x.PacketSize
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetPrivacy() string {
	if x != nil {
		return x.Privacy
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponseV2_DownloadAppInfo) GetDescURL() string {
	if x != nil {
		return x.DescURL
	}
	return ""
}

type BidResponseV2_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoURL       string `protobuf:"bytes,94,opt,name=videoURL,proto3" json:"videoURL,omitempty"`
	VideoDuration  int32  `protobuf:"varint,95,opt,name=videoDuration,proto3" json:"videoDuration,omitempty"`
	VideoStartURL  string `protobuf:"bytes,96,opt,name=videoStartURL,proto3" json:"videoStartURL,omitempty"`
	VideoFinishURL string `protobuf:"bytes,97,opt,name=videoFinishURL,proto3" json:"videoFinishURL,omitempty"`
	VideoVastXML   string `protobuf:"bytes,98,opt,name=videoVastXML,proto3" json:"videoVastXML,omitempty"`
	VideoEndImgURL string `protobuf:"bytes,99,opt,name=videoEndImgURL,proto3" json:"videoEndImgURL,omitempty"`
	VideoPreImgURL string `protobuf:"bytes,100,opt,name=videoPreImgURL,proto3" json:"videoPreImgURL,omitempty"`
}

func (x *BidResponseV2_Video) Reset() {
	*x = BidResponseV2_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_Video) ProtoMessage() {}

func (x *BidResponseV2_Video) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_Video.ProtoReflect.Descriptor instead.
func (*BidResponseV2_Video) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 8}
}

func (x *BidResponseV2_Video) GetVideoURL() string {
	if x != nil {
		return x.VideoURL
	}
	return ""
}

func (x *BidResponseV2_Video) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *BidResponseV2_Video) GetVideoStartURL() string {
	if x != nil {
		return x.VideoStartURL
	}
	return ""
}

func (x *BidResponseV2_Video) GetVideoFinishURL() string {
	if x != nil {
		return x.VideoFinishURL
	}
	return ""
}

func (x *BidResponseV2_Video) GetVideoVastXML() string {
	if x != nil {
		return x.VideoVastXML
	}
	return ""
}

func (x *BidResponseV2_Video) GetVideoEndImgURL() string {
	if x != nil {
		return x.VideoEndImgURL
	}
	return ""
}

func (x *BidResponseV2_Video) GetVideoPreImgURL() string {
	if x != nil {
		return x.VideoPreImgURL
	}
	return ""
}

type BidResponseV2_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkExt []*BidResponseV2_Ext_SDKExt `protobuf:"bytes,1,rep,name=sdkExt,proto3" json:"sdkExt,omitempty"`
}

func (x *BidResponseV2_Ext) Reset() {
	*x = BidResponseV2_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_Ext) ProtoMessage() {}

func (x *BidResponseV2_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_Ext.ProtoReflect.Descriptor instead.
func (*BidResponseV2_Ext) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 9}
}

func (x *BidResponseV2_Ext) GetSdkExt() []*BidResponseV2_Ext_SDKExt {
	if x != nil {
		return x.SdkExt
	}
	return nil
}

type BidResponseV2_Ext_SDKExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkType string     `protobuf:"bytes,1,opt,name=sdkType,proto3" json:"sdkType,omitempty"`
	DspID   string     `protobuf:"bytes,2,opt,name=dspID,proto3" json:"dspID,omitempty"`
	TagID   string     `protobuf:"bytes,3,opt,name=tagID,proto3" json:"tagID,omitempty"`
	Price   float64    `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	SdkInfo []*SDKInfo `protobuf:"bytes,5,rep,name=sdkInfo,proto3" json:"sdkInfo,omitempty"`
}

func (x *BidResponseV2_Ext_SDKExt) Reset() {
	*x = BidResponseV2_Ext_SDKExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponseV2_Ext_SDKExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponseV2_Ext_SDKExt) ProtoMessage() {}

func (x *BidResponseV2_Ext_SDKExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponseV2_Ext_SDKExt.ProtoReflect.Descriptor instead.
func (*BidResponseV2_Ext_SDKExt) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{11, 9, 0}
}

func (x *BidResponseV2_Ext_SDKExt) GetSdkType() string {
	if x != nil {
		return x.SdkType
	}
	return ""
}

func (x *BidResponseV2_Ext_SDKExt) GetDspID() string {
	if x != nil {
		return x.DspID
	}
	return ""
}

func (x *BidResponseV2_Ext_SDKExt) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *BidResponseV2_Ext_SDKExt) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponseV2_Ext_SDKExt) GetSdkInfo() []*SDKInfo {
	if x != nil {
		return x.SdkInfo
	}
	return nil
}

type AdPosRequest_PromotionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivationType    string  `protobuf:"bytes,1,opt,name=activationType,proto3" json:"activationType,omitempty"`
	UtmSource         string  `protobuf:"bytes,2,opt,name=utmSource,proto3" json:"utmSource,omitempty"`
	ActivationChannel string  `protobuf:"bytes,3,opt,name=activationChannel,proto3" json:"activationChannel,omitempty"`
	AccountId         string  `protobuf:"bytes,4,opt,name=accountId,proto3" json:"accountId,omitempty"`
	ActivateInterval  float32 `protobuf:"fixed32,5,opt,name=activateInterval,proto3" json:"activateInterval,omitempty"`
}

func (x *AdPosRequest_PromotionInfo) Reset() {
	*x = AdPosRequest_PromotionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosRequest_PromotionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosRequest_PromotionInfo) ProtoMessage() {}

func (x *AdPosRequest_PromotionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosRequest_PromotionInfo.ProtoReflect.Descriptor instead.
func (*AdPosRequest_PromotionInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{20, 0}
}

func (x *AdPosRequest_PromotionInfo) GetActivationType() string {
	if x != nil {
		return x.ActivationType
	}
	return ""
}

func (x *AdPosRequest_PromotionInfo) GetUtmSource() string {
	if x != nil {
		return x.UtmSource
	}
	return ""
}

func (x *AdPosRequest_PromotionInfo) GetActivationChannel() string {
	if x != nil {
		return x.ActivationChannel
	}
	return ""
}

func (x *AdPosRequest_PromotionInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AdPosRequest_PromotionInfo) GetActivateInterval() float32 {
	if x != nil {
		return x.ActivateInterval
	}
	return 0
}

type AdPosRequest_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VersionCode     int64   `protobuf:"varint,1,opt,name=versionCode,proto3" json:"versionCode,omitempty"`
	Brand           string  `protobuf:"bytes,2,opt,name=brand,proto3" json:"brand,omitempty"`
	DeviceId        string  `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	CustomParameter string  `protobuf:"bytes,4,opt,name=customParameter,proto3" json:"customParameter,omitempty"`
	Arpu            float32 `protobuf:"fixed32,5,opt,name=arpu,proto3" json:"arpu,omitempty"`
	Os              int64   `protobuf:"varint,6,opt,name=os,proto3" json:"os,omitempty"`
}

func (x *AdPosRequest_UserInfo) Reset() {
	*x = AdPosRequest_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosRequest_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosRequest_UserInfo) ProtoMessage() {}

func (x *AdPosRequest_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosRequest_UserInfo.ProtoReflect.Descriptor instead.
func (*AdPosRequest_UserInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{20, 1}
}

func (x *AdPosRequest_UserInfo) GetVersionCode() int64 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *AdPosRequest_UserInfo) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *AdPosRequest_UserInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AdPosRequest_UserInfo) GetCustomParameter() string {
	if x != nil {
		return x.CustomParameter
	}
	return ""
}

func (x *AdPosRequest_UserInfo) GetArpu() float32 {
	if x != nil {
		return x.Arpu
	}
	return 0
}

func (x *AdPosRequest_UserInfo) GetOs() int64 {
	if x != nil {
		return x.Os
	}
	return 0
}

type AdPosResponse_ExposureInterval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start    int64   `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End      int64   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	Interval float32 `protobuf:"fixed32,3,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *AdPosResponse_ExposureInterval) Reset() {
	*x = AdPosResponse_ExposureInterval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosResponse_ExposureInterval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosResponse_ExposureInterval) ProtoMessage() {}

func (x *AdPosResponse_ExposureInterval) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosResponse_ExposureInterval.ProtoReflect.Descriptor instead.
func (*AdPosResponse_ExposureInterval) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{21, 0}
}

func (x *AdPosResponse_ExposureInterval) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *AdPosResponse_ExposureInterval) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *AdPosResponse_ExposureInterval) GetInterval() float32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type AdPosResponse_ExposureStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId   string                            `protobuf:"bytes,1,opt,name=strategyId,proto3" json:"strategyId,omitempty"`
	StrategyName string                            `protobuf:"bytes,2,opt,name=strategyName,proto3" json:"strategyName,omitempty"`
	AdPosList    []string                          `protobuf:"bytes,3,rep,name=adPosList,proto3" json:"adPosList,omitempty"`
	TotalTimes   int64                             `protobuf:"varint,4,opt,name=totalTimes,proto3" json:"totalTimes,omitempty"`
	Intervals    []*AdPosResponse_ExposureInterval `protobuf:"bytes,5,rep,name=intervals,proto3" json:"intervals,omitempty"`
}

func (x *AdPosResponse_ExposureStrategy) Reset() {
	*x = AdPosResponse_ExposureStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosResponse_ExposureStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosResponse_ExposureStrategy) ProtoMessage() {}

func (x *AdPosResponse_ExposureStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosResponse_ExposureStrategy.ProtoReflect.Descriptor instead.
func (*AdPosResponse_ExposureStrategy) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{21, 1}
}

func (x *AdPosResponse_ExposureStrategy) GetStrategyId() string {
	if x != nil {
		return x.StrategyId
	}
	return ""
}

func (x *AdPosResponse_ExposureStrategy) GetStrategyName() string {
	if x != nil {
		return x.StrategyName
	}
	return ""
}

func (x *AdPosResponse_ExposureStrategy) GetAdPosList() []string {
	if x != nil {
		return x.AdPosList
	}
	return nil
}

func (x *AdPosResponse_ExposureStrategy) GetTotalTimes() int64 {
	if x != nil {
		return x.TotalTimes
	}
	return 0
}

func (x *AdPosResponse_ExposureStrategy) GetIntervals() []*AdPosResponse_ExposureInterval {
	if x != nil {
		return x.Intervals
	}
	return nil
}

type AdPosResponseSdkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagID   string `protobuf:"bytes,1,opt,name=tagID,proto3" json:"tagID,omitempty"`
	DspID   string `protobuf:"bytes,2,opt,name=dspID,proto3" json:"dspID,omitempty"`
	SdkType string `protobuf:"bytes,3,opt,name=sdkType,proto3" json:"sdkType,omitempty"`
	AdType  int64  `protobuf:"varint,4,opt,name=adType,proto3" json:"adType,omitempty"`
}

func (x *AdPosResponseSdkInfo) Reset() {
	*x = AdPosResponseSdkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdPosResponseSdkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdPosResponseSdkInfo) ProtoMessage() {}

func (x *AdPosResponseSdkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdPosResponseSdkInfo.ProtoReflect.Descriptor instead.
func (*AdPosResponseSdkInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{21, 2}
}

func (x *AdPosResponseSdkInfo) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *AdPosResponseSdkInfo) GetDspID() string {
	if x != nil {
		return x.DspID
	}
	return ""
}

func (x *AdPosResponseSdkInfo) GetSdkType() string {
	if x != nil {
		return x.SdkType
	}
	return ""
}

func (x *AdPosResponseSdkInfo) GetAdType() int64 {
	if x != nil {
		return x.AdType
	}
	return 0
}

type ClientBiddingResponse_ClientBiddingTagList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime int64                                                 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	Level     int64                                                 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	TagList   []*ClientBiddingResponse_ClientBiddingTagList_TagList `protobuf:"bytes,3,rep,name=tagList,proto3" json:"tagList,omitempty"`
}

func (x *ClientBiddingResponse_ClientBiddingTagList) Reset() {
	*x = ClientBiddingResponse_ClientBiddingTagList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientBiddingResponse_ClientBiddingTagList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientBiddingResponse_ClientBiddingTagList) ProtoMessage() {}

func (x *ClientBiddingResponse_ClientBiddingTagList) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientBiddingResponse_ClientBiddingTagList.ProtoReflect.Descriptor instead.
func (*ClientBiddingResponse_ClientBiddingTagList) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ClientBiddingResponse_ClientBiddingTagList) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ClientBiddingResponse_ClientBiddingTagList) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ClientBiddingResponse_ClientBiddingTagList) GetTagList() []*ClientBiddingResponse_ClientBiddingTagList_TagList {
	if x != nil {
		return x.TagList
	}
	return nil
}

type ClientBiddingResponse_ClientBiddingTagList_TagList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkType  string  `protobuf:"bytes,1,opt,name=sdkType,proto3" json:"sdkType,omitempty"`
	DspID    string  `protobuf:"bytes,2,opt,name=dspID,proto3" json:"dspID,omitempty"`
	TagID    string  `protobuf:"bytes,3,opt,name=tagID,proto3" json:"tagID,omitempty"`
	DealType int64   `protobuf:"varint,4,opt,name=dealType,proto3" json:"dealType,omitempty"`
	Price    float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) Reset() {
	*x = ClientBiddingResponse_ClientBiddingTagList_TagList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientBiddingResponse_ClientBiddingTagList_TagList) ProtoMessage() {}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientBiddingResponse_ClientBiddingTagList_TagList.ProtoReflect.Descriptor instead.
func (*ClientBiddingResponse_ClientBiddingTagList_TagList) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{24, 0, 0}
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) GetSdkType() string {
	if x != nil {
		return x.SdkType
	}
	return ""
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) GetDspID() string {
	if x != nil {
		return x.DspID
	}
	return ""
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) GetDealType() int64 {
	if x != nil {
		return x.DealType
	}
	return 0
}

func (x *ClientBiddingResponse_ClientBiddingTagList_TagList) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type ExposureStrategyRequest_PromotionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivationType    string  `protobuf:"bytes,1,opt,name=activationType,proto3" json:"activationType,omitempty"`
	UtmSource         string  `protobuf:"bytes,2,opt,name=utmSource,proto3" json:"utmSource,omitempty"`
	ActivationChannel string  `protobuf:"bytes,3,opt,name=activationChannel,proto3" json:"activationChannel,omitempty"`
	AccountId         string  `protobuf:"bytes,4,opt,name=accountId,proto3" json:"accountId,omitempty"`
	ActivateInterval  float32 `protobuf:"fixed32,5,opt,name=activateInterval,proto3" json:"activateInterval,omitempty"`
}

func (x *ExposureStrategyRequest_PromotionInfo) Reset() {
	*x = ExposureStrategyRequest_PromotionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyRequest_PromotionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyRequest_PromotionInfo) ProtoMessage() {}

func (x *ExposureStrategyRequest_PromotionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyRequest_PromotionInfo.ProtoReflect.Descriptor instead.
func (*ExposureStrategyRequest_PromotionInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{25, 0}
}

func (x *ExposureStrategyRequest_PromotionInfo) GetActivationType() string {
	if x != nil {
		return x.ActivationType
	}
	return ""
}

func (x *ExposureStrategyRequest_PromotionInfo) GetUtmSource() string {
	if x != nil {
		return x.UtmSource
	}
	return ""
}

func (x *ExposureStrategyRequest_PromotionInfo) GetActivationChannel() string {
	if x != nil {
		return x.ActivationChannel
	}
	return ""
}

func (x *ExposureStrategyRequest_PromotionInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ExposureStrategyRequest_PromotionInfo) GetActivateInterval() float32 {
	if x != nil {
		return x.ActivateInterval
	}
	return 0
}

type ExposureStrategyRequest_PosIdExposured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PosId          string `protobuf:"bytes,1,opt,name=posId,proto3" json:"posId,omitempty"`
	ExposuredNum   int64  `protobuf:"varint,2,opt,name=exposuredNum,proto3" json:"exposuredNum,omitempty"`
	LastExposureMs int64  `protobuf:"varint,3,opt,name=lastExposureMs,proto3" json:"lastExposureMs,omitempty"`
}

func (x *ExposureStrategyRequest_PosIdExposured) Reset() {
	*x = ExposureStrategyRequest_PosIdExposured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyRequest_PosIdExposured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyRequest_PosIdExposured) ProtoMessage() {}

func (x *ExposureStrategyRequest_PosIdExposured) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyRequest_PosIdExposured.ProtoReflect.Descriptor instead.
func (*ExposureStrategyRequest_PosIdExposured) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{25, 1}
}

func (x *ExposureStrategyRequest_PosIdExposured) GetPosId() string {
	if x != nil {
		return x.PosId
	}
	return ""
}

func (x *ExposureStrategyRequest_PosIdExposured) GetExposuredNum() int64 {
	if x != nil {
		return x.ExposuredNum
	}
	return 0
}

func (x *ExposureStrategyRequest_PosIdExposured) GetLastExposureMs() int64 {
	if x != nil {
		return x.LastExposureMs
	}
	return 0
}

type ExposureStrategyRequest_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VersionCode        int64                                     `protobuf:"varint,1,opt,name=versionCode,proto3" json:"versionCode,omitempty"`
	Brand              string                                    `protobuf:"bytes,2,opt,name=brand,proto3" json:"brand,omitempty"`
	DeviceId           string                                    `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	CustomParameter    string                                    `protobuf:"bytes,4,opt,name=customParameter,proto3" json:"customParameter,omitempty"`
	Arpu               float32                                   `protobuf:"fixed32,5,opt,name=arpu,proto3" json:"arpu,omitempty"`
	Os                 int64                                     `protobuf:"varint,6,opt,name=os,proto3" json:"os,omitempty"`
	PosIdExposuredList []*ExposureStrategyRequest_PosIdExposured `protobuf:"bytes,7,rep,name=posIdExposuredList,proto3" json:"posIdExposuredList,omitempty"`
	PosId              string                                    `protobuf:"bytes,8,opt,name=posId,proto3" json:"posId,omitempty"`
}

func (x *ExposureStrategyRequest_UserInfo) Reset() {
	*x = ExposureStrategyRequest_UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyRequest_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyRequest_UserInfo) ProtoMessage() {}

func (x *ExposureStrategyRequest_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyRequest_UserInfo.ProtoReflect.Descriptor instead.
func (*ExposureStrategyRequest_UserInfo) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{25, 2}
}

func (x *ExposureStrategyRequest_UserInfo) GetVersionCode() int64 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *ExposureStrategyRequest_UserInfo) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *ExposureStrategyRequest_UserInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ExposureStrategyRequest_UserInfo) GetCustomParameter() string {
	if x != nil {
		return x.CustomParameter
	}
	return ""
}

func (x *ExposureStrategyRequest_UserInfo) GetArpu() float32 {
	if x != nil {
		return x.Arpu
	}
	return 0
}

func (x *ExposureStrategyRequest_UserInfo) GetOs() int64 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *ExposureStrategyRequest_UserInfo) GetPosIdExposuredList() []*ExposureStrategyRequest_PosIdExposured {
	if x != nil {
		return x.PosIdExposuredList
	}
	return nil
}

func (x *ExposureStrategyRequest_UserInfo) GetPosId() string {
	if x != nil {
		return x.PosId
	}
	return ""
}

type ExposureStrategyResponse_ExposureStrategyIntervalTuple struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId         string `protobuf:"bytes,1,opt,name=strategyId,proto3" json:"strategyId,omitempty"`
	ExposureIntervalMs int64  `protobuf:"varint,2,opt,name=exposureIntervalMs,proto3" json:"exposureIntervalMs,omitempty"`
}

func (x *ExposureStrategyResponse_ExposureStrategyIntervalTuple) Reset() {
	*x = ExposureStrategyResponse_ExposureStrategyIntervalTuple{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureStrategyResponse_ExposureStrategyIntervalTuple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureStrategyResponse_ExposureStrategyIntervalTuple) ProtoMessage() {}

func (x *ExposureStrategyResponse_ExposureStrategyIntervalTuple) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureStrategyResponse_ExposureStrategyIntervalTuple.ProtoReflect.Descriptor instead.
func (*ExposureStrategyResponse_ExposureStrategyIntervalTuple) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ExposureStrategyResponse_ExposureStrategyIntervalTuple) GetStrategyId() string {
	if x != nil {
		return x.StrategyId
	}
	return ""
}

func (x *ExposureStrategyResponse_ExposureStrategyIntervalTuple) GetExposureIntervalMs() int64 {
	if x != nil {
		return x.ExposureIntervalMs
	}
	return 0
}

type GetExpectClickRatioResponse_ExpectClickRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrategyId       string  `protobuf:"bytes,1,opt,name=strategyId,proto3" json:"strategyId,omitempty"`
	TagId            string  `protobuf:"bytes,2,opt,name=tagId,proto3" json:"tagId,omitempty"`
	ExpectClickRatio float64 `protobuf:"fixed64,3,opt,name=expectClickRatio,proto3" json:"expectClickRatio,omitempty"`
}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) Reset() {
	*x = GetExpectClickRatioResponse_ExpectClickRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpectClickRatioResponse_ExpectClickRatio) ProtoMessage() {}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) ProtoReflect() protoreflect.Message {
	mi := &file_adx_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpectClickRatioResponse_ExpectClickRatio.ProtoReflect.Descriptor instead.
func (*GetExpectClickRatioResponse_ExpectClickRatio) Descriptor() ([]byte, []int) {
	return file_adx_proto_rawDescGZIP(), []int{28, 0}
}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) GetStrategyId() string {
	if x != nil {
		return x.StrategyId
	}
	return ""
}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *GetExpectClickRatioResponse_ExpectClickRatio) GetExpectClickRatio() float64 {
	if x != nil {
		return x.ExpectClickRatio
	}
	return 0
}

var File_adx_proto protoreflect.FileDescriptor

var file_adx_proto_rawDesc = []byte{
	0x0a, 0x09, 0x61, 0x64, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x22, 0x54, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xa5, 0x01, 0x0a, 0x09, 0x49, 0x6e, 0x6e, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x10, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22,
	0xe0, 0x13, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36,
	0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d,
	0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x36, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x3f,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x39, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70,
	0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x35,
	0x0a, 0x05, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x69, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x78, 0x30, 0x31, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x65, 0x6e,
	0x64, 0x69, 0x78, 0x30, 0x31, 0x1a, 0x85, 0x02, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65,
	0x12, 0x3b, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x1a, 0xea, 0x01,
	0x0a, 0x0a, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x4c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d,
	0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x57, 0x0a, 0x03, 0x41, 0x70,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x1a, 0xad, 0x0a, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x6f, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x6d, 0x65, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1c, 0x0a, 0x09,
	0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64,
	0x66, 0x61, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61,
	0x63, 0x4d, 0x64, 0x35, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x4d,
	0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x56, 0x36, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x70, 0x56, 0x36, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x26, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x68, 0x77, 0x76, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x68, 0x77, 0x76,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x2c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x63,
	0x63, 0x4d, 0x6e, 0x63, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x63, 0x63, 0x4d,
	0x6e, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18,
	0x30, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x36, 0x0a, 0x03, 0x67, 0x65,
	0x6f, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67,
	0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x32, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x41, 0x47, 0x18, 0x36, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x41, 0x47, 0x12, 0x1e,
	0x0a, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x37, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x38, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x18,
	0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x3b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x3c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x18,
	0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x31, 0x18,
	0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72,
	0x6b, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x40,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61,
	0x69, 0x64, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x69, 0x64, 0x12, 0x4f, 0x0a, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x45, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x1a, 0x32, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62,
	0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x3d, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x1a, 0x30, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18,
	0x3f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x22, 0x95, 0x10, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x42, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74,
	0x62, 0x69, 0x64, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x1a, 0x42, 0x0a,
	0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x37, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18,
	0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69,
	0x64, 0x1a, 0xe3, 0x02, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70,
	0x69, 0x64, 0x18, 0x45, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x46, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x18, 0x47, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x64, 0x53, 0x74, 0x79, 0x6c,
	0x65, 0x12, 0x3a, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x48, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x49, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x4a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18,
	0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x68, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70,
	0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x6b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x6b, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0xd8, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0c,
	0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x07,
	0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0xd9, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x1a, 0x5e, 0x0a, 0x0c, 0x53, 0x68, 0x61, 0x6b, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x6b, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0xd9, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x68, 0x61, 0x6b, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x2b, 0x0a, 0x10, 0x73, 0x68,
	0x61, 0x6b, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0xda,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x94, 0x07, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x4c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x74, 0x6d, 0x6c, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x74,
	0x6d, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65,
	0x18, 0x4f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x18, 0x50, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x12, 0x5b, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x52, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x70, 0x6c, 0x55, 0x72, 0x6c, 0x18, 0x53, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x70, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x54, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x55, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x18,
	0x56, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x57, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52,
	0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d,
	0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f,
	0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x40, 0x0a, 0x1b, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18,
	0x5b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x30, 0x0a, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12,
	0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x61, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xeb,
	0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x58, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x59, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18,
	0x5c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x63, 0x55, 0x52, 0x4c, 0x18, 0x66, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x73, 0x63, 0x55, 0x52, 0x4c, 0x1a, 0x8b, 0x02, 0x0a,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55,
	0x72, 0x6c, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55,
	0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x26,
	0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x55, 0x72, 0x6c,
	0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56,
	0x61, 0x73, 0x74, 0x58, 0x6d, 0x6c, 0x18, 0x62, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x6d, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x75,
	0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d,
	0x67, 0x75, 0x72, 0x6c, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x22, 0xec, 0x01, 0x0a, 0x0f, 0x43,
	0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x3b,
	0x0a, 0x07, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x07, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x47, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x47, 0x65, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61,
	0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x06, 0x4d, 0x69,
	0x78, 0x63, 0x66, 0x77, 0x12, 0x28, 0x0a, 0x02, 0x66, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x46, 0x73, 0x52, 0x02, 0x66, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x63, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x31, 0x12, 0x0e,
	0x0a, 0x02, 0x63, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x32, 0x12, 0x0e,
	0x0a, 0x02, 0x63, 0x33, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x33, 0x12, 0x0e,
	0x0a, 0x02, 0x63, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x34, 0x12, 0x0e,
	0x0a, 0x02, 0x63, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x35, 0x22, 0xee,
	0x01, 0x0a, 0x02, 0x46, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x33, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x34, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x36, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x37, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x37, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x38, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x38, 0x12, 0x0e, 0x0a, 0x02, 0x66, 0x39, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x66, 0x39, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x31, 0x30, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x66, 0x31, 0x30, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x31, 0x31, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x31, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x31, 0x32,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x31, 0x32, 0x12, 0x10, 0x0a, 0x03, 0x66,
	0x31, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x31, 0x33, 0x12, 0x10, 0x0a,
	0x03, 0x66, 0x31, 0x34, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x66, 0x31, 0x34, 0x22,
	0xcd, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x74, 0x6d,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x74,
	0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22,
	0x54, 0x0a, 0x10, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x62,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x46,
	0x6f, 0x72, 0x62, 0x69, 0x64, 0x22, 0xc2, 0x20, 0x0a, 0x0c, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70,
	0x12, 0x38, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56,
	0x32, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x41, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70,
	0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x38,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e,
	0x54, 0x61, 0x67, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x3d, 0x0a, 0x09, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78,
	0x74, 0x1a, 0x8f, 0x02, 0x0a, 0x03, 0x49, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67,
	0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12,
	0x4d, 0x0a, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x43,
	0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x32, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x1a, 0xea, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x65, 0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x1a, 0x57, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x44, 0x0a, 0x0c, 0x50, 0x6f, 0x73,
	0x49, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x73,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x1a,
	0xf3, 0x06, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x62, 0x61, 0x6e, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x6e,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x64, 0x66, 0x61, 0x46, 0x69, 0x6c, 0x6c, 0x12, 0x26,
	0x0a, 0x0f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x5f, 0x66, 0x69, 0x6c,
	0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x49, 0x64, 0x46, 0x69, 0x6c, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x68, 0x6f, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x68, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x6f, 0x6c, 0x64, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x2d,
	0x0a, 0x13, 0x68, 0x6f, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x68, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x2f, 0x0a,
	0x14, 0x63, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x63,
	0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x21, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x6c, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x4f, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0d, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x29, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x56, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x49, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x52, 0x10, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x70, 0x75, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x04, 0x61, 0x72, 0x70, 0x75, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x2b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x57, 0x0a, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x2c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x6e, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x88, 0x01, 0x01,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x1a, 0xa7, 0x0b, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f,
	0x73, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64,
	0x35, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35,
	0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6f, 0x61, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x64, 0x66, 0x61, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61,
	0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x63, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61,
	0x63, 0x4d, 0x64, 0x35, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x56, 0x36, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x56, 0x36, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x26,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x29,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x10, 0x0a, 0x03, 0x68, 0x77, 0x76, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x68,
	0x77, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x2c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x63, 0x63, 0x4d, 0x6e, 0x63, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x63,
	0x63, 0x4d, 0x6e, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70,
	0x69, 0x18, 0x30, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x38, 0x0a, 0x03,
	0x67, 0x65, 0x6f, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x47, 0x65,
	0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x22, 0x0a, 0x0c,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x18, 0x35, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x41, 0x47, 0x18,
	0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66,
	0x41, 0x47, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x38, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x65, 0x63, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x3c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x65, 0x63, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x45, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x78, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x65, 0x64, 0x18, 0x46, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x77, 0x78, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x6d, 0x69, 0x78, 0x63, 0x66,
	0x77, 0x18, 0x47, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4d,
	0x69, 0x78, 0x63, 0x66, 0x77, 0x52, 0x06, 0x6d, 0x69, 0x78, 0x63, 0x66, 0x77, 0x12, 0x20, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x31, 0x18, 0x4a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x31, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x40, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x41,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72, 0x74,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64, 0x18,
	0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x69, 0x64, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x63, 0x33, 0x18, 0x48, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x33, 0x12,
	0x0e, 0x0a, 0x02, 0x63, 0x31, 0x18, 0x49, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x31, 0x1a,
	0x32, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x39, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x3e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x1a, 0x30, 0x0a, 0x04,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x1a, 0x8b,
	0x02, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x4a, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x42, 0x69, 0x64,
	0x45, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x53, 0x64,
	0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x52, 0x09, 0x73, 0x64, 0x6b, 0x42, 0x69, 0x64, 0x45,
	0x78, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x64, 0x6b, 0x45, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x45, 0x78, 0x74, 0x2e, 0x53, 0x44, 0x4b, 0x45, 0x78,
	0x74, 0x52, 0x06, 0x73, 0x64, 0x6b, 0x45, 0x78, 0x74, 0x1a, 0x71, 0x0a, 0x06, 0x53, 0x44, 0x4b,
	0x45, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x64, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x44, 0x4b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xab, 0x01, 0x0a,
	0x09, 0x53, 0x64, 0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x79, 0x6c,
	0x68, 0x42, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x79, 0x6c, 0x68, 0x42, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x79, 0x6c,
	0x68, 0x4f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x79, 0x6c, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x17, 0x79, 0x6c, 0x68, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70,
	0x6c, 0x61, 0x73, 0x68, 0x5a, 0x6f, 0x6f, 0x6d, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x79, 0x6c, 0x68, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x6c,
	0x61, 0x73, 0x68, 0x5a, 0x6f, 0x6f, 0x6d, 0x6f, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x79, 0x6c,
	0x68, 0x53, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x79, 0x6c, 0x68, 0x53, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x33, 0x0a, 0x07, 0x53, 0x44,
	0x4b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x8d, 0x20, 0x0a, 0x0d, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56,
	0x32, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x69, 0x64, 0x49, 0x44, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x69, 0x64, 0x49, 0x44, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x42,
	0x69, 0x64, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e, 0x53, 0x65,
	0x61, 0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x39,
	0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32,
	0x2e, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0x4c, 0x0a, 0x07, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x07, 0x62, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x07,
	0x62, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xae, 0x04, 0x0a, 0x03, 0x42, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x44, 0x18, 0x45, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x70, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x46, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x47, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x61, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x48, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x49,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x55, 0x52, 0x4c, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x55, 0x52, 0x4c, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x55, 0x52, 0x4c, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x55, 0x52, 0x4c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x4e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x72, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x68,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a,
	0x08, 0x62, 0x69, 0x64, 0x54, 0x72, 0x61, 0x63, 0x65, 0x18, 0x69, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x56, 0x32, 0x2e, 0x42, 0x69, 0x64, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x08, 0x62,
	0x69, 0x64, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x6b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x55, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0xd8, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x32, 0x2e, 0x53, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x0c, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x52, 0x4c, 0x18, 0xd9, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x52, 0x4c, 0x1a, 0xc3, 0x02, 0x0a, 0x09, 0x49, 0x6e, 0x6e,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x6b, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x18, 0x6c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x61, 0x67, 0x49, 0x64, 0x18, 0x6d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x54, 0x61, 0x67, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4e, 0x75, 0x6d,
	0x12, 0x2a, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x18, 0x70, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x18, 0x71, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x2c, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x72, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x45,
	0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x73, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0xc6,
	0x05, 0x0a, 0x0c, 0x53, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x21, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0xd9,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x12, 0x2b, 0x0a, 0x10, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0xda, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x68, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12,
	0x33, 0x0a, 0x16, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x72, 0x5f,
	0x74, 0x77, 0x6f, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x73, 0x18, 0xdb, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x12, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x4f, 0x6e, 0x65, 0x4f, 0x72, 0x54, 0x77, 0x6f, 0x53,
	0x69, 0x64, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x5f, 0x6e, 0x65, 0x77, 0x5f,
	0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0xdc, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x75, 0x73, 0x65, 0x4e, 0x65, 0x77, 0x53, 0x68, 0x61, 0x6b, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x19, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x79, 0x18, 0xdd, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x73, 0x68, 0x61, 0x6b,
	0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x78, 0x18,
	0xde, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x41, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x78, 0x58, 0x12, 0x38, 0x0a,
	0x18, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x79, 0x18, 0xdf, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x15, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x78, 0x59, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x68, 0x61, 0x6b, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61,
	0x78, 0x5f, 0x7a, 0x18, 0xe0, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x73, 0x68, 0x61, 0x6b,
	0x65, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x78,
	0x5a, 0x12, 0x42, 0x0a, 0x1d, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x5f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0xe1, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x73, 0x68, 0x61, 0x6b, 0x65,
	0x43, 0x6c, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x5f, 0x6d, 0x69, 0x6e, 0x18, 0xe2, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x73, 0x68, 0x61,
	0x6b, 0x65, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x4d, 0x69, 0x6e, 0x12, 0x4b, 0x0a, 0x22, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0xe3, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x1e, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x68, 0x61, 0x6b,
	0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x44, 0x0a, 0x1e, 0x73, 0x68, 0x61, 0x6b, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0xe4, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b, 0x73, 0x68, 0x61, 0x6b,
	0x65, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0xba, 0x02, 0x0a, 0x08, 0x42, 0x69, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x64, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x61, 0x67, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x73, 0x70, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x70,
	0x49, 0x44, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34,
	0x0a, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x49, 0x44, 0x1a, 0xd6, 0x08, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x4c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x74, 0x6d, 0x6c, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x74, 0x6d, 0x6c, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x4f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52, 0x4c, 0x18, 0x50,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x52,
	0x4c, 0x12, 0x5d, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x18, 0x52, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x70, 0x6c, 0x55, 0x52, 0x4c, 0x18, 0x53, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x70,
	0x6c, 0x55, 0x52, 0x4c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6d, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x54, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x55, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55,
	0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x56, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x13, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x18, 0x57, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x44, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x44, 0x12,
	0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61,
	0x74, 0x68, 0x18, 0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x5a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x45, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x45, 0x78, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x1e, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52,
	0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1e, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x14, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x36, 0x0a, 0x16, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x16, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x5e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x15, 0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55,
	0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x64, 0x70,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x60, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x12, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x52, 0x4c, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x62, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x56, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x52, 0x4c, 0x52, 0x0e, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x52, 0x4c, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x44, 0x0a,
	0x0a, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x52, 0x4c, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x55, 0x52, 0x4c,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x55, 0x52, 0x4c, 0x4c,
	0x69, 0x73, 0x74, 0x1a, 0xeb, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x59,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x63, 0x79, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x65, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x63, 0x55,
	0x52, 0x4c, 0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x73, 0x63, 0x55, 0x52,
	0x4c, 0x1a, 0x8b, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x55, 0x52, 0x4c, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x55, 0x52, 0x4c, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x52, 0x4c, 0x18, 0x60,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x55, 0x52, 0x4c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x55, 0x52, 0x4c, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x55, 0x52, 0x4c, 0x12, 0x22, 0x0a, 0x0c, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x4d, 0x4c, 0x18, 0x62, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x4d, 0x4c, 0x12,
	0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x55, 0x52,
	0x4c, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e,
	0x64, 0x49, 0x6d, 0x67, 0x55, 0x52, 0x4c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x50, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x55, 0x52, 0x4c, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x55, 0x52, 0x4c, 0x1a,
	0xed, 0x01, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x64, 0x6b, 0x45, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x2e, 0x45, 0x78, 0x74,
	0x2e, 0x53, 0x44, 0x4b, 0x45, 0x78, 0x74, 0x52, 0x06, 0x73, 0x64, 0x6b, 0x45, 0x78, 0x74, 0x1a,
	0x9d, 0x01, 0x0a, 0x06, 0x53, 0x44, 0x4b, 0x45, 0x78, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x64,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x73, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x70, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x44,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53,
	0x44, 0x4b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x80, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x07, 0x61, 0x70, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x47, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x57, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb1, 0x03, 0x0a, 0x0b, 0x43, 0x61,
	0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x6f, 0x6f,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x79, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x79, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x9d, 0x01,
	0x0a, 0x12, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x32, 0x43, 0x61, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61,
	0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x12, 0x43, 0x0a, 0x0b,
	0x63, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0xb3, 0x01,
	0x0a, 0x13, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x32, 0x43, 0x61, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x61, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x61, 0x69,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x43, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x43,
	0x61, 0x69, 0x64, 0x47, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x43, 0x61, 0x69, 0x64, 0x47, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x43, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x61, 0x69,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x43, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x61, 0x73, 0x74, 0x22, 0xea, 0x05, 0x0a, 0x0c, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x44, 0x12, 0x56, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x88, 0x01, 0x01, 0x1a, 0xcd, 0x01, 0x0a,
	0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26,
	0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x74, 0x6d, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x74, 0x6d, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0xac, 0x01, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x70, 0x75, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x61, 0x72, 0x70, 0x75, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x6f, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x22, 0xd8, 0x06, 0x0a, 0x0d, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50,
	0x6f, 0x73, 0x52, 0x09, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4d, 0x0a,
	0x0b, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x67, 0x72, 0x61, 0x79, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x67, 0x72, 0x61, 0x79, 0x53, 0x63, 0x61, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x68, 0x0a, 0x14, 0x65, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64,
	0x50, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x14, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x0d,
	0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0d, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x1a,
	0x56, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0xe8, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x52,
	0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x73, 0x1a, 0x67, 0x0a, 0x07, 0x73, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x61, 0x67, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61,
	0x67, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x73, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x64, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc9, 0x05, 0x0a, 0x05,
	0x41, 0x64, 0x50, 0x6f, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x56, 0x32,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x56, 0x32, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x4d, 0x61, 0x78, 0x4c, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x64, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x4d,
	0x61, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x55, 0x6e,
	0x69, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53,
	0x68, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70,
	0x61, 0x67, 0x65, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x61,
	0x64, 0x50, 0x6f, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65,
	0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x12, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x64, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x61, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x22, 0x34, 0x0a, 0x14, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x44, 0x22, 0xf8, 0x03,
	0x0a, 0x15, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x14, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x14, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42,
	0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x15, 0x77, 0x61, 0x74, 0x65, 0x72, 0x66, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x77, 0x61,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x1a, 0xb2, 0x02, 0x0a, 0x14, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x62, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x48, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42,
	0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x74, 0x61, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x1a, 0x81, 0x01, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x64, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x64, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x73,
	0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x70, 0x49, 0x44,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x61, 0x67, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0xd3, 0x07, 0x0a, 0x17, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x44, 0x12, 0x61, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x52, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x57, 0x0a, 0x10, 0x6e, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x4e, 0x65, 0x67, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x10, 0x6e,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x88,
	0x01, 0x01, 0x1a, 0xcd, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x75, 0x74, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x74, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x1a, 0x72, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26,
	0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x4d, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x4d, 0x73, 0x1a, 0xb0, 0x02, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x70, 0x75, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x04, 0x61, 0x72, 0x70, 0x75, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x6c, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x45, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x6f, 0x73, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52,
	0x12, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x73, 0x49, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6e, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x84,
	0x03, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x8a, 0x01, 0x0a, 0x19,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x54, 0x75, 0x70, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x52, 0x19, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54,
	0x75, 0x70, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x54, 0x6f, 0x6f, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x6f, 0x6f, 0x4c,
	0x61, 0x72, 0x67, 0x65, 0x1a, 0x6f, 0x0a, 0x1d, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x54, 0x75, 0x70, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x65, 0x67, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x4d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x12, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x4d, 0x73, 0x22, 0x3c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x49, 0x64, 0x22, 0x8b, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x63,
	0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x14, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b,
	0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x14, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69,
	0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x74, 0x0a, 0x10, 0x45,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43,
	0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x2a, 0x10, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x32, 0x93, 0x07, 0x0a, 0x0a, 0x41, 0x64, 0x78, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x4f, 0x0a, 0x06, 0x41, 0x64, 0x78, 0x52, 0x54, 0x42, 0x12, 0x20, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x08, 0x41, 0x64, 0x78, 0x52, 0x54, 0x42, 0x56, 0x32, 0x12,
	0x22, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x56, 0x32, 0x1a, 0x23, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x56, 0x32, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x0b, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x32, 0x43, 0x61, 0x69, 0x64, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x32, 0x43, 0x61, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x32, 0x43, 0x61, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x61, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x53,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x27, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x05, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x12,
	0x22, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x41, 0x64, 0x50, 0x6f, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x0d, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x19, 0x5a, 0x17, 0x2e, 0x2f, 0x3b,
	0x67, 0x6f, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x61, 0x64, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_adx_proto_rawDescOnce sync.Once
	file_adx_proto_rawDescData = file_adx_proto_rawDesc
)

func file_adx_proto_rawDescGZIP() []byte {
	file_adx_proto_rawDescOnce.Do(func() {
		file_adx_proto_rawDescData = protoimpl.X.CompressGZIP(file_adx_proto_rawDescData)
	})
	return file_adx_proto_rawDescData
}

var file_adx_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_adx_proto_msgTypes = make([]protoimpl.MessageInfo, 77)
var file_adx_proto_goTypes = []interface{}{
	(STATUS)(0),                                                    // 0: go.micro.service.adx.STATUS
	(*ResponseStatus)(nil),                                         // 1: go.micro.service.adx.ResponseStatus
	(*InnerData)(nil),                                              // 2: go.micro.service.adx.InnerData
	(*BidRequest)(nil),                                             // 3: go.micro.service.adx.BidRequest
	(*BidResponse)(nil),                                            // 4: go.micro.service.adx.BidResponse
	(*CaidWithFactors)(nil),                                        // 5: go.micro.service.adx.CaidWithFactors
	(*Mixcfw)(nil),                                                 // 6: go.micro.service.adx.Mixcfw
	(*Fs)(nil),                                                     // 7: go.micro.service.adx.Fs
	(*PromotionInfo)(nil),                                          // 8: go.micro.service.adx.PromotionInfo
	(*NegativeFeedback)(nil),                                       // 9: go.micro.service.adx.NegativeFeedback
	(*BidRequestV2)(nil),                                           // 10: go.micro.service.adx.BidRequestV2
	(*SDKInfo)(nil),                                                // 11: go.micro.service.adx.SDKInfo
	(*BidResponseV2)(nil),                                          // 12: go.micro.service.adx.BidResponseV2
	(*SetAppListRequest)(nil),                                      // 13: go.micro.service.adx.SetAppListRequest
	(*SetAppListResponse)(nil),                                     // 14: go.micro.service.adx.SetAppListResponse
	(*GetAppListRequest)(nil),                                      // 15: go.micro.service.adx.GetAppListRequest
	(*AppInfo)(nil),                                                // 16: go.micro.service.adx.AppInfo
	(*GetAppListResponse)(nil),                                     // 17: go.micro.service.adx.GetAppListResponse
	(*CaidFactors)(nil),                                            // 18: go.micro.service.adx.CaidFactors
	(*Factor2CaidRequest)(nil),                                     // 19: go.micro.service.adx.Factor2CaidRequest
	(*Factor2CaidResponse)(nil),                                    // 20: go.micro.service.adx.Factor2CaidResponse
	(*AdPosRequest)(nil),                                           // 21: go.micro.service.adx.AdPosRequest
	(*AdPosResponse)(nil),                                          // 22: go.micro.service.adx.AdPosResponse
	(*AdPos)(nil),                                                  // 23: go.micro.service.adx.AdPos
	(*ClientBiddingRequest)(nil),                                   // 24: go.micro.service.adx.ClientBiddingRequest
	(*ClientBiddingResponse)(nil),                                  // 25: go.micro.service.adx.ClientBiddingResponse
	(*ExposureStrategyRequest)(nil),                                // 26: go.micro.service.adx.ExposureStrategyRequest
	(*ExposureStrategyResponse)(nil),                               // 27: go.micro.service.adx.ExposureStrategyResponse
	(*GetExpectClickRatioRequest)(nil),                             // 28: go.micro.service.adx.GetExpectClickRatioRequest
	(*GetExpectClickRatioResponse)(nil),                            // 29: go.micro.service.adx.GetExpectClickRatioResponse
	(*BidRequest_Imp)(nil),                                         // 30: go.micro.service.adx.BidRequest.Imp
	(*BidRequest_AdslotSize)(nil),                                  // 31: go.micro.service.adx.BidRequest.AdslotSize
	(*BidRequest_App)(nil),                                         // 32: go.micro.service.adx.BidRequest.App
	(*BidRequest_Device)(nil),                                      // 33: go.micro.service.adx.BidRequest.Device
	(*BidRequest_Deal)(nil),                                        // 34: go.micro.service.adx.BidRequest.Deal
	(*BidRequest_Geo)(nil),                                         // 35: go.micro.service.adx.BidRequest.Geo
	(*BidRequest_User)(nil),                                        // 36: go.micro.service.adx.BidRequest.User
	(*BidResponse_SeatBid)(nil),                                    // 37: go.micro.service.adx.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),                                        // 38: go.micro.service.adx.BidResponse.Bid
	(*BidResponse_ShakeSetting)(nil),                               // 39: go.micro.service.adx.BidResponse.ShakeSetting
	(*BidResponse_Item)(nil),                                       // 40: go.micro.service.adx.BidResponse.Item
	(*BidResponse_DownloadAppInfo)(nil),                            // 41: go.micro.service.adx.BidResponse.DownloadAppInfo
	(*BidResponse_Video)(nil),                                      // 42: go.micro.service.adx.BidResponse.Video
	(*BidRequestV2_Imp)(nil),                                       // 43: go.micro.service.adx.BidRequestV2.Imp
	(*BidRequestV2_AdSlotSize)(nil),                                // 44: go.micro.service.adx.BidRequestV2.AdSlotSize
	(*BidRequestV2_App)(nil),                                       // 45: go.micro.service.adx.BidRequestV2.App
	(*BidRequestV2_PosIdClicked)(nil),                              // 46: go.micro.service.adx.BidRequestV2.PosIdClicked
	(*BidRequestV2_Tag)(nil),                                       // 47: go.micro.service.adx.BidRequestV2.Tag
	(*BidRequestV2_Device)(nil),                                    // 48: go.micro.service.adx.BidRequestV2.Device
	(*BidRequestV2_Deal)(nil),                                      // 49: go.micro.service.adx.BidRequestV2.Deal
	(*BidRequestV2_Geo)(nil),                                       // 50: go.micro.service.adx.BidRequestV2.Geo
	(*BidRequestV2_User)(nil),                                      // 51: go.micro.service.adx.BidRequestV2.User
	(*BidRequestV2_Ext)(nil),                                       // 52: go.micro.service.adx.BidRequestV2.Ext
	(*BidRequestV2_SdkBidExt)(nil),                                 // 53: go.micro.service.adx.BidRequestV2.SdkBidExt
	(*BidRequestV2_Ext_SDKExt)(nil),                                // 54: go.micro.service.adx.BidRequestV2.Ext.SDKExt
	(*BidResponseV2_SeatBid)(nil),                                  // 55: go.micro.service.adx.BidResponseV2.SeatBid
	(*BidResponseV2_Bid)(nil),                                      // 56: go.micro.service.adx.BidResponseV2.Bid
	(*BidResponseV2_InnerData)(nil),                                // 57: go.micro.service.adx.BidResponseV2.InnerData
	(*BidResponseV2_ShakeSetting)(nil),                             // 58: go.micro.service.adx.BidResponseV2.ShakeSetting
	(*BidResponseV2_BidTrace)(nil),                                 // 59: go.micro.service.adx.BidResponseV2.BidTrace
	(*BidResponseV2_Item)(nil),                                     // 60: go.micro.service.adx.BidResponseV2.Item
	(*BidResponseV2_MonitorURL)(nil),                               // 61: go.micro.service.adx.BidResponseV2.MonitorURL
	(*BidResponseV2_DownloadAppInfo)(nil),                          // 62: go.micro.service.adx.BidResponseV2.DownloadAppInfo
	(*BidResponseV2_Video)(nil),                                    // 63: go.micro.service.adx.BidResponseV2.Video
	(*BidResponseV2_Ext)(nil),                                      // 64: go.micro.service.adx.BidResponseV2.Ext
	(*BidResponseV2_Ext_SDKExt)(nil),                               // 65: go.micro.service.adx.BidResponseV2.Ext.SDKExt
	(*AdPosRequest_PromotionInfo)(nil),                             // 66: go.micro.service.adx.AdPosRequest.PromotionInfo
	(*AdPosRequest_UserInfo)(nil),                                  // 67: go.micro.service.adx.AdPosRequest.UserInfo
	(*AdPosResponse_ExposureInterval)(nil),                         // 68: go.micro.service.adx.AdPosResponse.ExposureInterval
	(*AdPosResponse_ExposureStrategy)(nil),                         // 69: go.micro.service.adx.AdPosResponse.ExposureStrategy
	(*AdPosResponseSdkInfo)(nil),                                   // 70: go.micro.service.adx.AdPosResponse.sdkInfo
	(*ClientBiddingResponse_ClientBiddingTagList)(nil),             // 71: go.micro.service.adx.ClientBiddingResponse.ClientBiddingTagList
	(*ClientBiddingResponse_ClientBiddingTagList_TagList)(nil),     // 72: go.micro.service.adx.ClientBiddingResponse.ClientBiddingTagList.TagList
	(*ExposureStrategyRequest_PromotionInfo)(nil),                  // 73: go.micro.service.adx.ExposureStrategyRequest.PromotionInfo
	(*ExposureStrategyRequest_PosIdExposured)(nil),                 // 74: go.micro.service.adx.ExposureStrategyRequest.PosIdExposured
	(*ExposureStrategyRequest_UserInfo)(nil),                       // 75: go.micro.service.adx.ExposureStrategyRequest.UserInfo
	(*ExposureStrategyResponse_ExposureStrategyIntervalTuple)(nil), // 76: go.micro.service.adx.ExposureStrategyResponse.ExposureStrategyIntervalTuple
	(*GetExpectClickRatioResponse_ExpectClickRatio)(nil),           // 77: go.micro.service.adx.GetExpectClickRatioResponse.ExpectClickRatio
}
var file_adx_proto_depIdxs = []int32{
	0,  // 0: go.micro.service.adx.ResponseStatus.code:type_name -> go.micro.service.adx.STATUS
	30, // 1: go.micro.service.adx.BidRequest.imp:type_name -> go.micro.service.adx.BidRequest.Imp
	32, // 2: go.micro.service.adx.BidRequest.app:type_name -> go.micro.service.adx.BidRequest.App
	33, // 3: go.micro.service.adx.BidRequest.device:type_name -> go.micro.service.adx.BidRequest.Device
	36, // 4: go.micro.service.adx.BidRequest.user:type_name -> go.micro.service.adx.BidRequest.User
	2,  // 5: go.micro.service.adx.BidRequest.inner:type_name -> go.micro.service.adx.InnerData
	37, // 6: go.micro.service.adx.BidResponse.seatbid:type_name -> go.micro.service.adx.BidResponse.SeatBid
	18, // 7: go.micro.service.adx.CaidWithFactors.factors:type_name -> go.micro.service.adx.CaidFactors
	7,  // 8: go.micro.service.adx.Mixcfw.fs:type_name -> go.micro.service.adx.Fs
	43, // 9: go.micro.service.adx.BidRequestV2.imp:type_name -> go.micro.service.adx.BidRequestV2.Imp
	45, // 10: go.micro.service.adx.BidRequestV2.app:type_name -> go.micro.service.adx.BidRequestV2.App
	48, // 11: go.micro.service.adx.BidRequestV2.device:type_name -> go.micro.service.adx.BidRequestV2.Device
	51, // 12: go.micro.service.adx.BidRequestV2.user:type_name -> go.micro.service.adx.BidRequestV2.User
	47, // 13: go.micro.service.adx.BidRequestV2.tag:type_name -> go.micro.service.adx.BidRequestV2.Tag
	2,  // 14: go.micro.service.adx.BidRequestV2.innerData:type_name -> go.micro.service.adx.InnerData
	52, // 15: go.micro.service.adx.BidRequestV2.ext:type_name -> go.micro.service.adx.BidRequestV2.Ext
	55, // 16: go.micro.service.adx.BidResponseV2.seatBid:type_name -> go.micro.service.adx.BidResponseV2.SeatBid
	64, // 17: go.micro.service.adx.BidResponseV2.ext:type_name -> go.micro.service.adx.BidResponseV2.Ext
	16, // 18: go.micro.service.adx.SetAppListRequest.appList:type_name -> go.micro.service.adx.AppInfo
	16, // 19: go.micro.service.adx.GetAppListResponse.appList:type_name -> go.micro.service.adx.AppInfo
	18, // 20: go.micro.service.adx.Factor2CaidRequest.caidFactors:type_name -> go.micro.service.adx.CaidFactors
	66, // 21: go.micro.service.adx.AdPosRequest.promotionInfo:type_name -> go.micro.service.adx.AdPosRequest.PromotionInfo
	67, // 22: go.micro.service.adx.AdPosRequest.userInfo:type_name -> go.micro.service.adx.AdPosRequest.UserInfo
	9,  // 23: go.micro.service.adx.AdPosRequest.negativeFeedback:type_name -> go.micro.service.adx.NegativeFeedback
	23, // 24: go.micro.service.adx.AdPosResponse.adPosList:type_name -> go.micro.service.adx.AdPos
	70, // 25: go.micro.service.adx.AdPosResponse.sdkInfoList:type_name -> go.micro.service.adx.AdPosResponse.sdkInfo
	69, // 26: go.micro.service.adx.AdPosResponse.exposureStrategyList:type_name -> go.micro.service.adx.AdPosResponse.ExposureStrategy
	70, // 27: go.micro.service.adx.AdPosResponse.sdkInfoListV2:type_name -> go.micro.service.adx.AdPosResponse.sdkInfo
	71, // 28: go.micro.service.adx.ClientBiddingResponse.clientBiddingTagList:type_name -> go.micro.service.adx.ClientBiddingResponse.ClientBiddingTagList
	73, // 29: go.micro.service.adx.ExposureStrategyRequest.promotionInfo:type_name -> go.micro.service.adx.ExposureStrategyRequest.PromotionInfo
	75, // 30: go.micro.service.adx.ExposureStrategyRequest.userInfo:type_name -> go.micro.service.adx.ExposureStrategyRequest.UserInfo
	9,  // 31: go.micro.service.adx.ExposureStrategyRequest.negativeFeedback:type_name -> go.micro.service.adx.NegativeFeedback
	76, // 32: go.micro.service.adx.ExposureStrategyResponse.strategyIntervalTupleList:type_name -> go.micro.service.adx.ExposureStrategyResponse.ExposureStrategyIntervalTuple
	77, // 33: go.micro.service.adx.GetExpectClickRatioResponse.expectClickRatioList:type_name -> go.micro.service.adx.GetExpectClickRatioResponse.ExpectClickRatio
	31, // 34: go.micro.service.adx.BidRequest.Imp.adslotSize:type_name -> go.micro.service.adx.BidRequest.AdslotSize
	34, // 35: go.micro.service.adx.BidRequest.Imp.deals:type_name -> go.micro.service.adx.BidRequest.Deal
	35, // 36: go.micro.service.adx.BidRequest.Device.geo:type_name -> go.micro.service.adx.BidRequest.Geo
	5,  // 37: go.micro.service.adx.BidRequest.Device.caidWithFactors:type_name -> go.micro.service.adx.CaidWithFactors
	38, // 38: go.micro.service.adx.BidResponse.SeatBid.bid:type_name -> go.micro.service.adx.BidResponse.Bid
	40, // 39: go.micro.service.adx.BidResponse.Bid.item:type_name -> go.micro.service.adx.BidResponse.Item
	39, // 40: go.micro.service.adx.BidResponse.Bid.shakeSetting:type_name -> go.micro.service.adx.BidResponse.ShakeSetting
	41, // 41: go.micro.service.adx.BidResponse.Item.downloadAppInfo:type_name -> go.micro.service.adx.BidResponse.DownloadAppInfo
	42, // 42: go.micro.service.adx.BidResponse.Item.video:type_name -> go.micro.service.adx.BidResponse.Video
	44, // 43: go.micro.service.adx.BidRequestV2.Imp.adslotSize:type_name -> go.micro.service.adx.BidRequestV2.AdSlotSize
	49, // 44: go.micro.service.adx.BidRequestV2.Imp.dealList:type_name -> go.micro.service.adx.BidRequestV2.Deal
	8,  // 45: go.micro.service.adx.BidRequestV2.Tag.promotionInfo:type_name -> go.micro.service.adx.PromotionInfo
	46, // 46: go.micro.service.adx.BidRequestV2.Tag.posIdClickedList:type_name -> go.micro.service.adx.BidRequestV2.PosIdClicked
	9,  // 47: go.micro.service.adx.BidRequestV2.Tag.negativeFeedback:type_name -> go.micro.service.adx.NegativeFeedback
	50, // 48: go.micro.service.adx.BidRequestV2.Device.geo:type_name -> go.micro.service.adx.BidRequestV2.Geo
	5,  // 49: go.micro.service.adx.BidRequestV2.Device.caidWithFactors:type_name -> go.micro.service.adx.CaidWithFactors
	6,  // 50: go.micro.service.adx.BidRequestV2.Device.mixcfw:type_name -> go.micro.service.adx.Mixcfw
	53, // 51: go.micro.service.adx.BidRequestV2.Ext.sdkBidExt:type_name -> go.micro.service.adx.BidRequestV2.SdkBidExt
	54, // 52: go.micro.service.adx.BidRequestV2.Ext.sdkExt:type_name -> go.micro.service.adx.BidRequestV2.Ext.SDKExt
	11, // 53: go.micro.service.adx.BidRequestV2.Ext.SDKExt.sdkInfo:type_name -> go.micro.service.adx.SDKInfo
	56, // 54: go.micro.service.adx.BidResponseV2.SeatBid.bidList:type_name -> go.micro.service.adx.BidResponseV2.Bid
	60, // 55: go.micro.service.adx.BidResponseV2.Bid.item:type_name -> go.micro.service.adx.BidResponseV2.Item
	59, // 56: go.micro.service.adx.BidResponseV2.Bid.bidTrace:type_name -> go.micro.service.adx.BidResponseV2.BidTrace
	57, // 57: go.micro.service.adx.BidResponseV2.Bid.innerData:type_name -> go.micro.service.adx.BidResponseV2.InnerData
	58, // 58: go.micro.service.adx.BidResponseV2.Bid.shakeSetting:type_name -> go.micro.service.adx.BidResponseV2.ShakeSetting
	62, // 59: go.micro.service.adx.BidResponseV2.Item.downloadAppInfo:type_name -> go.micro.service.adx.BidResponseV2.DownloadAppInfo
	63, // 60: go.micro.service.adx.BidResponseV2.Item.video:type_name -> go.micro.service.adx.BidResponseV2.Video
	61, // 61: go.micro.service.adx.BidResponseV2.Item.monitorURLList:type_name -> go.micro.service.adx.BidResponseV2.MonitorURL
	65, // 62: go.micro.service.adx.BidResponseV2.Ext.sdkExt:type_name -> go.micro.service.adx.BidResponseV2.Ext.SDKExt
	11, // 63: go.micro.service.adx.BidResponseV2.Ext.SDKExt.sdkInfo:type_name -> go.micro.service.adx.SDKInfo
	68, // 64: go.micro.service.adx.AdPosResponse.ExposureStrategy.intervals:type_name -> go.micro.service.adx.AdPosResponse.ExposureInterval
	72, // 65: go.micro.service.adx.ClientBiddingResponse.ClientBiddingTagList.tagList:type_name -> go.micro.service.adx.ClientBiddingResponse.ClientBiddingTagList.TagList
	74, // 66: go.micro.service.adx.ExposureStrategyRequest.UserInfo.posIdExposuredList:type_name -> go.micro.service.adx.ExposureStrategyRequest.PosIdExposured
	3,  // 67: go.micro.service.adx.AdxService.AdxRTB:input_type -> go.micro.service.adx.BidRequest
	10, // 68: go.micro.service.adx.AdxService.AdxRTBV2:input_type -> go.micro.service.adx.BidRequestV2
	19, // 69: go.micro.service.adx.AdxService.Factor2Caid:input_type -> go.micro.service.adx.Factor2CaidRequest
	13, // 70: go.micro.service.adx.AdxService.SetAppList:input_type -> go.micro.service.adx.SetAppListRequest
	15, // 71: go.micro.service.adx.AdxService.GetAppList:input_type -> go.micro.service.adx.GetAppListRequest
	21, // 72: go.micro.service.adx.AdxService.AdPos:input_type -> go.micro.service.adx.AdPosRequest
	24, // 73: go.micro.service.adx.AdxService.ClientBidding:input_type -> go.micro.service.adx.ClientBiddingRequest
	26, // 74: go.micro.service.adx.AdxService.ExposureStrategy:input_type -> go.micro.service.adx.ExposureStrategyRequest
	28, // 75: go.micro.service.adx.AdxService.GetExpectClickRatio:input_type -> go.micro.service.adx.GetExpectClickRatioRequest
	4,  // 76: go.micro.service.adx.AdxService.AdxRTB:output_type -> go.micro.service.adx.BidResponse
	12, // 77: go.micro.service.adx.AdxService.AdxRTBV2:output_type -> go.micro.service.adx.BidResponseV2
	20, // 78: go.micro.service.adx.AdxService.Factor2Caid:output_type -> go.micro.service.adx.Factor2CaidResponse
	14, // 79: go.micro.service.adx.AdxService.SetAppList:output_type -> go.micro.service.adx.SetAppListResponse
	17, // 80: go.micro.service.adx.AdxService.GetAppList:output_type -> go.micro.service.adx.GetAppListResponse
	22, // 81: go.micro.service.adx.AdxService.AdPos:output_type -> go.micro.service.adx.AdPosResponse
	25, // 82: go.micro.service.adx.AdxService.ClientBidding:output_type -> go.micro.service.adx.ClientBiddingResponse
	27, // 83: go.micro.service.adx.AdxService.ExposureStrategy:output_type -> go.micro.service.adx.ExposureStrategyResponse
	29, // 84: go.micro.service.adx.AdxService.GetExpectClickRatio:output_type -> go.micro.service.adx.GetExpectClickRatioResponse
	76, // [76:85] is the sub-list for method output_type
	67, // [67:76] is the sub-list for method input_type
	67, // [67:67] is the sub-list for extension type_name
	67, // [67:67] is the sub-list for extension extendee
	0,  // [0:67] is the sub-list for field type_name
}

func init() { file_adx_proto_init() }
func file_adx_proto_init() {
	if File_adx_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_adx_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponseStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InnerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaidWithFactors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mixcfw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PromotionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NegativeFeedback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SDKInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAppListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAppListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaidFactors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Factor2CaidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Factor2CaidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientBiddingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientBiddingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpectClickRatioRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpectClickRatioResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdslotSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_ShakeSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_DownloadAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_AdSlotSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_PosIdClicked); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_SdkBidExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequestV2_Ext_SDKExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_InnerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_ShakeSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_BidTrace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_MonitorURL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_DownloadAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponseV2_Ext_SDKExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosRequest_PromotionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosRequest_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosResponse_ExposureInterval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosResponse_ExposureStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdPosResponseSdkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientBiddingResponse_ClientBiddingTagList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientBiddingResponse_ClientBiddingTagList_TagList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyRequest_PromotionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyRequest_PosIdExposured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyRequest_UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureStrategyResponse_ExposureStrategyIntervalTuple); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExpectClickRatioResponse_ExpectClickRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_adx_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_adx_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_adx_proto_msgTypes[46].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_adx_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   77,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_adx_proto_goTypes,
		DependencyIndexes: file_adx_proto_depIdxs,
		EnumInfos:         file_adx_proto_enumTypes,
		MessageInfos:      file_adx_proto_msgTypes,
	}.Build()
	File_adx_proto = out.File
	file_adx_proto_rawDesc = nil
	file_adx_proto_goTypes = nil
	file_adx_proto_depIdxs = nil
}
