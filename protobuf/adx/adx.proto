syntax = "proto3";

package go.micro.service.adx;

option go_package = "./;go_micro_service_adx";

service AdxService {
  rpc AdxRTB(BidRequest) returns (BidResponse) {}
  rpc AdxRTBV2(BidRequestV2) returns (BidResponseV2) {}
  rpc Factor2Caid(Factor2CaidRequest) returns (Factor2CaidResponse) {}
  rpc SetAppList(SetAppListRequest) returns (SetAppListResponse){}
  rpc GetAppList(GetAppListRequest) returns (GetAppListResponse){}
  rpc AdPos(AdPosRequest) returns (AdPosResponse) {}
  rpc ClientBidding(ClientBiddingRequest) returns (ClientBiddingResponse) {}
  rpc ExposureStrategy(ExposureStrategyRequest) returns (ExposureStrategyResponse) {}
  rpc GetExpectClickRatio(GetExpectClickRatioRequest) returns (GetExpectClickRatioResponse){}
}

enum STATUS {
  OK = 0; //成功
}

message ResponseStatus {
  STATUS code = 1;
  string msg = 2;
}

message InnerData {
  string source = 1;
  int64 versionCode = 2;
  string deviceID = 3;
  string versionCodeQuote = 4;
  string userID = 5;
}

message BidRequest {
  string id = 1;
  repeated Imp imp = 2;
  App app = 3;
  Device device = 4;
  User user = 5;
  string apiVersion = 6;
  repeated string installedApp = 7;
  InnerData inner = 8;
  repeated int64 appendix01 = 9;

  message Imp{
    string id = 7;
    string tagId = 8;
    AdslotSize adslotSize = 9;
    double bidfloor = 10;
    bool deeplink = 11;
    int32 secure = 12;
    repeated Deal deals = 13;
  }

  message AdslotSize{
    int32 width = 14;
    int32 height = 15;
    repeated string mimes = 16;
    int32 size = 17;
    int32 titleLength = 18;
    int32 descLength = 19;
    int32 minDuration = 20;
    int32 maxDuration = 21;
  }

  message App{
    string appName = 22;
    string bundle = 23;
    string appVersion = 24;
  }

  message Device{
    string os = 25;
    string osv = 26;
    string imei = 27;
    string imeiMd5 = 28;
    string oaid = 29;
    string oaidMd5 = 30;
    string androidId = 31;
    string idfa = 32;
    string idfaMd5 = 33;
    string mac = 34;
    string macMd5 = 35;
    string ip = 36;
    string ipV6 = 37;
    string ua = 38;
    int32 connectionType = 39;
    string brand = 40;
    string make = 41;
    string model= 42;
    string hwv = 43;
    int32 carrier = 44;
    string mccMnc = 45;
    int32 screenHeight = 46;
    int32 screenWidth = 47;
    int32 ppi = 48;
    Geo geo = 49;
    string appList = 50;
    string bootMark = 51;
    string updateMark = 52;
    string verCodeOfHms = 53;
    string verCodeOfAG = 54;
    string romVersion  = 55;
    int32 orientation = 56;
    string bootTimeSec = 57;
    string phoneName = 58;
    int64 memorySize = 59;
    int64 diskSize = 60;
    string osUpdateTimeSec = 61;
    string modelCode  = 62;
    string timeZone  = 63;
    string updateMark1 = 74; // 爱奇艺专用
    // 废弃
    string fileTime  = 64;
    string deviceBirthTime  = 65;
    string caidVersion  = 66;
    string caid = 67;
    string paid = 68;
    CaidWithFactors caidWithFactors = 69;
  }

  message Deal{
    string id = 57;
    double bidfloor = 58;
  }

  message Geo{
    int32 type = 59;
    double lat = 60;
    double lon = 61;
    string country  = 62;
  }

  message User{
    int32 age = 63;
    int32 gender = 64;
  }
}

message BidResponse {
  string id = 65;
  string bidid = 66;
  repeated SeatBid seatbid = 67;

  message SeatBid{
    repeated Bid bid = 68;
  }

  message Bid{
    string impid = 69;
    int32 adType = 70;
    int32 adStyle = 71;
    Item  item = 72;
    double price = 73;
    string nurl = 74;
    string crid = 99;
    string apiName = 104;
    string cid = 107;
    ShakeSetting shakeSetting = 600;
    string logoUrl = 601;
  }

  message ShakeSetting {
    string shakeSwitch = 601;
    string shakeSensitivity = 602;
  }

  message Item{
    string title = 75;
    string desc = 76;
    string icon = 77;
    string html = 78;
    int32 mediaStyle = 79;
    string downloadUrl = 80;
    DownloadAppInfo downloadAppInfo = 81;
    string clickUrl = 82;
    string dplUrl = 83;
    repeated string imgs = 84;
    repeated string exposalUrls = 85;
    repeated string clickMonitorUrls = 86;
    Video video = 87;
    string miniProgramId = 88;
    string miniProgramPath = 89;
    int32 miniProgramType = 90;
    repeated string miniProgramSuccessTrackUrls = 91;
    repeated string downloadTrackUrls = 92;
    repeated string downloadedTrackUrls = 93;
    repeated string installedTrackUrls = 94;
    repeated string dpSuccessTrackUrls = 95;
    repeated string actionTrackUrls = 96;
    string packageName = 97;
  }

  message DownloadAppInfo{
    string appName = 88;
    string developer = 89;
    string version = 90;
    string packetSize = 91;
    string privacy = 92;
    string permission = 93;
    string desc = 101;
    string descURL = 102;
  }

  message Video{
    string videoUrl = 94;
    int32 videoDuration = 95;
    string videoStartUrl = 96;
    string videoFinishUrl = 97;
    string videoVastXml = 98;
    string videoEndImgurl = 99;
    string videoPreImgurl = 100;
  }
}

message CaidWithFactors {
  CaidFactors factors = 1;
  string caid = 2;
  string caidVersion = 3;
  string caidGenTime = 4;
  string caidLast = 5;
  string caidVersionLast = 6;
}

message Mixcfw{
  Fs fs = 1;
  string c1 = 2; // caid
  string c2 = 3; // caidLast
  string c3 = 4; // caidVersion
  string c4 = 5; // caidVersionLast
  string c5 = 6; // caidGenTime
}

message Fs {
  string f1 = 1; // carrierInfo,运营商 示例值: "中国移动"
  string f2 = 2; // machine,设备Machine 示例值: "iPhone10,3"
  string f3 = 3; // sysFileTime,系统更新时间 示例值: "1595214620.383940"
  string f4 = 4; // countryCode,国家 示例值: "CN"
  string f5 = 5; // deviceName,设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
  string f6 = 6; // timeZone,时区 示例值: "28800"
  string f7 = 7; // memory,物理内存容量 示例值: "3955589120"
  string f8 = 8; // disk,硬盘容量 示例值: "63900340224"
  string f9 = 9; // language,语言 示例值: "zh-Hans-CN"
  string f10 = 10; // systemVersion,系统版本 示例值: "14.0"
  string f11 = 11; // bootTimeInSec,设备启动时间（秒） 示例值: "**********"
  string f12 = 12; // model,设备Model 示例值: "D22AP"
  string f13 = 13; // mntId,挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
  string f14 = 14; // deviceInitTime,设备初始化时间 示例值: "**********.*********"
}

message PromotionInfo {
  string activationType = 1;
  string utmSource = 2;
  string activationChannel = 3;
  string accountId = 4;
  float activateInterval = 5;
}

message NegativeFeedback{
  repeated string adComplain = 1;
  string shakeForbid = 2;
}

message BidRequestV2 {
  string id = 1;
  repeated Imp imp = 2;
  App app = 3;
  Device device = 4;
  User user = 5;
  string apiVersion = 6;
  repeated string installedApp = 7;
  Tag tag = 8;
  InnerData innerData = 9;
  Ext ext = 10;

  message Imp{
    string id = 7;
    string tagId = 8;
    AdSlotSize adslotSize = 9;
    double bidfloor = 10;
    bool deeplink = 11;
    int32 secure = 12;
    repeated Deal dealList = 13;
  }

  message AdSlotSize{
    int32 width = 14;
    int32 height = 15;
    repeated string mimes = 16;
    int32 size = 17;
    int32 titleLength = 18;
    int32 descLength = 19;
    int32 minDuration = 20;
    int32 maxDuration = 21;
  }

  message App{
    string appName = 22;
    string bundle = 23;
    string appVersion = 24;
  }

  message PosIdClicked{
    string posId = 1;
    int32 clickedNum = 2;
  }

  message Tag{
    string city = 22;
    string versioncode = 23;
    string band = 24;
    bool idfa_fill = 25;
    bool android_id_fill = 26;
    string network = 27;
    int32 installed_time = 28;
    string start_type = 29;
    int32 hot_start_exposal_num = 30;
    int32 cold_start_exposal_num = 31;
    int32 hot_start_click_num = 32;
    int32 cold_start_click_num = 33;
    string user_value = 35;
    bool force_old = 36;
    string label = 38;
    string custom_parameter = 39;
    PromotionInfo promotionInfo = 40;
    repeated PosIdClicked posIdClickedList = 41;
    float arpu = 42;
    int32 os = 43;
    optional NegativeFeedback negativeFeedback = 44;
  }

  message Device{
    string os = 25;
    string osv = 26;
    string imei = 27;
    string imeiMd5 = 28;
    string oaid = 29;
    string oaidMd5 = 30;
    string androidId = 31;
    string idfa = 32;
    string idfaMd5 = 33;
    string mac = 34;
    string macMd5 = 35;
    string ip = 36;
    string ipV6 = 37;
    string ua = 38;
    int32 connectionType = 39;
    string brand = 40;
    string make = 41;
    string model= 42;
    string hwv = 43;
    int32 carrier = 44;
    string mccMnc = 45;
    int32 screenHeight = 46;
    int32 screenWidth = 47;
    int32 ppi = 48;
    Geo geo = 49;
    string appList = 50;
    string bootMark = 51;
    string updateMark = 52;
    string verCodeOfHms = 53;
    string verCodeOfAG = 54;
    string romVersion  = 55;
    int32 orientation = 56;
    string bootTimeSec = 57;
    string phoneName = 58;
    int64 memorySize = 59;
    int64 diskSize = 60;
    string osUpdateTimeSec = 61;
    string modelCode  = 62;
    string timeZone  = 63;
    CaidWithFactors caidWithFactors = 69;
    bool wxInstalled = 70;
    Mixcfw mixcfw = 71;
    string updateMark1 = 74; // 爱奇艺专用
    // 废弃
    string fileTime  = 64;
    string deviceBirthTime  = 65;
    string caidVersion  = 66;
    string caid = 67;
    string paid = 68;
    string c3  = 72; // caidVersion mix
    string c1 = 73; // caid mix
  }

  message Deal{
    string id = 57;
    double bidFloor = 58;
  }

  message Geo{
    int32 type = 59;
    double lat = 60;
    double lon = 61;
    string country  = 62;
  }

  message User{
    int32 age = 63;
    int32 gender = 64;
  }

  message Ext{
    SdkBidExt sdkBidExt = 1;
    repeated SDKExt sdkExt = 2;
    message SDKExt {
      string tagID = 1;
      string sdkType = 2;
      repeated SDKInfo sdkInfo = 3;
    }
  }

  message SdkBidExt{
    string ylhBuyerId = 1; // 优量汇SDK获取的buyerID
    string ylhOpensdkVer = 3; // 优量汇SDKopenSDK 版本，用于判断是否能支持微信小程序广告
    bool ylhSupportSplashZoomout = 5 ; // 优量汇SDK是否支持开屏 V+
    string ylhSdkInfo = 6; // 优量汇SDK获取的SDKInfo，缺失时将影响广告推荐效果，强烈建议开发者全量上传
  }
}

message SDKInfo {
  string type = 1;
  string value = 2;
}

message BidResponseV2 {
  string id = 65;
  string bidID = 66;
  repeated SeatBid seatBid = 67;
  Ext ext = 78;

  message SeatBid{
    repeated Bid bidList = 68;
  }

  message Bid{
    string impID = 69;
    int32 adType = 70;
    int32 adStyle = 71;
    Item  item = 72;
    double price = 73;
    string nURL = 74;
    string lURL = 77;
    int64 dealType = 78;
    string crid = 99;
    string apiName = 104;
    BidTrace bidTrace = 105;
    InnerData innerData = 106;
    string cid = 107;
    ShakeSetting shakeSetting = 600;
    string logoURL = 601;
  }

  message InnerData{
    repeated int32 interactionType = 107;
    string StrategyId = 108;
    string TagId = 109;
    string Sensitivity = 110;
    int64 SensitivityNum = 111;
    float ExpectClickRatio = 112;
    string Batch = 113;
    int64 ExposureTimestamp = 114;
    string RequestId = 115;
  }

  message ShakeSetting {
    string shakeSwitch = 601;
    string shakeSensitivity = 602;
    int64 shake_one_or_two_sides = 603;
    bool use_new_shake_config = 604;
    int64 shake_sensitivity_density= 605;
    int64 shake_acceleration_max_x= 606;
    int64 shake_acceleration_max_y= 607;
    int64 shake_acceleration_max_z= 608;
    int64 shake_click_sensitivity_level= 609;
    int64 shake_click_time_length_min= 610;
    int64 time_count_shake_sensitivity_level= 611;
    int64 shake_sensor_callback_interval= 612;
  }

  message BidTrace{
    string trafficGroupID = 1;
    string trafficID = 2;
    string adGroupID = 3;
    string adPositionID = 4;
    string tagID = 5;
    string dspName = 6;
    string dspID = 7;
    repeated int64 interactionTypes = 8;
      string interactionStrategyID = 9;
  }

  message Item{
    string title = 75;
    string desc = 76;
    string icon = 77;
    string html = 78;
    int32 mediaStyle = 79;
    string downloadURL = 80;
    DownloadAppInfo downloadAppInfo = 81;
    string clickURL = 82;
    string dplURL = 83;
    repeated string imgList = 84;
    repeated string exposalURLList = 85;
    repeated string clickMonitorURLList = 86;
    Video video = 87;
    string miniProgramID = 88;
    string miniProgramPath = 89;
    int32 miniProgramType = 90;
    string miniProgramExtData = 99;
    repeated string miniProgramSuccessTrackURLList = 91;
    repeated string downloadTrackURLList = 92;
    repeated string downloadedTrackURLList = 93;
    repeated string installedTrackURLList = 94;
    repeated string dpSuccessTrackURLList = 95;
    repeated string actionTrackURLList = 96;
    string packageName = 97;
    repeated MonitorURL monitorURLList = 98;
  }

  message MonitorURL{
    string eventType = 1;
    repeated string URLList = 2;
  }

  message DownloadAppInfo{
    string appName = 88;
    string developer = 89;
    string version = 90;
    string packetSize = 91;
    string privacy = 92;
    string permission = 93;
    string desc = 101;
    string descURL = 102;
  }

  message Video{
    string videoURL = 94;
    int32 videoDuration = 95;
    string videoStartURL = 96;
    string videoFinishURL = 97;
    string videoVastXML = 98;
    string videoEndImgURL = 99;
    string videoPreImgURL = 100;
  }

  message Ext{
    repeated SDKExt sdkExt = 1;
    message SDKExt{
      string sdkType = 1;
      string dspID = 2;
      string tagID = 3;
      double price = 4;
      repeated SDKInfo sdkInfo = 5;
    }
  }
}

message SetAppListRequest{
  string deviceId = 1;
  string userId = 2;
  repeated AppInfo appList = 3;
}

message SetAppListResponse{}

message GetAppListRequest{
  string deviceId = 1;
  string userId = 2;
}

message AppInfo{
  string bundleId = 1;
  string schemaId = 2;
  int64 appId = 3;
}

message GetAppListResponse{
  repeated AppInfo appList  = 1;
}

message CaidFactors {
  string bootTimeInSec = 1; // 设备启动时间（秒） 示例值: "**********"
  string countryCode = 2; // 国家 示例值: "CN"
  string language = 3; // 语言 示例值: "zh-Hans-CN"
  string deviceName = 4; // 设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
  string systemVersion = 5; // 系统版本 示例值: "14.0"
  string machine = 6; // 设备Machine 示例值: "iPhone10,3"
  string carrierInfo = 7; // 运营商 示例值: "中国移动"
  string memory = 8; // 物理内存容量 示例值: "3955589120"
  string disk = 9; // 硬盘容量 示例值: "63900340224"
  string sysFileTime = 10; // 系统更新时间 示例值: "1595214620.383940"
  string model = 11; // 设备Model 示例值: "D22AP"
  string timeZone = 12; // 时区 示例值: "28800"
  string mntId = 13; // 挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
  string deviceInitTime = 14; // 设备初始化时间 示例值: "**********.*********"
}

message Factor2CaidRequest{
  string caidFactorsSum = 1;
  CaidFactors caidFactors = 2;
  string deviceId = 3;
}

message Factor2CaidResponse{
  string Caid  = 1;
  string CaidVersion = 2;
  string CaidGenTime = 3;
  string CaidLast = 4;
  string CaidVersionLast = 5;
}

message AdPosRequest{
  message PromotionInfo {
    string activationType = 1;
    string utmSource = 2;
    string activationChannel = 3;
    string accountId = 4;
    float activateInterval = 5;
  }
  message UserInfo{
    int64  versionCode = 1;
    string brand = 2;
    string deviceId = 3;
    string customParameter= 4;
    float arpu = 5;
    int64 os = 6;
  }
  int64 os = 1;
  int64 versionCode = 2;
  string deviceID = 3;
  PromotionInfo promotionInfo = 4;
  UserInfo userInfo = 5;
  optional NegativeFeedback negativeFeedback = 6;
}

message AdPosResponse{
  message ExposureInterval{
      int64  start = 1;
      int64  end = 2;
      float interval = 3;
  }
  message ExposureStrategy{
    string strategyId = 1;
    string strategyName = 2;
    repeated string adPosList= 3;
    int64  totalTimes = 4;
    repeated ExposureInterval intervals = 5;
  }
  repeated AdPos adPosList = 1;
  repeated sdkInfo sdkInfoList = 2;
  int32 grayScaleControl = 3; // 灰度控制:1-自建聚合, 2-topon
  message sdkInfo {
    string tagID = 1;
    string dspID = 2;
    string sdkType = 3;
    int64 adType = 4;
  }
  repeated ExposureStrategy exposureStrategyList = 4;
  string strategyGroupId = 5;
  repeated sdkInfo sdkInfoListV2 = 6;
}

message AdPos{
  string containerID = 1;
  int32 containerType = 2; // 容器类型
  int32 containerTypeV2 = 19; // containerTypeV2 与 rtb 保持一致
  int32 containerStyle = 3; // 容器样式
  int32 adTitleMaxLen = 4;
  int32 closeUnitPos = 5; // 关闭组件位置
  int32 feedbackType = 6; // 反馈组件
  int32 channelShowType = 7; // 渠道组件
  int32 downloadType = 8; // 下载类型
  repeated int32 triggerType = 9; // 按钮组件
  string pageID = 10;
  int32 position = 11; // 点位
  string adPosID = 12; // 广告位id
  int32 adPosStatus = 13; // 广告位状态
  int32 exposeCondition = 14; // 曝光条件
  int32 endCondition     = 15; // 结束条件
  repeated int32 materialType     = 17; // 物料类型
  repeated int32 interactionType  = 18; // 交互类型
  string adPositionName = 20; // 广告位名称
  string place = 21; // 页面位置
}

message ClientBiddingRequest{
  string requestID = 1;
}

message ClientBiddingResponse{
  repeated ClientBiddingTagList clientBiddingTagList = 1;
  int64 waterfallTotalTimeout = 2;
  message ClientBiddingTagList{
    int64 startTime = 1;
    int64 level = 2;
    repeated TagList tagList = 3;
    message TagList{
      string sdkType = 1;
      string dspID = 2;
      string tagID = 3;
      int64 dealType = 4;
      double price = 5;
    }
  }
}

message ExposureStrategyRequest{
  message PromotionInfo {
      string activationType = 1;
      string utmSource = 2;
      string activationChannel = 3;
      string accountId = 4;
      float activateInterval = 5;
  }
  message PosIdExposured{
      string posId = 1;
      int64 exposuredNum = 2;
      int64 lastExposureMs = 3;
  }

  message UserInfo{
      int64  versionCode = 1;
      string brand = 2;
      string deviceId = 3;
      string customParameter= 4;
      float arpu = 5;
      int64 os = 6;
      repeated PosIdExposured posIdExposuredList = 7;
      string posId = 8;
  }

  string requestID = 1;
  PromotionInfo promotionInfo = 2;
  UserInfo userInfo = 3;
  optional NegativeFeedback negativeFeedback = 4;
}

message ExposureStrategyResponse{
  bool  limit = 1;
  string strategyGroupId = 2;

  message ExposureStrategyIntervalTuple {
    string strategyId = 1;
    int64 exposureIntervalMs = 2;
  }
  repeated ExposureStrategyIntervalTuple strategyIntervalTupleList = 3;
  bool intervalTooLarge = 4;
}

message GetExpectClickRatioRequest{
  string strategyId =1 ;
}

message GetExpectClickRatioResponse{
  message ExpectClickRatio {
    string strategyId = 1;
    string tagId = 2;
    double expectClickRatio = 3;
  }
  repeated ExpectClickRatio expectClickRatioList = 1;
}

