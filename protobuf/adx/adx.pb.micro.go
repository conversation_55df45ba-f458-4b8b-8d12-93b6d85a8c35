// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: adx.proto

package go_micro_service_adx

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "github.com/micro/go-micro/v2/api"
	client "github.com/micro/go-micro/v2/client"
	server "github.com/micro/go-micro/v2/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for AdxService service

func NewAdxServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for AdxService service

type AdxService interface {
	AdxRTB(ctx context.Context, in *BidRequest, opts ...client.CallOption) (*BidResponse, error)
	AdxRTBV2(ctx context.Context, in *BidRequestV2, opts ...client.CallOption) (*BidResponseV2, error)
	Factor2Caid(ctx context.Context, in *Factor2CaidRequest, opts ...client.CallOption) (*Factor2CaidResponse, error)
	SetAppList(ctx context.Context, in *SetAppListRequest, opts ...client.CallOption) (*SetAppListResponse, error)
	GetAppList(ctx context.Context, in *GetAppListRequest, opts ...client.CallOption) (*GetAppListResponse, error)
	AdPos(ctx context.Context, in *AdPosRequest, opts ...client.CallOption) (*AdPosResponse, error)
	ClientBidding(ctx context.Context, in *ClientBiddingRequest, opts ...client.CallOption) (*ClientBiddingResponse, error)
	ExposureStrategy(ctx context.Context, in *ExposureStrategyRequest, opts ...client.CallOption) (*ExposureStrategyResponse, error)
	GetExpectClickRatio(ctx context.Context, in *GetExpectClickRatioRequest, opts ...client.CallOption) (*GetExpectClickRatioResponse, error)
}

type adxService struct {
	c    client.Client
	name string
}

func NewAdxService(name string, c client.Client) AdxService {
	return &adxService{
		c:    c,
		name: name,
	}
}

func (c *adxService) AdxRTB(ctx context.Context, in *BidRequest, opts ...client.CallOption) (*BidResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.AdxRTB", in)
	out := new(BidResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) AdxRTBV2(ctx context.Context, in *BidRequestV2, opts ...client.CallOption) (*BidResponseV2, error) {
	req := c.c.NewRequest(c.name, "AdxService.AdxRTBV2", in)
	out := new(BidResponseV2)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) Factor2Caid(ctx context.Context, in *Factor2CaidRequest, opts ...client.CallOption) (*Factor2CaidResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.Factor2Caid", in)
	out := new(Factor2CaidResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) SetAppList(ctx context.Context, in *SetAppListRequest, opts ...client.CallOption) (*SetAppListResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.SetAppList", in)
	out := new(SetAppListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) GetAppList(ctx context.Context, in *GetAppListRequest, opts ...client.CallOption) (*GetAppListResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.GetAppList", in)
	out := new(GetAppListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) AdPos(ctx context.Context, in *AdPosRequest, opts ...client.CallOption) (*AdPosResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.AdPos", in)
	out := new(AdPosResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) ClientBidding(ctx context.Context, in *ClientBiddingRequest, opts ...client.CallOption) (*ClientBiddingResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.ClientBidding", in)
	out := new(ClientBiddingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) ExposureStrategy(ctx context.Context, in *ExposureStrategyRequest, opts ...client.CallOption) (*ExposureStrategyResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.ExposureStrategy", in)
	out := new(ExposureStrategyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adxService) GetExpectClickRatio(ctx context.Context, in *GetExpectClickRatioRequest, opts ...client.CallOption) (*GetExpectClickRatioResponse, error) {
	req := c.c.NewRequest(c.name, "AdxService.GetExpectClickRatio", in)
	out := new(GetExpectClickRatioResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AdxService service

type AdxServiceHandler interface {
	AdxRTB(context.Context, *BidRequest, *BidResponse) error
	AdxRTBV2(context.Context, *BidRequestV2, *BidResponseV2) error
	Factor2Caid(context.Context, *Factor2CaidRequest, *Factor2CaidResponse) error
	SetAppList(context.Context, *SetAppListRequest, *SetAppListResponse) error
	GetAppList(context.Context, *GetAppListRequest, *GetAppListResponse) error
	AdPos(context.Context, *AdPosRequest, *AdPosResponse) error
	ClientBidding(context.Context, *ClientBiddingRequest, *ClientBiddingResponse) error
	ExposureStrategy(context.Context, *ExposureStrategyRequest, *ExposureStrategyResponse) error
	GetExpectClickRatio(context.Context, *GetExpectClickRatioRequest, *GetExpectClickRatioResponse) error
}

func RegisterAdxServiceHandler(s server.Server, hdlr AdxServiceHandler, opts ...server.HandlerOption) error {
	type adxService interface {
		AdxRTB(ctx context.Context, in *BidRequest, out *BidResponse) error
		AdxRTBV2(ctx context.Context, in *BidRequestV2, out *BidResponseV2) error
		Factor2Caid(ctx context.Context, in *Factor2CaidRequest, out *Factor2CaidResponse) error
		SetAppList(ctx context.Context, in *SetAppListRequest, out *SetAppListResponse) error
		GetAppList(ctx context.Context, in *GetAppListRequest, out *GetAppListResponse) error
		AdPos(ctx context.Context, in *AdPosRequest, out *AdPosResponse) error
		ClientBidding(ctx context.Context, in *ClientBiddingRequest, out *ClientBiddingResponse) error
		ExposureStrategy(ctx context.Context, in *ExposureStrategyRequest, out *ExposureStrategyResponse) error
		GetExpectClickRatio(ctx context.Context, in *GetExpectClickRatioRequest, out *GetExpectClickRatioResponse) error
	}
	type AdxService struct {
		adxService
	}
	h := &adxServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&AdxService{h}, opts...))
}

type adxServiceHandler struct {
	AdxServiceHandler
}

func (h *adxServiceHandler) AdxRTB(ctx context.Context, in *BidRequest, out *BidResponse) error {
	return h.AdxServiceHandler.AdxRTB(ctx, in, out)
}

func (h *adxServiceHandler) AdxRTBV2(ctx context.Context, in *BidRequestV2, out *BidResponseV2) error {
	return h.AdxServiceHandler.AdxRTBV2(ctx, in, out)
}

func (h *adxServiceHandler) Factor2Caid(ctx context.Context, in *Factor2CaidRequest, out *Factor2CaidResponse) error {
	return h.AdxServiceHandler.Factor2Caid(ctx, in, out)
}

func (h *adxServiceHandler) SetAppList(ctx context.Context, in *SetAppListRequest, out *SetAppListResponse) error {
	return h.AdxServiceHandler.SetAppList(ctx, in, out)
}

func (h *adxServiceHandler) GetAppList(ctx context.Context, in *GetAppListRequest, out *GetAppListResponse) error {
	return h.AdxServiceHandler.GetAppList(ctx, in, out)
}

func (h *adxServiceHandler) AdPos(ctx context.Context, in *AdPosRequest, out *AdPosResponse) error {
	return h.AdxServiceHandler.AdPos(ctx, in, out)
}

func (h *adxServiceHandler) ClientBidding(ctx context.Context, in *ClientBiddingRequest, out *ClientBiddingResponse) error {
	return h.AdxServiceHandler.ClientBidding(ctx, in, out)
}

func (h *adxServiceHandler) ExposureStrategy(ctx context.Context, in *ExposureStrategyRequest, out *ExposureStrategyResponse) error {
	return h.AdxServiceHandler.ExposureStrategy(ctx, in, out)
}

func (h *adxServiceHandler) GetExpectClickRatio(ctx context.Context, in *GetExpectClickRatioRequest, out *GetExpectClickRatioResponse) error {
	return h.AdxServiceHandler.GetExpectClickRatio(ctx, in, out)
}
