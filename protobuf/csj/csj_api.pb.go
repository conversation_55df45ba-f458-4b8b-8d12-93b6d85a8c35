// Copyright 2021 Bytedance Inc. All Rights Reserved.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.28.3
// source: csj_api.proto

package go_micro_service_csj

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidResponse_Ad_MaterialMeta_ImageMode int32

const (
	BidResponse_Ad_MaterialMeta_SINGLE BidResponse_Ad_MaterialMeta_ImageMode = 1 //小图
	BidResponse_Ad_MaterialMeta_GROUP  BidResponse_Ad_MaterialMeta_ImageMode = 2 //组图
	BidResponse_Ad_MaterialMeta_VIDEO  BidResponse_Ad_MaterialMeta_ImageMode = 4 //视频
)

// Enum value maps for BidResponse_Ad_MaterialMeta_ImageMode.
var (
	BidResponse_Ad_MaterialMeta_ImageMode_name = map[int32]string{
		1: "SINGLE",
		2: "GROUP",
		4: "VIDEO",
	}
	BidResponse_Ad_MaterialMeta_ImageMode_value = map[string]int32{
		"SINGLE": 1,
		"GROUP":  2,
		"VIDEO":  4,
	}
)

func (x BidResponse_Ad_MaterialMeta_ImageMode) Enum() *BidResponse_Ad_MaterialMeta_ImageMode {
	p := new(BidResponse_Ad_MaterialMeta_ImageMode)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialMeta_ImageMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialMeta_ImageMode) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_api_proto_enumTypes[0].Descriptor()
}

func (BidResponse_Ad_MaterialMeta_ImageMode) Type() protoreflect.EnumType {
	return &file_csj_api_proto_enumTypes[0]
}

func (x BidResponse_Ad_MaterialMeta_ImageMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialMeta_ImageMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialMeta_ImageMode(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_ImageMode.Descriptor instead.
func (BidResponse_Ad_MaterialMeta_ImageMode) EnumDescriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

// 创意的交互类型
type BidResponse_Ad_MaterialMeta_InteractionType int32

const (
	BidResponse_Ad_MaterialMeta_NO_INTERACTION BidResponse_Ad_MaterialMeta_InteractionType = 1 // 无动作，针对有些开屏不支持点击。
	BidResponse_Ad_MaterialMeta_SURFING        BidResponse_Ad_MaterialMeta_InteractionType = 2 // 使用浏览器打开网页。
	BidResponse_Ad_MaterialMeta_DOWNLOAD       BidResponse_Ad_MaterialMeta_InteractionType = 4 // 下载应用。
)

// Enum value maps for BidResponse_Ad_MaterialMeta_InteractionType.
var (
	BidResponse_Ad_MaterialMeta_InteractionType_name = map[int32]string{
		1: "NO_INTERACTION",
		2: "SURFING",
		4: "DOWNLOAD",
	}
	BidResponse_Ad_MaterialMeta_InteractionType_value = map[string]int32{
		"NO_INTERACTION": 1,
		"SURFING":        2,
		"DOWNLOAD":       4,
	}
)

func (x BidResponse_Ad_MaterialMeta_InteractionType) Enum() *BidResponse_Ad_MaterialMeta_InteractionType {
	p := new(BidResponse_Ad_MaterialMeta_InteractionType)
	*p = x
	return p
}

func (x BidResponse_Ad_MaterialMeta_InteractionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Ad_MaterialMeta_InteractionType) Descriptor() protoreflect.EnumDescriptor {
	return file_csj_api_proto_enumTypes[1].Descriptor()
}

func (BidResponse_Ad_MaterialMeta_InteractionType) Type() protoreflect.EnumType {
	return &file_csj_api_proto_enumTypes[1]
}

func (x BidResponse_Ad_MaterialMeta_InteractionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Ad_MaterialMeta_InteractionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Ad_MaterialMeta_InteractionType(num)
	return nil
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_InteractionType.Descriptor instead.
func (BidResponse_Ad_MaterialMeta_InteractionType) EnumDescriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId  *string              `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`    // 必填，自定义的请求id，长度不超过32位，adx应保证其唯一性。
	ApiVersion *string              `protobuf:"bytes,2,opt,name=api_version,json=apiVersion" json:"api_version,omitempty"` // 选填，此API的版本。
	AdxName    *string              `protobuf:"bytes,3,req,name=adx_name,json=adxName" json:"adx_name,omitempty"`          // 必填，用于判断哪家adx
	Ip         *string              `protobuf:"bytes,4,req,name=ip" json:"ip,omitempty"`                                   // 必填，设备的ip，用于定位，地域定向
	Adslots    []*BidRequest_AdSlot `protobuf:"bytes,5,rep,name=adslots" json:"adslots,omitempty"`                         // 必填，广告位的信息
	SdkToken   *string              `protobuf:"bytes,6,req,name=sdk_token,json=sdkToken" json:"sdk_token,omitempty"`       // 必填，客户端从穿山甲SDK获取的token
	Ext        []*BidRequest_Ext    `protobuf:"bytes,7,rep,name=ext" json:"ext,omitempty"`                                 // 选填，扩展参数
	Timeout    *uint32              `protobuf:"varint,8,opt,name=timeout" json:"timeout,omitempty"`                        // 选填，adx给该请求预留的时间，单位为ms
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{0}
}

func (x *BidRequest) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetAdxName() string {
	if x != nil && x.AdxName != nil {
		return *x.AdxName
	}
	return ""
}

func (x *BidRequest) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest) GetAdslots() []*BidRequest_AdSlot {
	if x != nil {
		return x.Adslots
	}
	return nil
}

func (x *BidRequest) GetSdkToken() string {
	if x != nil && x.SdkToken != nil {
		return *x.SdkToken
	}
	return ""
}

func (x *BidRequest) GetExt() []*BidRequest_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

func (x *BidRequest) GetTimeout() uint32 {
	if x != nil && x.Timeout != nil {
		return *x.Timeout
	}
	return 0
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId        *string           `protobuf:"bytes,1,req,name=request_id,json=requestId" json:"request_id,omitempty"`                         // 必填，BidRequest中所带的request_id。
	StatusCode       *int64            `protobuf:"varint,2,req,name=status_code,json=statusCode" json:"status_code,omitempty"`                     // 必填，请求时的状态码。
	Reason           *int32            `protobuf:"varint,3,req,name=reason" json:"reason,omitempty"`                                               // 必填，请求时的状态码。
	Desc             *string           `protobuf:"bytes,4,opt,name=desc" json:"desc,omitempty"`                                                    // 可选，未返回广告的原因
	ProcessingTimeMs *uint32           `protobuf:"varint,5,opt,name=processing_time_ms,json=processingTimeMs" json:"processing_time_ms,omitempty"` // 可选，从收到请求到返回响应所用的时间。
	Ads              []*BidResponse_Ad `protobuf:"bytes,6,rep,name=ads" json:"ads,omitempty"`                                                      // 可选，当竞价时必填，竞价广告列表，与adslots对应。
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1}
}

func (x *BidResponse) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

func (x *BidResponse) GetStatusCode() int64 {
	if x != nil && x.StatusCode != nil {
		return *x.StatusCode
	}
	return 0
}

func (x *BidResponse) GetReason() int32 {
	if x != nil && x.Reason != nil {
		return *x.Reason
	}
	return 0
}

func (x *BidResponse) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *BidResponse) GetProcessingTimeMs() uint32 {
	if x != nil && x.ProcessingTimeMs != nil {
		return *x.ProcessingTimeMs
	}
	return 0
}

func (x *BidResponse) GetAds() []*BidResponse_Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

type BidRequest_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  *string `protobuf:"bytes,1,req,name=name" json:"name,omitempty"`
	Value *string `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
}

func (x *BidRequest_Ext) Reset() {
	*x = BidRequest_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Ext) ProtoMessage() {}

func (x *BidRequest_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Ext.ProtoReflect.Descriptor instead.
func (*BidRequest_Ext) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BidRequest_Ext) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Ext) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type BidRequest_AdSlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`                                    // 必填，广告位标识符
	MinimumCpm *uint64 `protobuf:"varint,2,opt,name=minimum_cpm,json=minimumCpm" json:"minimum_cpm,omitempty"` // 选填，最低的cpm出价, 单位为分/cpm
	AdCount    *uint32 `protobuf:"varint,3,opt,name=ad_count,json=adCount" json:"ad_count,omitempty"`          // 选填，需要的广告数量，可覆盖客户端设置
}

func (x *BidRequest_AdSlot) Reset() {
	*x = BidRequest_AdSlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdSlot) ProtoMessage() {}

func (x *BidRequest_AdSlot) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdSlot.ProtoReflect.Descriptor instead.
func (*BidRequest_AdSlot) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BidRequest_AdSlot) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_AdSlot) GetMinimumCpm() uint64 {
	if x != nil && x.MinimumCpm != nil {
		return *x.MinimumCpm
	}
	return 0
}

func (x *BidRequest_AdSlot) GetAdCount() uint32 {
	if x != nil && x.AdCount != nil {
		return *x.AdCount
	}
	return 0
}

type BidResponse_Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdId     *string                      `protobuf:"bytes,1,req,name=ad_id,json=adId" json:"ad_id,omitempty"` // 必填，广告id
	Price    *uint64                      `protobuf:"varint,2,req,name=price" json:"price,omitempty"`          // 必填，出价，cpm/元
	Creative *BidResponse_Ad_MaterialMeta `protobuf:"bytes,3,req,name=creative" json:"creative,omitempty"`     // 必填，素材。
}

func (x *BidResponse_Ad) Reset() {
	*x = BidResponse_Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad) ProtoMessage() {}

func (x *BidResponse_Ad) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidResponse_Ad) GetAdId() string {
	if x != nil && x.AdId != nil {
		return *x.AdId
	}
	return ""
}

func (x *BidResponse_Ad) GetPrice() uint64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_Ad) GetCreative() *BidResponse_Ad_MaterialMeta {
	if x != nil {
		return x.Creative
	}
	return nil
}

type BidResponse_Ad_MaterialMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeId      *string                                      `protobuf:"bytes,1,req,name=creative_id,json=creativeId" json:"creative_id,omitempty"`                                                                                       // 可选。广告物料的唯一标识
	ImageMode       *BidResponse_Ad_MaterialMeta_ImageMode       `protobuf:"varint,2,req,name=image_mode,json=imageMode,enum=go.micro.service.csj.BidResponse_Ad_MaterialMeta_ImageMode" json:"image_mode,omitempty"`                         // 必填，该广告的创意类型
	InteractionType *BidResponse_Ad_MaterialMeta_InteractionType `protobuf:"varint,3,req,name=interaction_type,json=interactionType,enum=go.micro.service.csj.BidResponse_Ad_MaterialMeta_InteractionType" json:"interaction_type,omitempty"` // 必填：广告支持的交互类型。
	Image           *BidResponse_Ad_MaterialMeta_Image           `protobuf:"bytes,4,opt,name=image" json:"image,omitempty"`                                                                                                                   // 可选。素材图片。
	ImageList       []*BidResponse_Ad_MaterialMeta_Image         `protobuf:"bytes,5,rep,name=image_list,json=imageList" json:"image_list,omitempty"`                                                                                          // 多图
	Video           *BidResponse_Ad_MaterialMeta_Video           `protobuf:"bytes,6,opt,name=video" json:"video,omitempty"`                                                                                                                   // 视频
	TargetUrl       *string                                      `protobuf:"bytes,7,opt,name=target_url,json=targetUrl" json:"target_url,omitempty"`                                                                                          // 可选。创意的落地页url。
	DownloadUrl     *string                                      `protobuf:"bytes,8,opt,name=download_url,json=downloadUrl" json:"download_url,omitempty"`                                                                                    // 可选,应用下载必填。应用下载url。
	Title           *string                                      `protobuf:"bytes,9,opt,name=title" json:"title,omitempty"`                                                                                                                   // 可选。广告标题。
	Description     *string                                      `protobuf:"bytes,10,opt,name=description" json:"description,omitempty"`                                                                                                      // 可选。广告描述。
	AppName         *string                                      `protobuf:"bytes,11,opt,name=app_name,json=appName" json:"app_name,omitempty"`                                                                                               // 可选。针对应用下载类广告。
	PackageName     *string                                      `protobuf:"bytes,12,opt,name=package_name,json=packageName" json:"package_name,omitempty"`                                                                                   // 可选。安卓应用下载包名。
	WinNoticeUrl    []string                                     `protobuf:"bytes,13,rep,name=win_notice_url,json=winNoticeUrl" json:"win_notice_url,omitempty"`                                                                              // 可选。获胜通知的url列表。
	Source          *string                                      `protobuf:"bytes,14,opt,name=source" json:"source,omitempty"`                                                                                                                // 落地页的来源
	Icon            *string                                      `protobuf:"bytes,15,opt,name=icon" json:"icon,omitempty"`                                                                                                                    // icon
	Adm             *string                                      `protobuf:"bytes,16,req,name=adm" json:"adm,omitempty"`                                                                                                                      // 必填，应替换结算价后透传到客户端
	LossNoticeUrl   []string                                     `protobuf:"bytes,17,rep,name=loss_notice_url,json=lossNoticeUrl" json:"loss_notice_url,omitempty"`                                                                           // 可选。竞价失败通知的url列表。
}

func (x *BidResponse_Ad_MaterialMeta) Reset() {
	*x = BidResponse_Ad_MaterialMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidResponse_Ad_MaterialMeta) GetCreativeId() string {
	if x != nil && x.CreativeId != nil {
		return *x.CreativeId
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetImageMode() BidResponse_Ad_MaterialMeta_ImageMode {
	if x != nil && x.ImageMode != nil {
		return *x.ImageMode
	}
	return BidResponse_Ad_MaterialMeta_SINGLE
}

func (x *BidResponse_Ad_MaterialMeta) GetInteractionType() BidResponse_Ad_MaterialMeta_InteractionType {
	if x != nil && x.InteractionType != nil {
		return *x.InteractionType
	}
	return BidResponse_Ad_MaterialMeta_NO_INTERACTION
}

func (x *BidResponse_Ad_MaterialMeta) GetImage() *BidResponse_Ad_MaterialMeta_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetImageList() []*BidResponse_Ad_MaterialMeta_Image {
	if x != nil {
		return x.ImageList
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetVideo() *BidResponse_Ad_MaterialMeta_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetTargetUrl() string {
	if x != nil && x.TargetUrl != nil {
		return *x.TargetUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetDownloadUrl() string {
	if x != nil && x.DownloadUrl != nil {
		return *x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetWinNoticeUrl() []string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return nil
}

func (x *BidResponse_Ad_MaterialMeta) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetAdm() string {
	if x != nil && x.Adm != nil {
		return *x.Adm
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta) GetLossNoticeUrl() []string {
	if x != nil {
		return x.LossNoticeUrl
	}
	return nil
}

// 图片素材信息。
type BidResponse_Ad_MaterialMeta_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    *string `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	Width  *uint32 `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`
	Height *uint32 `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`
}

func (x *BidResponse_Ad_MaterialMeta_Image) Reset() {
	*x = BidResponse_Ad_MaterialMeta_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta_Image) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta_Image) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta_Image) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Image) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

// 视频
type BidResponse_Ad_MaterialMeta_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverHeight   *uint32  `protobuf:"varint,1,opt,name=cover_height,json=coverHeight" json:"cover_height,omitempty"`
	CoverWidth    *uint32  `protobuf:"varint,2,opt,name=cover_width,json=coverWidth" json:"cover_width,omitempty"`
	CoverUrl      *string  `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl" json:"cover_url,omitempty"`
	Resolution    *string  `protobuf:"bytes,4,opt,name=resolution" json:"resolution,omitempty"`
	Size          *uint64  `protobuf:"varint,5,opt,name=size" json:"size,omitempty"`
	VideoDuration *float32 `protobuf:"fixed32,6,opt,name=video_duration,json=videoDuration" json:"video_duration,omitempty"`
	VideoUrl      *string  `protobuf:"bytes,7,opt,name=video_url,json=videoUrl" json:"video_url,omitempty"`
}

func (x *BidResponse_Ad_MaterialMeta_Video) Reset() {
	*x = BidResponse_Ad_MaterialMeta_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_csj_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Ad_MaterialMeta_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Ad_MaterialMeta_Video) ProtoMessage() {}

func (x *BidResponse_Ad_MaterialMeta_Video) ProtoReflect() protoreflect.Message {
	mi := &file_csj_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Ad_MaterialMeta_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Ad_MaterialMeta_Video) Descriptor() ([]byte, []int) {
	return file_csj_api_proto_rawDescGZIP(), []int{1, 0, 0, 1}
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverHeight() uint32 {
	if x != nil && x.CoverHeight != nil {
		return *x.CoverHeight
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverWidth() uint32 {
	if x != nil && x.CoverWidth != nil {
		return *x.CoverWidth
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetCoverUrl() string {
	if x != nil && x.CoverUrl != nil {
		return *x.CoverUrl
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetResolution() string {
	if x != nil && x.Resolution != nil {
		return *x.Resolution
	}
	return ""
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetSize() uint64 {
	if x != nil && x.Size != nil {
		return *x.Size
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetVideoDuration() float32 {
	if x != nil && x.VideoDuration != nil {
		return *x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Ad_MaterialMeta_Video) GetVideoUrl() string {
	if x != nil && x.VideoUrl != nil {
		return *x.VideoUrl
	}
	return ""
}

var File_csj_api_proto protoreflect.FileDescriptor

var file_csj_api_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x63, 0x73, 0x6a, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x14, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x63, 0x73, 0x6a, 0x22, 0xb0, 0x03, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x02, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x41, 0x0a, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x07, 0x61, 0x64, 0x73, 0x6c, 0x6f,
	0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x64, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x06, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x73, 0x64, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x36, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45,
	0x78, 0x74, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x1a, 0x2f, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x1a, 0x54, 0x0a, 0x06, 0x41, 0x64, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x70, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x43, 0x70, 0x6d, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb3, 0x0c, 0x0a, 0x0b, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65,
	0x4d, 0x73, 0x12, 0x36, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x1a, 0xd1, 0x0a, 0x0a, 0x02, 0x41,
	0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x02, 0x28, 0x04, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x1a, 0xd0, 0x09, 0x0a, 0x0c,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x5a, 0x0a,
	0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x02, 0x28,
	0x0e, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x6c, 0x0a, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x02, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x73,
	0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4d,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x63, 0x73, 0x6a, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61,
	0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x77,
	0x69, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x64, 0x6d, 0x18, 0x10,
	0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x61, 0x64, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x73,
	0x73, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72,
	0x6c, 0x1a, 0x47, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0xe0, 0x01, 0x0a, 0x05, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x2d, 0x0a,
	0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49,
	0x4e, 0x47, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10,
	0x02, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0x04, 0x22, 0x40, 0x0a, 0x0f,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x0e, 0x4e, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x52, 0x46, 0x49, 0x4e, 0x47, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x04, 0x42, 0x19,
	0x5a, 0x17, 0x2e, 0x2f, 0x3b, 0x67, 0x6f, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x73, 0x6a,
}

var (
	file_csj_api_proto_rawDescOnce sync.Once
	file_csj_api_proto_rawDescData = file_csj_api_proto_rawDesc
)

func file_csj_api_proto_rawDescGZIP() []byte {
	file_csj_api_proto_rawDescOnce.Do(func() {
		file_csj_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_csj_api_proto_rawDescData)
	})
	return file_csj_api_proto_rawDescData
}

var file_csj_api_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_csj_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_csj_api_proto_goTypes = []interface{}{
	(BidResponse_Ad_MaterialMeta_ImageMode)(0),       // 0: go.micro.service.csj.BidResponse.Ad.MaterialMeta.ImageMode
	(BidResponse_Ad_MaterialMeta_InteractionType)(0), // 1: go.micro.service.csj.BidResponse.Ad.MaterialMeta.InteractionType
	(*BidRequest)(nil),                               // 2: go.micro.service.csj.BidRequest
	(*BidResponse)(nil),                              // 3: go.micro.service.csj.BidResponse
	(*BidRequest_Ext)(nil),                           // 4: go.micro.service.csj.BidRequest.Ext
	(*BidRequest_AdSlot)(nil),                        // 5: go.micro.service.csj.BidRequest.AdSlot
	(*BidResponse_Ad)(nil),                           // 6: go.micro.service.csj.BidResponse.Ad
	(*BidResponse_Ad_MaterialMeta)(nil),              // 7: go.micro.service.csj.BidResponse.Ad.MaterialMeta
	(*BidResponse_Ad_MaterialMeta_Image)(nil),        // 8: go.micro.service.csj.BidResponse.Ad.MaterialMeta.Image
	(*BidResponse_Ad_MaterialMeta_Video)(nil),        // 9: go.micro.service.csj.BidResponse.Ad.MaterialMeta.Video
}
var file_csj_api_proto_depIdxs = []int32{
	5, // 0: go.micro.service.csj.BidRequest.adslots:type_name -> go.micro.service.csj.BidRequest.AdSlot
	4, // 1: go.micro.service.csj.BidRequest.ext:type_name -> go.micro.service.csj.BidRequest.Ext
	6, // 2: go.micro.service.csj.BidResponse.ads:type_name -> go.micro.service.csj.BidResponse.Ad
	7, // 3: go.micro.service.csj.BidResponse.Ad.creative:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta
	0, // 4: go.micro.service.csj.BidResponse.Ad.MaterialMeta.image_mode:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta.ImageMode
	1, // 5: go.micro.service.csj.BidResponse.Ad.MaterialMeta.interaction_type:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta.InteractionType
	8, // 6: go.micro.service.csj.BidResponse.Ad.MaterialMeta.image:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta.Image
	8, // 7: go.micro.service.csj.BidResponse.Ad.MaterialMeta.image_list:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta.Image
	9, // 8: go.micro.service.csj.BidResponse.Ad.MaterialMeta.video:type_name -> go.micro.service.csj.BidResponse.Ad.MaterialMeta.Video
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_csj_api_proto_init() }
func file_csj_api_proto_init() {
	if File_csj_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_csj_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdSlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_csj_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Ad_MaterialMeta_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_csj_api_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_csj_api_proto_goTypes,
		DependencyIndexes: file_csj_api_proto_depIdxs,
		EnumInfos:         file_csj_api_proto_enumTypes,
		MessageInfos:      file_csj_api_proto_msgTypes,
	}.Build()
	File_csj_api_proto = out.File
	file_csj_api_proto_rawDesc = nil
	file_csj_api_proto_goTypes = nil
	file_csj_api_proto_depIdxs = nil
}
