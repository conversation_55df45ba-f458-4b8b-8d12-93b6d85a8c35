// Copyright 2021 Bytedance Inc. All Rights Reserved.
syntax = "proto2";

package go.micro.service.csj;

option go_package = "./;go_micro_service_csj";

message BidRequest {
  required string request_id = 1;     // 必填，自定义的请求id，长度不超过32位，adx应保证其唯一性。
  optional string api_version = 2;    // 选填，此API的版本。
  required string adx_name = 3;       // 必填，用于判断哪家adx
  required string ip = 4;             // 必填，设备的ip，用于定位，地域定向
  repeated AdSlot adslots = 5;        // 必填，广告位的信息
  required string sdk_token = 6;      // 必填，客户端从穿山甲SDK获取的token
  repeated Ext ext = 7;               // 选填，扩展参数
  optional uint32 timeout = 8;        // 选填，adx给该请求预留的时间，单位为ms

  message Ext {
    required string name = 1;
    required string value = 2;
  }

  message AdSlot {
    required string id = 1;             // 必填，广告位标识符
    optional uint64 minimum_cpm = 2;    // 选填，最低的cpm出价, 单位为分/cpm
    optional uint32 ad_count = 3;       // 选填，需要的广告数量，可覆盖客户端设置
  }
}

message BidResponse {
  required string request_id = 1;             // 必填，BidRequest中所带的request_id。
  required int64 status_code = 2;             // 必填，请求时的状态码。
  required int32 reason = 3;                  // 必填，请求时的状态码。
  optional string desc = 4;                   // 可选，未返回广告的原因
  optional uint32 processing_time_ms = 5;     // 可选，从收到请求到返回响应所用的时间。
  repeated Ad ads = 6;                        // 可选，当竞价时必填，竞价广告列表，与adslots对应。

  message Ad {
    required string ad_id = 1;          // 必填，广告id
    required uint64 price = 2;          // 必填，出价，cpm/元
    required MaterialMeta creative = 3; // 必填，素材。

    message MaterialMeta {
      required string creative_id = 1; // 可选。广告物料的唯一标识
      required ImageMode image_mode = 2; // 必填，该广告的创意类型
      required InteractionType interaction_type = 3; // 必填：广告支持的交互类型。
      optional Image image = 4; // 可选。素材图片。
      repeated Image image_list = 5; // 多图
      optional Video video = 6; // 视频
      optional string target_url = 7; // 可选。创意的落地页url。
      optional string download_url = 8; // 可选,应用下载必填。应用下载url。
      optional string title = 9; // 可选。广告标题。
      optional string description = 10; // 可选。广告描述。
      optional string app_name = 11; // 可选。针对应用下载类广告。
      optional string package_name = 12; // 可选。安卓应用下载包名。
      repeated string win_notice_url = 13; // 可选。获胜通知的url列表。
      optional string source = 14; // 落地页的来源
      optional string icon = 15; // icon
      required string adm = 16; // 必填，应替换结算价后透传到客户端
      repeated string loss_notice_url = 17; // 可选。竞价失败通知的url列表。

      enum ImageMode {
        SINGLE = 1; //小图
        GROUP = 2; //组图
        VIDEO = 4; //视频
      }

      // 创意的交互类型
      enum InteractionType {
        NO_INTERACTION = 1; // 无动作，针对有些开屏不支持点击。
        SURFING = 2; // 使用浏览器打开网页。
        DOWNLOAD = 4; // 下载应用。
      }

      // 图片素材信息。
      message Image {
        optional string url = 1;
        optional uint32 width = 2;
        optional uint32 height = 3;
      }

      // 视频
      message Video {
        optional uint32 cover_height = 1;
        optional uint32 cover_width = 2;
        optional string cover_url = 3;
        optional string resolution = 4;
        optional uint64 size = 5;
        optional float video_duration = 6;
        optional string video_url = 7;
      }
    }
  }
}
