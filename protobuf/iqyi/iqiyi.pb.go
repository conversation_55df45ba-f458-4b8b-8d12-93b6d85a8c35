// Copyright (C) 2023 iQIYI.COM - All Rights Reserved
//
// This file is part of SSP.
// Unauthorized copy of this file, via any medium is strictly prohibited.
// Proprietary and Confidential.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.0
// 	protoc        v3.20.3
// source: iqiyi.proto

package go_micro_service_iqyi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BidRequest_Imp_AdType int32

const (
	BidRequest_Imp_ROLL    BidRequest_Imp_AdType = 0
	BidRequest_Imp_FEEDS   BidRequest_Imp_AdType = 1
	BidRequest_Imp_OPENING BidRequest_Imp_AdType = 2
	BidRequest_Imp_PAUSE   BidRequest_Imp_AdType = 3
)

// Enum value maps for BidRequest_Imp_AdType.
var (
	BidRequest_Imp_AdType_name = map[int32]string{
		0: "ROLL",
		1: "FEEDS",
		2: "OPENING",
		3: "PAUSE",
	}
	BidRequest_Imp_AdType_value = map[string]int32{
		"ROLL":    0,
		"FEEDS":   1,
		"OPENING": 2,
		"PAUSE":   3,
	}
)

func (x BidRequest_Imp_AdType) Enum() *BidRequest_Imp_AdType {
	p := new(BidRequest_Imp_AdType)
	*p = x
	return p
}

func (x BidRequest_Imp_AdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_AdType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[0].Descriptor()
}

func (BidRequest_Imp_AdType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[0]
}

func (x BidRequest_Imp_AdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_AdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_AdType(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_AdType.Descriptor instead.
func (BidRequest_Imp_AdType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 0}
}

// Video format.
type BidRequest_Imp_VideoFormat int32

const (
	BidRequest_Imp_VIDEO_ANY BidRequest_Imp_VideoFormat = 0
	BidRequest_Imp_VIDEO_FLV BidRequest_Imp_VideoFormat = 1
	BidRequest_Imp_VIDEO_MP4 BidRequest_Imp_VideoFormat = 2
)

// Enum value maps for BidRequest_Imp_VideoFormat.
var (
	BidRequest_Imp_VideoFormat_name = map[int32]string{
		0: "VIDEO_ANY",
		1: "VIDEO_FLV",
		2: "VIDEO_MP4",
	}
	BidRequest_Imp_VideoFormat_value = map[string]int32{
		"VIDEO_ANY": 0,
		"VIDEO_FLV": 1,
		"VIDEO_MP4": 2,
	}
)

func (x BidRequest_Imp_VideoFormat) Enum() *BidRequest_Imp_VideoFormat {
	p := new(BidRequest_Imp_VideoFormat)
	*p = x
	return p
}

func (x BidRequest_Imp_VideoFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_VideoFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[1].Descriptor()
}

func (BidRequest_Imp_VideoFormat) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[1]
}

func (x BidRequest_Imp_VideoFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_VideoFormat) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_VideoFormat(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_VideoFormat.Descriptor instead.
func (BidRequest_Imp_VideoFormat) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 1}
}

// The following table indicates the options for video linearity.
// "In-stream" or "linear" video refers to pre-roll, post-roll,
// or mid-roll video ads where the user is forced to watch ad in
// order to see the video content.
// "Overlay" or "non-linear" refer to ads that are shown on top
// of the video content.
type BidRequest_Imp_Video_VideoLinearity int32

const (
	BidRequest_Imp_Video_LINEAR     BidRequest_Imp_Video_VideoLinearity = 1 // Linear/In-stream
	BidRequest_Imp_Video_NON_LINEAR BidRequest_Imp_Video_VideoLinearity = 2 // Non-linear/Overlay
	BidRequest_Imp_Video_PAUSE      BidRequest_Imp_Video_VideoLinearity = 3
)

// Enum value maps for BidRequest_Imp_Video_VideoLinearity.
var (
	BidRequest_Imp_Video_VideoLinearity_name = map[int32]string{
		1: "LINEAR",
		2: "NON_LINEAR",
		3: "PAUSE",
	}
	BidRequest_Imp_Video_VideoLinearity_value = map[string]int32{
		"LINEAR":     1,
		"NON_LINEAR": 2,
		"PAUSE":      3,
	}
)

func (x BidRequest_Imp_Video_VideoLinearity) Enum() *BidRequest_Imp_Video_VideoLinearity {
	p := new(BidRequest_Imp_Video_VideoLinearity)
	*p = x
	return p
}

func (x BidRequest_Imp_Video_VideoLinearity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Video_VideoLinearity) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[2].Descriptor()
}

func (BidRequest_Imp_Video_VideoLinearity) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[2]
}

func (x BidRequest_Imp_Video_VideoLinearity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Video_VideoLinearity) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Video_VideoLinearity(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Video_VideoLinearity.Descriptor instead.
func (BidRequest_Imp_Video_VideoLinearity) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 0, 0}
}

type BidRequest_Imp_Native_Image_ImageAssetType int32

const (
	BidRequest_Imp_Native_Image_ICON BidRequest_Imp_Native_Image_ImageAssetType = 1
	BidRequest_Imp_Native_Image_LOGO BidRequest_Imp_Native_Image_ImageAssetType = 2
	BidRequest_Imp_Native_Image_MAIN BidRequest_Imp_Native_Image_ImageAssetType = 3
)

// Enum value maps for BidRequest_Imp_Native_Image_ImageAssetType.
var (
	BidRequest_Imp_Native_Image_ImageAssetType_name = map[int32]string{
		1: "ICON",
		2: "LOGO",
		3: "MAIN",
	}
	BidRequest_Imp_Native_Image_ImageAssetType_value = map[string]int32{
		"ICON": 1,
		"LOGO": 2,
		"MAIN": 3,
	}
)

func (x BidRequest_Imp_Native_Image_ImageAssetType) Enum() *BidRequest_Imp_Native_Image_ImageAssetType {
	p := new(BidRequest_Imp_Native_Image_ImageAssetType)
	*p = x
	return p
}

func (x BidRequest_Imp_Native_Image_ImageAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Imp_Native_Image_ImageAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[3].Descriptor()
}

func (BidRequest_Imp_Native_Image_ImageAssetType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[3]
}

func (x BidRequest_Imp_Native_Image_ImageAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Imp_Native_Image_ImageAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Imp_Native_Image_ImageAssetType(num)
	return nil
}

// Deprecated: Use BidRequest_Imp_Native_Image_ImageAssetType.Descriptor instead.
func (BidRequest_Imp_Native_Image_ImageAssetType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 1, 0, 0}
}

type BidRequest_Content_ProductionQuality int32

const (
	BidRequest_Content_QUALITY_UNKNOWN BidRequest_Content_ProductionQuality = 0
	BidRequest_Content_PROFESSIONAL    BidRequest_Content_ProductionQuality = 1
	BidRequest_Content_PROSUMER        BidRequest_Content_ProductionQuality = 2
	BidRequest_Content_USER_GENERATED  BidRequest_Content_ProductionQuality = 3
)

// Enum value maps for BidRequest_Content_ProductionQuality.
var (
	BidRequest_Content_ProductionQuality_name = map[int32]string{
		0: "QUALITY_UNKNOWN",
		1: "PROFESSIONAL",
		2: "PROSUMER",
		3: "USER_GENERATED",
	}
	BidRequest_Content_ProductionQuality_value = map[string]int32{
		"QUALITY_UNKNOWN": 0,
		"PROFESSIONAL":    1,
		"PROSUMER":        2,
		"USER_GENERATED":  3,
	}
)

func (x BidRequest_Content_ProductionQuality) Enum() *BidRequest_Content_ProductionQuality {
	p := new(BidRequest_Content_ProductionQuality)
	*p = x
	return p
}

func (x BidRequest_Content_ProductionQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_ProductionQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[4].Descriptor()
}

func (BidRequest_Content_ProductionQuality) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[4]
}

func (x BidRequest_Content_ProductionQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_ProductionQuality) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_ProductionQuality(num)
	return nil
}

// Deprecated: Use BidRequest_Content_ProductionQuality.Descriptor instead.
func (BidRequest_Content_ProductionQuality) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 4, 0}
}

type BidRequest_Content_ContentContext int32

const (
	BidRequest_Content_VIDEO           BidRequest_Content_ContentContext = 1
	BidRequest_Content_GAME            BidRequest_Content_ContentContext = 2
	BidRequest_Content_MUSIC           BidRequest_Content_ContentContext = 3
	BidRequest_Content_APPLICATION     BidRequest_Content_ContentContext = 4
	BidRequest_Content_TEXT            BidRequest_Content_ContentContext = 5
	BidRequest_Content_OTHER           BidRequest_Content_ContentContext = 6
	BidRequest_Content_CONTEXT_UNKNOWN BidRequest_Content_ContentContext = 7
)

// Enum value maps for BidRequest_Content_ContentContext.
var (
	BidRequest_Content_ContentContext_name = map[int32]string{
		1: "VIDEO",
		2: "GAME",
		3: "MUSIC",
		4: "APPLICATION",
		5: "TEXT",
		6: "OTHER",
		7: "CONTEXT_UNKNOWN",
	}
	BidRequest_Content_ContentContext_value = map[string]int32{
		"VIDEO":           1,
		"GAME":            2,
		"MUSIC":           3,
		"APPLICATION":     4,
		"TEXT":            5,
		"OTHER":           6,
		"CONTEXT_UNKNOWN": 7,
	}
)

func (x BidRequest_Content_ContentContext) Enum() *BidRequest_Content_ContentContext {
	p := new(BidRequest_Content_ContentContext)
	*p = x
	return p
}

func (x BidRequest_Content_ContentContext) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_ContentContext) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[5].Descriptor()
}

func (BidRequest_Content_ContentContext) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[5]
}

func (x BidRequest_Content_ContentContext) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_ContentContext) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_ContentContext(num)
	return nil
}

// Deprecated: Use BidRequest_Content_ContentContext.Descriptor instead.
func (BidRequest_Content_ContentContext) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 4, 1}
}

type BidRequest_Content_QAGMediaRating int32

const (
	BidRequest_Content_ALL_AUDIENCES    BidRequest_Content_QAGMediaRating = 1
	BidRequest_Content_EVERYONE_OVER_12 BidRequest_Content_QAGMediaRating = 2
	BidRequest_Content_MATURE           BidRequest_Content_QAGMediaRating = 3
)

// Enum value maps for BidRequest_Content_QAGMediaRating.
var (
	BidRequest_Content_QAGMediaRating_name = map[int32]string{
		1: "ALL_AUDIENCES",
		2: "EVERYONE_OVER_12",
		3: "MATURE",
	}
	BidRequest_Content_QAGMediaRating_value = map[string]int32{
		"ALL_AUDIENCES":    1,
		"EVERYONE_OVER_12": 2,
		"MATURE":           3,
	}
)

func (x BidRequest_Content_QAGMediaRating) Enum() *BidRequest_Content_QAGMediaRating {
	p := new(BidRequest_Content_QAGMediaRating)
	*p = x
	return p
}

func (x BidRequest_Content_QAGMediaRating) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Content_QAGMediaRating) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[6].Descriptor()
}

func (BidRequest_Content_QAGMediaRating) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[6]
}

func (x BidRequest_Content_QAGMediaRating) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Content_QAGMediaRating) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Content_QAGMediaRating(num)
	return nil
}

// Deprecated: Use BidRequest_Content_QAGMediaRating.Descriptor instead.
func (BidRequest_Content_QAGMediaRating) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 4, 2}
}

type BidRequest_Device_DeviceType int32

const (
	BidRequest_Device_UNKNOWN_DEVICE    BidRequest_Device_DeviceType = 1
	BidRequest_Device_PERSONAL_COMPUTER BidRequest_Device_DeviceType = 2
	BidRequest_Device_TV                BidRequest_Device_DeviceType = 3
	BidRequest_Device_PHONE             BidRequest_Device_DeviceType = 4
	BidRequest_Device_PAD               BidRequest_Device_DeviceType = 5
)

// Enum value maps for BidRequest_Device_DeviceType.
var (
	BidRequest_Device_DeviceType_name = map[int32]string{
		1: "UNKNOWN_DEVICE",
		2: "PERSONAL_COMPUTER",
		3: "TV",
		4: "PHONE",
		5: "PAD",
	}
	BidRequest_Device_DeviceType_value = map[string]int32{
		"UNKNOWN_DEVICE":    1,
		"PERSONAL_COMPUTER": 2,
		"TV":                3,
		"PHONE":             4,
		"PAD":               5,
	}
)

func (x BidRequest_Device_DeviceType) Enum() *BidRequest_Device_DeviceType {
	p := new(BidRequest_Device_DeviceType)
	*p = x
	return p
}

func (x BidRequest_Device_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[7].Descriptor()
}

func (BidRequest_Device_DeviceType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[7]
}

func (x BidRequest_Device_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_DeviceType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_DeviceType.Descriptor instead.
func (BidRequest_Device_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6, 0}
}

type BidRequest_Device_TvType int32

const (
	BidRequest_Device_UNKNOWN     BidRequest_Device_TvType = 0
	BidRequest_Device_TV_TYPE_OTT BidRequest_Device_TvType = 1
	// Set-Top-Box
	BidRequest_Device_TV_TYPE_STB  BidRequest_Device_TvType = 2
	BidRequest_Device_TV_TYPE_IPTV BidRequest_Device_TvType = 3
)

// Enum value maps for BidRequest_Device_TvType.
var (
	BidRequest_Device_TvType_name = map[int32]string{
		0: "UNKNOWN",
		1: "TV_TYPE_OTT",
		2: "TV_TYPE_STB",
		3: "TV_TYPE_IPTV",
	}
	BidRequest_Device_TvType_value = map[string]int32{
		"UNKNOWN":      0,
		"TV_TYPE_OTT":  1,
		"TV_TYPE_STB":  2,
		"TV_TYPE_IPTV": 3,
	}
)

func (x BidRequest_Device_TvType) Enum() *BidRequest_Device_TvType {
	p := new(BidRequest_Device_TvType)
	*p = x
	return p
}

func (x BidRequest_Device_TvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_TvType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[8].Descriptor()
}

func (BidRequest_Device_TvType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[8]
}

func (x BidRequest_Device_TvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_TvType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_TvType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_TvType.Descriptor instead.
func (BidRequest_Device_TvType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6, 1}
}

type BidRequest_Device_ConnectionType int32

const (
	BidRequest_Device_CONNECTION_UNKNOWN BidRequest_Device_ConnectionType = 0
	BidRequest_Device_ETHERNET           BidRequest_Device_ConnectionType = 1
	BidRequest_Device_WIFI               BidRequest_Device_ConnectionType = 2
	BidRequest_Device_CELL_UNKNOWN       BidRequest_Device_ConnectionType = 3
	BidRequest_Device_CELL_2G            BidRequest_Device_ConnectionType = 4
	BidRequest_Device_CELL_3G            BidRequest_Device_ConnectionType = 5
	BidRequest_Device_CELL_4G            BidRequest_Device_ConnectionType = 6
	BidRequest_Device_CELL_5G            BidRequest_Device_ConnectionType = 7
)

// Enum value maps for BidRequest_Device_ConnectionType.
var (
	BidRequest_Device_ConnectionType_name = map[int32]string{
		0: "CONNECTION_UNKNOWN",
		1: "ETHERNET",
		2: "WIFI",
		3: "CELL_UNKNOWN",
		4: "CELL_2G",
		5: "CELL_3G",
		6: "CELL_4G",
		7: "CELL_5G",
	}
	BidRequest_Device_ConnectionType_value = map[string]int32{
		"CONNECTION_UNKNOWN": 0,
		"ETHERNET":           1,
		"WIFI":               2,
		"CELL_UNKNOWN":       3,
		"CELL_2G":            4,
		"CELL_3G":            5,
		"CELL_4G":            6,
		"CELL_5G":            7,
	}
)

func (x BidRequest_Device_ConnectionType) Enum() *BidRequest_Device_ConnectionType {
	p := new(BidRequest_Device_ConnectionType)
	*p = x
	return p
}

func (x BidRequest_Device_ConnectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Device_ConnectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[9].Descriptor()
}

func (BidRequest_Device_ConnectionType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[9]
}

func (x BidRequest_Device_ConnectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Device_ConnectionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Device_ConnectionType(num)
	return nil
}

// Deprecated: Use BidRequest_Device_ConnectionType.Descriptor instead.
func (BidRequest_Device_ConnectionType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6, 2}
}

type BidRequest_Geo_LocationType int32

const (
	BidRequest_Geo_GPS_LOCATION  BidRequest_Geo_LocationType = 1
	BidRequest_Geo_IP            BidRequest_Geo_LocationType = 2
	BidRequest_Geo_USER_PROVIDED BidRequest_Geo_LocationType = 3
)

// Enum value maps for BidRequest_Geo_LocationType.
var (
	BidRequest_Geo_LocationType_name = map[int32]string{
		1: "GPS_LOCATION",
		2: "IP",
		3: "USER_PROVIDED",
	}
	BidRequest_Geo_LocationType_value = map[string]int32{
		"GPS_LOCATION":  1,
		"IP":            2,
		"USER_PROVIDED": 3,
	}
)

func (x BidRequest_Geo_LocationType) Enum() *BidRequest_Geo_LocationType {
	p := new(BidRequest_Geo_LocationType)
	*p = x
	return p
}

func (x BidRequest_Geo_LocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidRequest_Geo_LocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[10].Descriptor()
}

func (BidRequest_Geo_LocationType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[10]
}

func (x BidRequest_Geo_LocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidRequest_Geo_LocationType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidRequest_Geo_LocationType(num)
	return nil
}

// Deprecated: Use BidRequest_Geo_LocationType.Descriptor instead.
func (BidRequest_Geo_LocationType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 7, 0}
}

type BidResponse_Bid_AdActionType int32

const (
	// Open in webview.
	BidResponse_Bid_OPEN_IN_WEBVIEW BidResponse_Bid_AdActionType = 1
	// Open app deeplink.
	BidResponse_Bid_OPEN_APP_DEEPLINK BidResponse_Bid_AdActionType = 2
	// Download app.
	BidResponse_Bid_DOWNLOAD_APP BidResponse_Bid_AdActionType = 3
	// Open app universal link.
	BidResponse_Bid_OPEN_APP_UNIVERSAL_LINK BidResponse_Bid_AdActionType = 4
	// Open mini app.
	BidResponse_Bid_OPEN_MINI_APP BidResponse_Bid_AdActionType = 5
)

// Enum value maps for BidResponse_Bid_AdActionType.
var (
	BidResponse_Bid_AdActionType_name = map[int32]string{
		1: "OPEN_IN_WEBVIEW",
		2: "OPEN_APP_DEEPLINK",
		3: "DOWNLOAD_APP",
		4: "OPEN_APP_UNIVERSAL_LINK",
		5: "OPEN_MINI_APP",
	}
	BidResponse_Bid_AdActionType_value = map[string]int32{
		"OPEN_IN_WEBVIEW":         1,
		"OPEN_APP_DEEPLINK":       2,
		"DOWNLOAD_APP":            3,
		"OPEN_APP_UNIVERSAL_LINK": 4,
		"OPEN_MINI_APP":           5,
	}
)

func (x BidResponse_Bid_AdActionType) Enum() *BidResponse_Bid_AdActionType {
	p := new(BidResponse_Bid_AdActionType)
	*p = x
	return p
}

func (x BidResponse_Bid_AdActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_AdActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[11].Descriptor()
}

func (BidResponse_Bid_AdActionType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[11]
}

func (x BidResponse_Bid_AdActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_AdActionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_AdActionType(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_AdActionType.Descriptor instead.
func (BidResponse_Bid_AdActionType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 0}
}

type BidResponse_Bid_CreativeType int32

const (
	BidResponse_Bid_IMG   BidResponse_Bid_CreativeType = 0
	BidResponse_Bid_VIDEO BidResponse_Bid_CreativeType = 1
)

// Enum value maps for BidResponse_Bid_CreativeType.
var (
	BidResponse_Bid_CreativeType_name = map[int32]string{
		0: "IMG",
		1: "VIDEO",
	}
	BidResponse_Bid_CreativeType_value = map[string]int32{
		"IMG":   0,
		"VIDEO": 1,
	}
)

func (x BidResponse_Bid_CreativeType) Enum() *BidResponse_Bid_CreativeType {
	p := new(BidResponse_Bid_CreativeType)
	*p = x
	return p
}

func (x BidResponse_Bid_CreativeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_CreativeType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[12].Descriptor()
}

func (BidResponse_Bid_CreativeType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[12]
}

func (x BidResponse_Bid_CreativeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_CreativeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_CreativeType(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_CreativeType.Descriptor instead.
func (BidResponse_Bid_CreativeType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 1}
}

type BidResponse_Bid_CreativeDirection int32

const (
	BidResponse_Bid_UNKNOWN    BidResponse_Bid_CreativeDirection = 0
	BidResponse_Bid_HORIZONTAL BidResponse_Bid_CreativeDirection = 1
	BidResponse_Bid_VERTICAL   BidResponse_Bid_CreativeDirection = 2
)

// Enum value maps for BidResponse_Bid_CreativeDirection.
var (
	BidResponse_Bid_CreativeDirection_name = map[int32]string{
		0: "UNKNOWN",
		1: "HORIZONTAL",
		2: "VERTICAL",
	}
	BidResponse_Bid_CreativeDirection_value = map[string]int32{
		"UNKNOWN":    0,
		"HORIZONTAL": 1,
		"VERTICAL":   2,
	}
)

func (x BidResponse_Bid_CreativeDirection) Enum() *BidResponse_Bid_CreativeDirection {
	p := new(BidResponse_Bid_CreativeDirection)
	*p = x
	return p
}

func (x BidResponse_Bid_CreativeDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_CreativeDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[13].Descriptor()
}

func (BidResponse_Bid_CreativeDirection) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[13]
}

func (x BidResponse_Bid_CreativeDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_CreativeDirection) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_CreativeDirection(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_CreativeDirection.Descriptor instead.
func (BidResponse_Bid_CreativeDirection) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 2}
}

type BidResponse_Bid_Opening_Type int32

const (
	BidResponse_Bid_Opening_NON_IMMERSIVE             BidResponse_Bid_Opening_Type = 0
	BidResponse_Bid_Opening_NON_IMMERSIVE_FULL_SCREEN BidResponse_Bid_Opening_Type = 1
	BidResponse_Bid_Opening_IMMERSIVE                 BidResponse_Bid_Opening_Type = 2
)

// Enum value maps for BidResponse_Bid_Opening_Type.
var (
	BidResponse_Bid_Opening_Type_name = map[int32]string{
		0: "NON_IMMERSIVE",
		1: "NON_IMMERSIVE_FULL_SCREEN",
		2: "IMMERSIVE",
	}
	BidResponse_Bid_Opening_Type_value = map[string]int32{
		"NON_IMMERSIVE":             0,
		"NON_IMMERSIVE_FULL_SCREEN": 1,
		"IMMERSIVE":                 2,
	}
)

func (x BidResponse_Bid_Opening_Type) Enum() *BidResponse_Bid_Opening_Type {
	p := new(BidResponse_Bid_Opening_Type)
	*p = x
	return p
}

func (x BidResponse_Bid_Opening_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_Opening_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[14].Descriptor()
}

func (BidResponse_Bid_Opening_Type) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[14]
}

func (x BidResponse_Bid_Opening_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_Opening_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_Opening_Type(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_Opening_Type.Descriptor instead.
func (BidResponse_Bid_Opening_Type) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 1, 0}
}

type BidResponse_Bid_Image_ImageAssetType int32

const (
	BidResponse_Bid_Image_ICON BidResponse_Bid_Image_ImageAssetType = 1
	BidResponse_Bid_Image_LOGO BidResponse_Bid_Image_ImageAssetType = 2
	BidResponse_Bid_Image_MAIN BidResponse_Bid_Image_ImageAssetType = 3
)

// Enum value maps for BidResponse_Bid_Image_ImageAssetType.
var (
	BidResponse_Bid_Image_ImageAssetType_name = map[int32]string{
		1: "ICON",
		2: "LOGO",
		3: "MAIN",
	}
	BidResponse_Bid_Image_ImageAssetType_value = map[string]int32{
		"ICON": 1,
		"LOGO": 2,
		"MAIN": 3,
	}
)

func (x BidResponse_Bid_Image_ImageAssetType) Enum() *BidResponse_Bid_Image_ImageAssetType {
	p := new(BidResponse_Bid_Image_ImageAssetType)
	*p = x
	return p
}

func (x BidResponse_Bid_Image_ImageAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BidResponse_Bid_Image_ImageAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_iqiyi_proto_enumTypes[15].Descriptor()
}

func (BidResponse_Bid_Image_ImageAssetType) Type() protoreflect.EnumType {
	return &file_iqiyi_proto_enumTypes[15]
}

func (x BidResponse_Bid_Image_ImageAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *BidResponse_Bid_Image_ImageAssetType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = BidResponse_Bid_Image_ImageAssetType(num)
	return nil
}

// Deprecated: Use BidResponse_Bid_Image_ImageAssetType.Descriptor instead.
func (BidResponse_Bid_Image_ImageAssetType) EnumDescriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 5, 0}
}

type Entry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *string `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Value *string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
}

func (x *Entry) Reset() {
	*x = Entry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entry) ProtoMessage() {}

func (x *Entry) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entry.ProtoReflect.Descriptor instead.
func (*Entry) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{0}
}

func (x *Entry) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Entry) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID of the bid request, provided by the exchange.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// The timestamp in millisecond to send this request.
	Timestamp *int64 `protobuf:"varint,2,req,name=timestamp" json:"timestamp,omitempty"`
	// Array of Imp objects representing the impressions offered.
	// At least 1 Imp object is required.
	Imp []*BidRequest_Imp `protobuf:"bytes,3,rep,name=imp" json:"imp,omitempty"`
	// 0: site, 1: app.
	Resourcetype *int32 `protobuf:"varint,4,req,name=resourcetype" json:"resourcetype,omitempty"`
	// Details via a Site object about the publisher's website.
	// Only applicable for websites.
	Site *BidRequest_Site `protobuf:"bytes,5,opt,name=site" json:"site,omitempty"`
	// Details via an App object about the publisher's app.
	// Only applicable for apps.
	App *BidRequest_App `protobuf:"bytes,6,opt,name=app" json:"app,omitempty"`
	// Details via a Device object about the user's
	// device to which the impression will be delivered.
	Device *BidRequest_Device `protobuf:"bytes,7,opt,name=device" json:"device,omitempty"`
	// Details via a User object about the human user of the device.
	User *BidRequest_User `protobuf:"bytes,8,opt,name=user" json:"user,omitempty"`
	// Indicator of test mode, where 0 = live mode, 1 = test mode..
	Test            *bool                        `protobuf:"varint,9,opt,name=test,def=0" json:"test,omitempty"`
	DeduplicatedIds []*BidRequest_DeduplicatedId `protobuf:"bytes,10,rep,name=deduplicated_ids,json=deduplicatedIds" json:"deduplicated_ids,omitempty"`
}

// Default values for BidRequest fields.
const (
	Default_BidRequest_Test = bool(false)
)

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1}
}

func (x *BidRequest) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetResourcetype() int32 {
	if x != nil && x.Resourcetype != nil {
		return *x.Resourcetype
	}
	return 0
}

func (x *BidRequest) GetSite() *BidRequest_Site {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetTest() bool {
	if x != nil && x.Test != nil {
		return *x.Test
	}
	return Default_BidRequest_Test
}

func (x *BidRequest) GetDeduplicatedIds() []*BidRequest_DeduplicatedId {
	if x != nil {
		return x.DeduplicatedIds
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the bid request to which this is a response.
	Id *string `protobuf:"bytes,1,req,name=id" json:"id,omitempty"`
	// Array of 1+ Bid objects each related to an impression.
	Bid []*BidResponse_Bid `protobuf:"bytes,5,rep,name=bid" json:"bid,omitempty"`
	// Key-value entries with unique keys.
	// Supported entries:
	// key: "user_session_id", value(example): "123456", it is the same value in
	//   one user session.
	ExtendedEntries []*Entry `protobuf:"bytes,6,rep,name=extended_entries,json=extendedEntries" json:"extended_entries,omitempty"`
	DebugInfo       *string  `protobuf:"bytes,4,opt,name=debug_info,json=debugInfo" json:"debug_info,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2}
}

func (x *BidResponse) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidResponse) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *BidResponse) GetExtendedEntries() []*Entry {
	if x != nil {
		return x.ExtendedEntries
	}
	return nil
}

func (x *BidResponse) GetDebugInfo() string {
	if x != nil && x.DebugInfo != nil {
		return *x.DebugInfo
	}
	return ""
}

type QxForbiddenCacheInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstTimestamp *int64 `protobuf:"varint,1,opt,name=first_timestamp,json=firstTimestamp,def=0" json:"first_timestamp,omitempty"`
	LastTimestamp  *int64 `protobuf:"varint,2,opt,name=last_timestamp,json=lastTimestamp,def=0" json:"last_timestamp,omitempty"`
	ForbiddenCount *int64 `protobuf:"varint,3,opt,name=forbidden_count,json=forbiddenCount,def=0" json:"forbidden_count,omitempty"`
	SkippedCount   *int64 `protobuf:"varint,4,opt,name=skipped_count,json=skippedCount,def=0" json:"skipped_count,omitempty"`
}

// Default values for QxForbiddenCacheInfo fields.
const (
	Default_QxForbiddenCacheInfo_FirstTimestamp = int64(0)
	Default_QxForbiddenCacheInfo_LastTimestamp  = int64(0)
	Default_QxForbiddenCacheInfo_ForbiddenCount = int64(0)
	Default_QxForbiddenCacheInfo_SkippedCount   = int64(0)
)

func (x *QxForbiddenCacheInfo) Reset() {
	*x = QxForbiddenCacheInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QxForbiddenCacheInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QxForbiddenCacheInfo) ProtoMessage() {}

func (x *QxForbiddenCacheInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QxForbiddenCacheInfo.ProtoReflect.Descriptor instead.
func (*QxForbiddenCacheInfo) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{3}
}

func (x *QxForbiddenCacheInfo) GetFirstTimestamp() int64 {
	if x != nil && x.FirstTimestamp != nil {
		return *x.FirstTimestamp
	}
	return Default_QxForbiddenCacheInfo_FirstTimestamp
}

func (x *QxForbiddenCacheInfo) GetLastTimestamp() int64 {
	if x != nil && x.LastTimestamp != nil {
		return *x.LastTimestamp
	}
	return Default_QxForbiddenCacheInfo_LastTimestamp
}

func (x *QxForbiddenCacheInfo) GetForbiddenCount() int64 {
	if x != nil && x.ForbiddenCount != nil {
		return *x.ForbiddenCount
	}
	return Default_QxForbiddenCacheInfo_ForbiddenCount
}

func (x *QxForbiddenCacheInfo) GetSkippedCount() int64 {
	if x != nil && x.SkippedCount != nil {
		return *x.SkippedCount
	}
	return Default_QxForbiddenCacheInfo_SkippedCount
}

// This object describes an ad placement or impression being auctioned.
type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ad zone id representing the impressions offered.
	AdzoneId *string `protobuf:"bytes,1,req,name=adzone_id,json=adzoneId" json:"adzone_id,omitempty"`
	// Media ad zone id.
	MediaAdzoneId *string `protobuf:"bytes,7,opt,name=media_adzone_id,json=mediaAdzoneId" json:"media_adzone_id,omitempty"`
	// The ad type of this impression.
	AdType *BidRequest_Imp_AdType `protobuf:"varint,4,opt,name=ad_type,json=adType,enum=go.micro.service.iqyi.BidRequest_Imp_AdType" json:"ad_type,omitempty"`
	// Required if this impression is offered as a video ad opportunity.
	Video *BidRequest_Imp_Video `protobuf:"bytes,2,opt,name=video" json:"video,omitempty"`
	// Required if this impression is offered as a native ad opportunity.
	Native *BidRequest_Imp_Native `protobuf:"bytes,3,opt,name=native" json:"native,omitempty"`
	// The bid floor price.
	Bidfloor *int32 `protobuf:"varint,6,opt,name=bidfloor" json:"bidfloor,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BidRequest_Imp) GetAdzoneId() string {
	if x != nil && x.AdzoneId != nil {
		return *x.AdzoneId
	}
	return ""
}

func (x *BidRequest_Imp) GetMediaAdzoneId() string {
	if x != nil && x.MediaAdzoneId != nil {
		return *x.MediaAdzoneId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdType() BidRequest_Imp_AdType {
	if x != nil && x.AdType != nil {
		return *x.AdType
	}
	return BidRequest_Imp_ROLL
}

func (x *BidRequest_Imp) GetVideo() *BidRequest_Imp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp) GetNative() *BidRequest_Imp_Native {
	if x != nil {
		return x.Native
	}
	return nil
}

func (x *BidRequest_Imp) GetBidfloor() int32 {
	if x != nil && x.Bidfloor != nil {
		return *x.Bidfloor
	}
	return 0
}

// Site information
type BidRequest_Site struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Site name.
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// Domain of the site.
	Domain *string `protobuf:"bytes,2,opt,name=domain" json:"domain,omitempty"`
	// Array of IAB content categories of the site.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Array of IAB content categories that describe
	// the current page or view of the site.
	Pagecat []string `protobuf:"bytes,4,rep,name=pagecat" json:"pagecat,omitempty"`
	// URL of the page where the impression will be shown.
	Page *string `protobuf:"bytes,5,opt,name=page" json:"page,omitempty"`
	// Indicates if the site has a privacy policy, where 0 = no, 1 = yes.
	Privacypolicy *bool `protobuf:"varint,6,opt,name=privacypolicy" json:"privacypolicy,omitempty"`
	// Referrer URL that caused navigation to the current page.
	Ref *string `protobuf:"bytes,7,opt,name=ref" json:"ref,omitempty"`
	// Search string that caused navigation to the current page.
	Search *string `protobuf:"bytes,8,opt,name=search" json:"search,omitempty"`
	// Details about the Publisher of the site.
	Publisher *BidRequest_Publisher `protobuf:"bytes,9,opt,name=publisher" json:"publisher,omitempty"`
	// Details about the Content within the site.
	Content *BidRequest_Content `protobuf:"bytes,10,opt,name=content" json:"content,omitempty"`
	// Comma separated list of keywords about this site.
	Keywords *string `protobuf:"bytes,11,opt,name=keywords" json:"keywords,omitempty"`
	// Mobile-optimized signal, where 0 = no, 1 = yes.
	Mobile *bool `protobuf:"varint,12,opt,name=mobile" json:"mobile,omitempty"`
}

func (x *BidRequest_Site) Reset() {
	*x = BidRequest_Site{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Site) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Site) ProtoMessage() {}

func (x *BidRequest_Site) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Site.ProtoReflect.Descriptor instead.
func (*BidRequest_Site) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BidRequest_Site) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Site) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *BidRequest_Site) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Site) GetPagecat() []string {
	if x != nil {
		return x.Pagecat
	}
	return nil
}

func (x *BidRequest_Site) GetPage() string {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return ""
}

func (x *BidRequest_Site) GetPrivacypolicy() bool {
	if x != nil && x.Privacypolicy != nil {
		return *x.Privacypolicy
	}
	return false
}

func (x *BidRequest_Site) GetRef() string {
	if x != nil && x.Ref != nil {
		return *x.Ref
	}
	return ""
}

func (x *BidRequest_Site) GetSearch() string {
	if x != nil && x.Search != nil {
		return *x.Search
	}
	return ""
}

func (x *BidRequest_Site) GetPublisher() *BidRequest_Publisher {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidRequest_Site) GetContent() *BidRequest_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *BidRequest_Site) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_Site) GetMobile() bool {
	if x != nil && x.Mobile != nil {
		return *x.Mobile
	}
	return false
}

// App information.
type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Application name.
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// Domain of the application. For example, "mygame.foo.com".
	Domain *string `protobuf:"bytes,2,opt,name=domain" json:"domain,omitempty"`
	// Array of IAB content categories of the app.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Application version.
	Ver *string `protobuf:"bytes,4,opt,name=ver" json:"ver,omitempty"`
	// Application bundle or package name (for example, com.foo.mygame).
	Bundle *string `protobuf:"bytes,5,opt,name=bundle" json:"bundle,omitempty"`
	// 0 = app is free, 1 = the app is a paid version.
	Paid *bool `protobuf:"varint,6,opt,name=paid" json:"paid,omitempty"`
	// Details about the Publisher of the site.
	Publisher *BidRequest_Publisher `protobuf:"bytes,7,opt,name=publisher" json:"publisher,omitempty"`
	// Details about the Content within the site.
	Content *BidRequest_Content `protobuf:"bytes,8,opt,name=content" json:"content,omitempty"`
	// Comma separated list of keywords about the app.
	Keywords *string `protobuf:"bytes,9,opt,name=keywords" json:"keywords,omitempty"`
	// App store URL for an installed app.
	Storeurl *string `protobuf:"bytes,10,opt,name=storeurl" json:"storeurl,omitempty"`
	// deeplink support state.
	Deeplinkstate *int32 `protobuf:"varint,11,opt,name=deeplinkstate" json:"deeplinkstate,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BidRequest_App) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_App) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

func (x *BidRequest_App) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_App) GetVer() string {
	if x != nil && x.Ver != nil {
		return *x.Ver
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil && x.Bundle != nil {
		return *x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetPaid() bool {
	if x != nil && x.Paid != nil {
		return *x.Paid
	}
	return false
}

func (x *BidRequest_App) GetPublisher() *BidRequest_Publisher {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *BidRequest_App) GetContent() *BidRequest_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *BidRequest_App) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_App) GetStoreurl() string {
	if x != nil && x.Storeurl != nil {
		return *x.Storeurl
	}
	return ""
}

func (x *BidRequest_App) GetDeeplinkstate() int32 {
	if x != nil && x.Deeplinkstate != nil {
		return *x.Deeplinkstate
	}
	return 0
}

type BidRequest_Publisher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Exchange-specific publisher ID.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Publisher name (may be aliased at the publisher’s request).
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Array of IAB content categories that describe the publisher.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Highest level domain of the publisher (e.g., “publisher.com”).
	Domain *string `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`
}

func (x *BidRequest_Publisher) Reset() {
	*x = BidRequest_Publisher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Publisher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Publisher) ProtoMessage() {}

func (x *BidRequest_Publisher) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Publisher.ProtoReflect.Descriptor instead.
func (*BidRequest_Publisher) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 3}
}

func (x *BidRequest_Publisher) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Publisher) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Publisher) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Publisher) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

type BidRequest_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID uniquely identifying the content.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Content episode number (typically applies to video content).
	Episode *int32 `protobuf:"varint,2,opt,name=episode" json:"episode,omitempty"`
	// Video Examples:
	// “Search Committee” (television), “A New Hope” (movie), or “Endgame” (made for web).
	// Non-Video Example:
	// “Why an Antarctic Glacier Is Melting So Quickly” (Time magazine article).
	Title *string `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`
	// Video Examples: “The Office” (television), “Star Wars” (movie),
	// or “Arby ‘N’ The Chief” (made for web).
	// Non-Video Example: “Ecocentric” (Time Magazine blog).
	Series *string `protobuf:"bytes,4,opt,name=series" json:"series,omitempty"`
	// Content season (e.g., “Season 3”).
	Season *string `protobuf:"bytes,5,opt,name=season" json:"season,omitempty"`
	// Artist credited with the content.
	Artist *string `protobuf:"bytes,6,opt,name=artist" json:"artist,omitempty"`
	// Genre that best describes the content (e.g., rock, pop, etc).
	Genre *string `protobuf:"bytes,7,opt,name=genre" json:"genre,omitempty"`
	// Album to which the content belongs; typically for audio/video.
	Album *string `protobuf:"bytes,8,opt,name=album" json:"album,omitempty"`
	// URL of the content, for buy-side contextualization or review.
	Url *string `protobuf:"bytes,9,opt,name=url" json:"url,omitempty"`
	// Array of IAB content categories that describe the content producer.
	Cat []string `protobuf:"bytes,10,rep,name=cat" json:"cat,omitempty"`
	// Production quality.
	Prodq *BidRequest_Content_ProductionQuality `protobuf:"varint,11,opt,name=prodq,enum=go.micro.service.iqyi.BidRequest_Content_ProductionQuality" json:"prodq,omitempty"`
	// Type of content (game, video, text, etc.).
	Context *BidRequest_Content_ContentContext `protobuf:"varint,12,opt,name=context,enum=go.micro.service.iqyi.BidRequest_Content_ContentContext" json:"context,omitempty"`
	// Content rating (e.g., MPAA).
	Contentrating *string `protobuf:"bytes,13,opt,name=contentrating" json:"contentrating,omitempty"`
	// User rating of the content (e.g., number of stars, likes, etc.).
	Userrating *string `protobuf:"bytes,14,opt,name=userrating" json:"userrating,omitempty"`
	// Comma separated list of keywords describing the content.
	Keywords *string `protobuf:"bytes,15,opt,name=keywords" json:"keywords,omitempty"`
	// 0 = not live, 1 = content is live (e.g., stream, live blog).
	Livestream *bool `protobuf:"varint,16,opt,name=livestream" json:"livestream,omitempty"`
	// 0 = indirect, 1 = direct.
	Sourcerelationship *bool `protobuf:"varint,17,opt,name=sourcerelationship" json:"sourcerelationship,omitempty"`
	// Details about the content Producer
	Producer *BidRequest_Producer `protobuf:"bytes,18,opt,name=producer" json:"producer,omitempty"`
	// Length of content in seconds; appropriate for video or audio.
	Len *int32 `protobuf:"varint,19,opt,name=len" json:"len,omitempty"`
	// Media rating per IQG guidelines.
	Qagmediarating *BidRequest_Content_QAGMediaRating `protobuf:"varint,20,opt,name=qagmediarating,enum=go.micro.service.iqyi.BidRequest_Content_QAGMediaRating" json:"qagmediarating,omitempty"`
	// Indicator of whether or not the content is embeddable
	// (e.g., an embeddable video player), where 0 = no, 1 = yes.
	Embeddable *bool `protobuf:"varint,21,opt,name=embeddable" json:"embeddable,omitempty"`
	// Content language using ISO-639-1-alpha-2.
	Language *string `protobuf:"bytes,22,opt,name=language" json:"language,omitempty"`
}

func (x *BidRequest_Content) Reset() {
	*x = BidRequest_Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Content) ProtoMessage() {}

func (x *BidRequest_Content) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Content.ProtoReflect.Descriptor instead.
func (*BidRequest_Content) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 4}
}

func (x *BidRequest_Content) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Content) GetEpisode() int32 {
	if x != nil && x.Episode != nil {
		return *x.Episode
	}
	return 0
}

func (x *BidRequest_Content) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidRequest_Content) GetSeries() string {
	if x != nil && x.Series != nil {
		return *x.Series
	}
	return ""
}

func (x *BidRequest_Content) GetSeason() string {
	if x != nil && x.Season != nil {
		return *x.Season
	}
	return ""
}

func (x *BidRequest_Content) GetArtist() string {
	if x != nil && x.Artist != nil {
		return *x.Artist
	}
	return ""
}

func (x *BidRequest_Content) GetGenre() string {
	if x != nil && x.Genre != nil {
		return *x.Genre
	}
	return ""
}

func (x *BidRequest_Content) GetAlbum() string {
	if x != nil && x.Album != nil {
		return *x.Album
	}
	return ""
}

func (x *BidRequest_Content) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidRequest_Content) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Content) GetProdq() BidRequest_Content_ProductionQuality {
	if x != nil && x.Prodq != nil {
		return *x.Prodq
	}
	return BidRequest_Content_QUALITY_UNKNOWN
}

func (x *BidRequest_Content) GetContext() BidRequest_Content_ContentContext {
	if x != nil && x.Context != nil {
		return *x.Context
	}
	return BidRequest_Content_VIDEO
}

func (x *BidRequest_Content) GetContentrating() string {
	if x != nil && x.Contentrating != nil {
		return *x.Contentrating
	}
	return ""
}

func (x *BidRequest_Content) GetUserrating() string {
	if x != nil && x.Userrating != nil {
		return *x.Userrating
	}
	return ""
}

func (x *BidRequest_Content) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_Content) GetLivestream() bool {
	if x != nil && x.Livestream != nil {
		return *x.Livestream
	}
	return false
}

func (x *BidRequest_Content) GetSourcerelationship() bool {
	if x != nil && x.Sourcerelationship != nil {
		return *x.Sourcerelationship
	}
	return false
}

func (x *BidRequest_Content) GetProducer() *BidRequest_Producer {
	if x != nil {
		return x.Producer
	}
	return nil
}

func (x *BidRequest_Content) GetLen() int32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *BidRequest_Content) GetQagmediarating() BidRequest_Content_QAGMediaRating {
	if x != nil && x.Qagmediarating != nil {
		return *x.Qagmediarating
	}
	return BidRequest_Content_ALL_AUDIENCES
}

func (x *BidRequest_Content) GetEmbeddable() bool {
	if x != nil && x.Embeddable != nil {
		return *x.Embeddable
	}
	return false
}

func (x *BidRequest_Content) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}
	return ""
}

type BidRequest_Producer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Content producer or originator ID.
	// Useful if content is syndicated and may be posted on a site using embed tags.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Content producer or originator name (e.g., “Warner Bros”).
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// Array of IAB content categories that describe the content producer.
	Cat []string `protobuf:"bytes,3,rep,name=cat" json:"cat,omitempty"`
	// Highest level domain of the content producer (e.g., “producer.com”).
	Domain *string `protobuf:"bytes,4,opt,name=domain" json:"domain,omitempty"`
}

func (x *BidRequest_Producer) Reset() {
	*x = BidRequest_Producer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Producer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Producer) ProtoMessage() {}

func (x *BidRequest_Producer) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Producer.ProtoReflect.Descriptor instead.
func (*BidRequest_Producer) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 5}
}

func (x *BidRequest_Producer) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_Producer) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *BidRequest_Producer) GetCat() []string {
	if x != nil {
		return x.Cat
	}
	return nil
}

func (x *BidRequest_Producer) GetDomain() string {
	if x != nil && x.Domain != nil {
		return *x.Domain
	}
	return ""
}

// Device information.
// Either available device id or it's md5 value is fine to
// identify the device.
type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User agent identification, e.g: iPhone11,8
	Ua *string `protobuf:"bytes,1,req,name=ua" json:"ua,omitempty"`
	// IPv4 address closest to device.
	Ip *string `protobuf:"bytes,2,opt,name=ip" json:"ip,omitempty"`
	// Location information.
	Geo *BidRequest_Geo `protobuf:"bytes,3,req,name=geo" json:"geo,omitempty"`
	// Idfa in plaintext.
	Idfa *string `protobuf:"bytes,4,opt,name=idfa" json:"idfa,omitempty"`
	// 32 bytes lower-case md5 value of idfa.
	// For example:
	// idfa: EA0000CD-A667-48BC-B806-42E111148606
	// idfa_md5: 1a59721a46a48f707afbb57fa8c18db5
	IdfaMd5 *string `protobuf:"bytes,27,opt,name=idfa_md5,json=idfaMd5" json:"idfa_md5,omitempty"`
	// Imei in plaintext is forbidden.
	// 32 bytes lower-case md5 value of imei.
	// For example:
	// imei: 000000111122222
	// imei_md5: ecf3f377cac0a6f0c2b5b831021ea877
	ImeiMd5 *string `protobuf:"bytes,5,opt,name=imei_md5,json=imeiMd5" json:"imei_md5,omitempty"`
	// Android id in lower-case.
	Androidid *string `protobuf:"bytes,6,opt,name=androidid" json:"androidid,omitempty"`
	// 32 bytes lower-case md5 value of android id.
	AndroididMd5 *string `protobuf:"bytes,28,opt,name=androidid_md5,json=androididMd5" json:"androidid_md5,omitempty"`
	// Oaid in lower-case.
	Oaid *string `protobuf:"bytes,7,opt,name=oaid" json:"oaid,omitempty"`
	// 32 bytes lower-case md5 value of oaid.
	// For example:
	// oaid: f6e7f6f6-d5f7-d50f-9fff-f2cefdn7b88f
	// oaid_md5: f0232f69a83a18b2b41755412a5c6c28
	OaidMd5 *string `protobuf:"bytes,29,opt,name=oaid_md5,json=oaidMd5" json:"oaid_md5,omitempty"`
	// Mac address in upper-case.
	// For example:
	// mac: 00:00:00:AC:CD:75
	Mac *string `protobuf:"bytes,8,opt,name=mac" json:"mac,omitempty"`
	// 32 bytes lower-case md5 value of processed mac address which removed
	// ':' and '-' concatenations and transformed to upper case.
	// For example:
	// mac: 00:00:00:ac:cd:75
	// processed: 000000ACCD75
	// processed_mac_md5: 34b18ff8a1ba82be8fcfcb1a4bda0693
	ProcessedMacMd5 *string `protobuf:"bytes,30,opt,name=processed_mac_md5,json=processedMacMd5" json:"processed_mac_md5,omitempty"`
	// IPv6 address closest to device.
	Ipv6 *string `protobuf:"bytes,17,opt,name=ipv6" json:"ipv6,omitempty"`
	// Carrier or ISP, e.g.
	// "1: China Mobile  2: China Unicom 3: China Telecom  0:unknown".
	Carrier *int32 `protobuf:"varint,18,opt,name=carrier" json:"carrier,omitempty"`
	// Device make (e.g., "Apple").
	Make *string `protobuf:"bytes,19,opt,name=make" json:"make,omitempty"`
	// Device model (e.g., "iPhone5s").
	Model *string `protobuf:"bytes,20,opt,name=model" json:"model,omitempty"`
	// Device operating system (e.g., "iOS" or "Android").
	Os *string `protobuf:"bytes,21,opt,name=os" json:"os,omitempty"`
	// Physical width of the screen in pixels.
	W *int32 `protobuf:"varint,23,opt,name=w" json:"w,omitempty"`
	// Physical height of the screen in pixels.
	H *int32 `protobuf:"varint,24,opt,name=h" json:"h,omitempty"`
	// Network connection type.
	Connectiontype *BidRequest_Device_ConnectionType `protobuf:"varint,25,opt,name=connectiontype,enum=go.micro.service.iqyi.BidRequest_Device_ConnectionType" json:"connectiontype,omitempty"`
	// The general type of device.
	Devicetype *BidRequest_Device_DeviceType `protobuf:"varint,26,opt,name=devicetype,enum=go.micro.service.iqyi.BidRequest_Device_DeviceType" json:"devicetype,omitempty"`
	// It's used only if devicetype is TV.
	// Refer to TvType for enum values.
	TvType *int32 `protobuf:"varint,31,opt,name=tv_type,json=tvType" json:"tv_type,omitempty"`
	// CaidVersions.
	CaidInfo *BidRequest_Device_CaidInfo `protobuf:"bytes,32,opt,name=caid_info,json=caidInfo" json:"caid_info,omitempty"`
	// Country code, e.g: "GB"
	CountryCode *string `protobuf:"bytes,33,opt,name=country_code,json=countryCode" json:"country_code,omitempty"`
	// Time zone offset seconds, e.g: "28800"
	TimeZoneSec *string `protobuf:"bytes,34,opt,name=time_zone_sec,json=timeZoneSec" json:"time_zone_sec,omitempty"`
	// Device name md5 value, e.g: "867e57bd062c7169995dc03cc0541c19"
	DeviceNameMd5 *string `protobuf:"bytes,35,opt,name=device_name_md5,json=deviceNameMd5" json:"device_name_md5,omitempty"`
	// The language used by the device, e.g: "zh-Hans-CN"
	DeviceLanguage *string `protobuf:"bytes,36,opt,name=device_language,json=deviceLanguage" json:"device_language,omitempty"`
	// Machine of device, e.g: "D22AP"
	MachineOfDevice *string `protobuf:"bytes,37,opt,name=machine_of_device,json=machineOfDevice" json:"machine_of_device,omitempty"`
	// Device boot mark, e.g: "1649650429"
	BootMark *string `protobuf:"bytes,38,opt,name=boot_mark,json=bootMark" json:"boot_mark,omitempty"`
	// Device update mark, e.g: "1624852793.084198"
	UpdateMark *string `protobuf:"bytes,39,opt,name=update_mark,json=updateMark" json:"update_mark,omitempty"`
	// Device operating system version, e.g: "14.0"
	Osv *string `protobuf:"bytes,22,opt,name=osv" json:"osv,omitempty"`
	// Carrier name, e.g: "中国移动"
	CarrierName *string `protobuf:"bytes,40,opt,name=carrier_name,json=carrierName" json:"carrier_name,omitempty"`
	// The total disk space in byte, e.g: 63944380416
	DiskTotal *int64 `protobuf:"varint,41,opt,name=disk_total,json=diskTotal" json:"disk_total,omitempty"`
	// The total memory space in byte, e.g: 2983313408
	MemTotal *int64 `protobuf:"varint,42,opt,name=mem_total,json=memTotal" json:"mem_total,omitempty"`
	// Only for ios, disk mount id, like "58F3A74E0607EAB123456789FA9598D258EDEE9C2C7A12F365C499EB723BA7B0D014E7A1CE1721C71C28619B65E97E24@/dev/disk1s1".
	MntId *string `protobuf:"bytes,43,opt,name=mnt_id,json=mntId" json:"mnt_id,omitempty"`
	// Only for ios, device init time, like "1630181241.433910459".
	// Note that this field needs to retain 9 decimal places, and fill in '0' if it's not enough.
	// Wrong: "1630181241.4339104" x
	// Correct: "1630181241.433910400" v
	FileInitTime *string `protobuf:"bytes,44,opt,name=file_init_time,json=fileInitTime" json:"file_init_time,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6}
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil && x.Ua != nil {
		return *x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil && x.Ip != nil {
		return *x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil && x.Idfa != nil {
		return *x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil && x.IdfaMd5 != nil {
		return *x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil && x.ImeiMd5 != nil {
		return *x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidid() string {
	if x != nil && x.Androidid != nil {
		return *x.Androidid
	}
	return ""
}

func (x *BidRequest_Device) GetAndroididMd5() string {
	if x != nil && x.AndroididMd5 != nil {
		return *x.AndroididMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil && x.Oaid != nil {
		return *x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil && x.OaidMd5 != nil {
		return *x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil && x.Mac != nil {
		return *x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetProcessedMacMd5() string {
	if x != nil && x.ProcessedMacMd5 != nil {
		return *x.ProcessedMacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetIpv6() string {
	if x != nil && x.Ipv6 != nil {
		return *x.Ipv6
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil && x.Carrier != nil {
		return *x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Device) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Device) GetConnectiontype() BidRequest_Device_ConnectionType {
	if x != nil && x.Connectiontype != nil {
		return *x.Connectiontype
	}
	return BidRequest_Device_CONNECTION_UNKNOWN
}

func (x *BidRequest_Device) GetDevicetype() BidRequest_Device_DeviceType {
	if x != nil && x.Devicetype != nil {
		return *x.Devicetype
	}
	return BidRequest_Device_UNKNOWN_DEVICE
}

func (x *BidRequest_Device) GetTvType() int32 {
	if x != nil && x.TvType != nil {
		return *x.TvType
	}
	return 0
}

func (x *BidRequest_Device) GetCaidInfo() *BidRequest_Device_CaidInfo {
	if x != nil {
		return x.CaidInfo
	}
	return nil
}

func (x *BidRequest_Device) GetCountryCode() string {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return ""
}

func (x *BidRequest_Device) GetTimeZoneSec() string {
	if x != nil && x.TimeZoneSec != nil {
		return *x.TimeZoneSec
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceNameMd5() string {
	if x != nil && x.DeviceNameMd5 != nil {
		return *x.DeviceNameMd5
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceLanguage() string {
	if x != nil && x.DeviceLanguage != nil {
		return *x.DeviceLanguage
	}
	return ""
}

func (x *BidRequest_Device) GetMachineOfDevice() string {
	if x != nil && x.MachineOfDevice != nil {
		return *x.MachineOfDevice
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil && x.BootMark != nil {
		return *x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil && x.UpdateMark != nil {
		return *x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil && x.Osv != nil {
		return *x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrierName() string {
	if x != nil && x.CarrierName != nil {
		return *x.CarrierName
	}
	return ""
}

func (x *BidRequest_Device) GetDiskTotal() int64 {
	if x != nil && x.DiskTotal != nil {
		return *x.DiskTotal
	}
	return 0
}

func (x *BidRequest_Device) GetMemTotal() int64 {
	if x != nil && x.MemTotal != nil {
		return *x.MemTotal
	}
	return 0
}

func (x *BidRequest_Device) GetMntId() string {
	if x != nil && x.MntId != nil {
		return *x.MntId
	}
	return ""
}

func (x *BidRequest_Device) GetFileInitTime() string {
	if x != nil && x.FileInitTime != nil {
		return *x.FileInitTime
	}
	return ""
}

// Location information.
type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Latitude from -90.0 to +90.0, where negative is south.
	Lat *float64 `protobuf:"fixed64,1,opt,name=lat" json:"lat,omitempty"`
	// Longitude from -180.0 to +180.0, where negative is west.
	Lon *float64 `protobuf:"fixed64,2,opt,name=lon" json:"lon,omitempty"`
	// Country name, e.g. "中国".
	Country *string `protobuf:"bytes,3,opt,name=country" json:"country,omitempty"`
	// Province, e.g. "广州".
	Prov *string `protobuf:"bytes,6,opt,name=prov" json:"prov,omitempty"`
	// City, e.g. "深圳".
	City     *string `protobuf:"bytes,7,opt,name=city" json:"city,omitempty"`
	District *string `protobuf:"bytes,10,opt,name=district" json:"district,omitempty"`
	// Source of location data.
	Type *BidRequest_Geo_LocationType `protobuf:"varint,9,opt,name=type,enum=go.micro.service.iqyi.BidRequest_Geo_LocationType" json:"type,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 7}
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil && x.Lon != nil {
		return *x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *BidRequest_Geo) GetProv() string {
	if x != nil && x.Prov != nil {
		return *x.Prov
	}
	return ""
}

func (x *BidRequest_Geo) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *BidRequest_Geo) GetDistrict() string {
	if x != nil && x.District != nil {
		return *x.District
	}
	return ""
}

func (x *BidRequest_Geo) GetType() BidRequest_Geo_LocationType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Geo_GPS_LOCATION
}

// User information.
type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The identification of user in media side.
	Id *string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// This indicate the age group id as following:
	// 0: [0, 0]
	// 1: [1, 18]
	// 2: [19, 24]
	// 3: [25, 30]
	// 4: [31, 35]
	// 5: [36, 40]
	// 6: [41, INT_MAX]
	Age *int32 `protobuf:"varint,6,opt,name=age" json:"age,omitempty"`
	// Gender as "M" male, "F" female.
	Gender *string `protobuf:"bytes,2,opt,name=gender" json:"gender,omitempty"`
	// Comma separated list of keywords, interests, or intent.
	Keywords *string `protobuf:"bytes,3,opt,name=keywords" json:"keywords,omitempty"`
	// Comma separated List of apps installed by the user
	Applist *string `protobuf:"bytes,4,opt,name=applist" json:"applist,omitempty"`
	Data    *string `protobuf:"bytes,5,opt,name=data" json:"data,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 8}
}

func (x *BidRequest_User) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *BidRequest_User) GetAge() int32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

func (x *BidRequest_User) GetGender() string {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return ""
}

func (x *BidRequest_User) GetKeywords() string {
	if x != nil && x.Keywords != nil {
		return *x.Keywords
	}
	return ""
}

func (x *BidRequest_User) GetApplist() string {
	if x != nil && x.Applist != nil {
		return *x.Applist
	}
	return ""
}

func (x *BidRequest_User) GetData() string {
	if x != nil && x.Data != nil {
		return *x.Data
	}
	return ""
}

type BidRequest_DeduplicatedId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 0: unknown type
	// 1: user session id.
	// 2: tv id.
	Type *int32  `protobuf:"varint,1,opt,name=type" json:"type,omitempty"`
	Id   *string `protobuf:"bytes,2,opt,name=id" json:"id,omitempty"`
}

func (x *BidRequest_DeduplicatedId) Reset() {
	*x = BidRequest_DeduplicatedId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_DeduplicatedId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_DeduplicatedId) ProtoMessage() {}

func (x *BidRequest_DeduplicatedId) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_DeduplicatedId.ProtoReflect.Descriptor instead.
func (*BidRequest_DeduplicatedId) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 9}
}

func (x *BidRequest_DeduplicatedId) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *BidRequest_DeduplicatedId) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

// Video impression.
type BidRequest_Imp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Width of the player in pixels.
	W *int32 `protobuf:"varint,1,req,name=w" json:"w,omitempty"`
	// Height of the player in pixels.
	H *int32 `protobuf:"varint,2,req,name=h" json:"h,omitempty"`
	// Minimum video ad duration in seconds.
	Minduration *int32 `protobuf:"varint,3,req,name=minduration" json:"minduration,omitempty"`
	// Maximum video ad duration in seconds.
	Maxduration *int32 `protobuf:"varint,4,req,name=maxduration" json:"maxduration,omitempty"`
	// Indicates the start delay in seconds for pre-roll,
	// mid-roll, or post-roll ad placements.
	//  0: PRE_ROLL
	//  1: GENERIC_MID_ROLL
	//  -1: GENERIC_POST_ROLL
	Startdelay *int32 `protobuf:"varint,5,req,name=startdelay" json:"startdelay,omitempty"`
	// Indicates if the impression must be linear, nonlinear, etc.
	// If none specified, assume all are allowed.
	Linearity *BidRequest_Imp_Video_VideoLinearity `protobuf:"varint,6,req,name=linearity,enum=go.micro.service.iqyi.BidRequest_Imp_Video_VideoLinearity" json:"linearity,omitempty"`
	// Accepted creative types of this impression.
	// 1: the creative should be a image.
	// 2: the creative should be a video.
	// 3: the creative is an image or a video.
	AcceptedCreativeTypes *int32                      `protobuf:"varint,9,opt,name=accepted_creative_types,json=acceptedCreativeTypes" json:"accepted_creative_types,omitempty"`
	Maxadscount           *int32                      `protobuf:"varint,7,opt,name=maxadscount" json:"maxadscount,omitempty"`
	Format                *BidRequest_Imp_VideoFormat `protobuf:"varint,8,opt,name=format,enum=go.micro.service.iqyi.BidRequest_Imp_VideoFormat" json:"format,omitempty"`
}

func (x *BidRequest_Imp_Video) Reset() {
	*x = BidRequest_Imp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Video) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *BidRequest_Imp_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMinduration() int32 {
	if x != nil && x.Minduration != nil {
		return *x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxduration() int32 {
	if x != nil && x.Maxduration != nil {
		return *x.Maxduration
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetStartdelay() int32 {
	if x != nil && x.Startdelay != nil {
		return *x.Startdelay
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetLinearity() BidRequest_Imp_Video_VideoLinearity {
	if x != nil && x.Linearity != nil {
		return *x.Linearity
	}
	return BidRequest_Imp_Video_LINEAR
}

func (x *BidRequest_Imp_Video) GetAcceptedCreativeTypes() int32 {
	if x != nil && x.AcceptedCreativeTypes != nil {
		return *x.AcceptedCreativeTypes
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetMaxadscount() int32 {
	if x != nil && x.Maxadscount != nil {
		return *x.Maxadscount
	}
	return 0
}

func (x *BidRequest_Imp_Video) GetFormat() BidRequest_Imp_VideoFormat {
	if x != nil && x.Format != nil {
		return *x.Format
	}
	return BidRequest_Imp_VIDEO_ANY
}

// Native impression.
type BidRequest_Imp_Native struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Maximum length of the text in the title element.
	TitleLen *int32 `protobuf:"varint,1,opt,name=title_len,json=titleLen" json:"title_len,omitempty"`
	// Image assets.
	Imgs  []*BidRequest_Imp_Native_Image `protobuf:"bytes,2,rep,name=imgs" json:"imgs,omitempty"`
	Video *BidRequest_Imp_Native_Video   `protobuf:"bytes,3,opt,name=video" json:"video,omitempty"`
	// Maximum ads can be returned.
	Maxadscount *int32 `protobuf:"varint,4,opt,name=maxadscount" json:"maxadscount,omitempty"`
}

func (x *BidRequest_Imp_Native) Reset() {
	*x = BidRequest_Imp_Native{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native) ProtoMessage() {}

func (x *BidRequest_Imp_Native) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 1}
}

func (x *BidRequest_Imp_Native) GetTitleLen() int32 {
	if x != nil && x.TitleLen != nil {
		return *x.TitleLen
	}
	return 0
}

func (x *BidRequest_Imp_Native) GetImgs() []*BidRequest_Imp_Native_Image {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetVideo() *BidRequest_Imp_Native_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidRequest_Imp_Native) GetMaxadscount() int32 {
	if x != nil && x.Maxadscount != nil {
		return *x.Maxadscount
	}
	return 0
}

// The Image object to be used for all image elements
// of the Native ad such as Icons, Main Image, etc.
type BidRequest_Imp_Native_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type ID of the image element supported by the publisher.
	Type *BidRequest_Imp_Native_Image_ImageAssetType `protobuf:"varint,1,opt,name=type,enum=go.micro.service.iqyi.BidRequest_Imp_Native_Image_ImageAssetType" json:"type,omitempty"`
	// Width of the image in pixels.
	W *int32 `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	// Height of the image in pixels.
	H *int32 `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
	// The minimum requested width of the image in pixels.
	// Either w or wmin should be transmitted.
	// If only w is included, it should be considered
	// an exact requirement.
	Wmin *int32 `protobuf:"varint,4,opt,name=wmin" json:"wmin,omitempty"`
	// The minimum requested height of the image in pixels.
	// Either h or hmin should be transmitted.
	// If only h is included, it should be considered
	// an exact requirement.
	Hmin *int32 `protobuf:"varint,5,opt,name=hmin" json:"hmin,omitempty"`
}

func (x *BidRequest_Imp_Native_Image) Reset() {
	*x = BidRequest_Imp_Native_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Image) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Image) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Image.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Image) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 1, 0}
}

func (x *BidRequest_Imp_Native_Image) GetType() BidRequest_Imp_Native_Image_ImageAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidRequest_Imp_Native_Image_ICON
}

func (x *BidRequest_Imp_Native_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Native_Image) GetWmin() int32 {
	if x != nil && x.Wmin != nil {
		return *x.Wmin
	}
	return 0
}

func (x *BidRequest_Imp_Native_Image) GetHmin() int32 {
	if x != nil && x.Hmin != nil {
		return *x.Hmin
	}
	return 0
}

type BidRequest_Imp_Native_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Width of the player in pixels.
	W *int32 `protobuf:"varint,1,req,name=w" json:"w,omitempty"`
	// Height of the player in pixels.
	H *int32 `protobuf:"varint,2,req,name=h" json:"h,omitempty"`
	// Minimum video ad duration in seconds.
	Minduration *int32 `protobuf:"varint,3,req,name=minduration" json:"minduration,omitempty"`
	// Maximum video ad duration in seconds.
	Maxduration *int32                      `protobuf:"varint,4,req,name=maxduration" json:"maxduration,omitempty"`
	Format      *BidRequest_Imp_VideoFormat `protobuf:"varint,5,opt,name=format,enum=go.micro.service.iqyi.BidRequest_Imp_VideoFormat" json:"format,omitempty"`
}

func (x *BidRequest_Imp_Native_Video) Reset() {
	*x = BidRequest_Imp_Native_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp_Native_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp_Native_Video) ProtoMessage() {}

func (x *BidRequest_Imp_Native_Video) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp_Native_Video.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp_Native_Video) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 0, 1, 1}
}

func (x *BidRequest_Imp_Native_Video) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidRequest_Imp_Native_Video) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidRequest_Imp_Native_Video) GetMinduration() int32 {
	if x != nil && x.Minduration != nil {
		return *x.Minduration
	}
	return 0
}

func (x *BidRequest_Imp_Native_Video) GetMaxduration() int32 {
	if x != nil && x.Maxduration != nil {
		return *x.Maxduration
	}
	return 0
}

func (x *BidRequest_Imp_Native_Video) GetFormat() BidRequest_Imp_VideoFormat {
	if x != nil && x.Format != nil {
		return *x.Format
	}
	return BidRequest_Imp_VIDEO_ANY
}

type BidRequest_Device_Caid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version *string `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	Caid    *string `protobuf:"bytes,2,opt,name=caid" json:"caid,omitempty"`
}

func (x *BidRequest_Device_Caid) Reset() {
	*x = BidRequest_Device_Caid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_Caid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_Caid) ProtoMessage() {}

func (x *BidRequest_Device_Caid) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_Caid.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_Caid) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6, 0}
}

func (x *BidRequest_Device_Caid) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *BidRequest_Device_Caid) GetCaid() string {
	if x != nil && x.Caid != nil {
		return *x.Caid
	}
	return ""
}

type BidRequest_Device_CaidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Caid []*BidRequest_Device_Caid `protobuf:"bytes,1,rep,name=caid" json:"caid,omitempty"`
}

func (x *BidRequest_Device_CaidInfo) Reset() {
	*x = BidRequest_Device_CaidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device_CaidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device_CaidInfo) ProtoMessage() {}

func (x *BidRequest_Device_CaidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device_CaidInfo.ProtoReflect.Descriptor instead.
func (*BidRequest_Device_CaidInfo) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{1, 6, 1}
}

func (x *BidRequest_Device_CaidInfo) GetCaid() []*BidRequest_Device_Caid {
	if x != nil {
		return x.Caid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Adzone id of the related impression.
	AdzoneId *string `protobuf:"bytes,1,req,name=adzone_id,json=adzoneId" json:"adzone_id,omitempty"`
	// Video and response.
	Admvideo *BidResponse_Bid_AdmVideo `protobuf:"bytes,2,opt,name=admvideo" json:"admvideo,omitempty"`
	// Native ad response.
	Admnative *BidResponse_Bid_AdmNative `protobuf:"bytes,3,opt,name=admnative" json:"admnative,omitempty"`
	// Creative ID of this bid.
	Crid *string `protobuf:"bytes,4,req,name=crid" json:"crid,omitempty"`
	// Price of this bid.
	Price *int32 `protobuf:"varint,5,opt,name=price" json:"price,omitempty"`
	// Win notice tracking url.
	WinNoticeUrl []string `protobuf:"bytes,6,rep,name=win_notice_url,json=winNoticeUrl" json:"win_notice_url,omitempty"`
	// These mini app fields are only set when action is OPEN_MINI_APP.
	// Mini app name like: gh_db349995c9b7.
	MiniAppName *string `protobuf:"bytes,7,opt,name=mini_app_name,json=miniAppName" json:"mini_app_name,omitempty"`
	// Mini app path.
	MiniAppPath *string `protobuf:"bytes,8,opt,name=mini_app_path,json=miniAppPath" json:"mini_app_path,omitempty"`
	// Ad click action type
	Action *BidResponse_Bid_AdActionType `protobuf:"varint,10,opt,name=action,enum=go.micro.service.iqyi.BidResponse_Bid_AdActionType" json:"action,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Ad url.
	AdUrl *string `protobuf:"bytes,11,opt,name=ad_url,json=adUrl" json:"ad_url,omitempty"`
	// Detail page url.
	// This filed was used for app description historically,
	// please use "app_desc_page_url" instead if it's "DOWNLOAD_APP" type.
	DetailPageUrl *string `protobuf:"bytes,12,opt,name=detail_page_url,json=detailPageUrl" json:"detail_page_url,omitempty"`
	// Note: it's different from the "curl" in link object, it's the actual
	// landing page and "curl" is download url when it's "DOWNLOAD_APP" type.
	AppDescPageUrl *string `protobuf:"bytes,29,opt,name=app_desc_page_url,json=appDescPageUrl" json:"app_desc_page_url,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Ad title.
	Title *string `protobuf:"bytes,14,opt,name=title" json:"title,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Ad description.
	Description *string `protobuf:"bytes,15,opt,name=description" json:"description,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Package name.
	ApkName       *string `protobuf:"bytes,16,opt,name=apk_name,json=apkName" json:"apk_name,omitempty"`
	AppName       *string `protobuf:"bytes,17,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	AppVersion    *string `protobuf:"bytes,25,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	AppDeveloper  *string `protobuf:"bytes,26,opt,name=app_developer,json=appDeveloper" json:"app_developer,omitempty"`
	AppPermission *string `protobuf:"bytes,27,opt,name=app_permission,json=appPermission" json:"app_permission,omitempty"`
	AppPrivacy    *string `protobuf:"bytes,28,opt,name=app_privacy,json=appPrivacy" json:"app_privacy,omitempty"`
	AppFeature    *string `protobuf:"bytes,32,opt,name=app_feature,json=appFeature" json:"app_feature,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Creative type.
	CreativeType *BidResponse_Bid_CreativeType `protobuf:"varint,18,opt,name=creative_type,json=creativeType,enum=go.micro.service.iqyi.BidResponse_Bid_CreativeType" json:"creative_type,omitempty"`
	// Creative direction, refer to enum CreativeDirection for value definitions.
	CreativeDirection *int32 `protobuf:"varint,24,opt,name=creative_direction,json=creativeDirection" json:"creative_direction,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Landings and Trackings.
	Link *BidResponse_Bid_Link `protobuf:"bytes,19,opt,name=link" json:"link,omitempty"`
	// Only for OPENING/PAUSE ad type.
	// Video object.
	AdVideo *BidResponse_Bid_AdVideo `protobuf:"bytes,20,opt,name=ad_video,json=adVideo" json:"ad_video,omitempty"`
	// Opening ad object.
	Opening *BidResponse_Bid_Opening `protobuf:"bytes,23,opt,name=opening" json:"opening,omitempty"`
	// Tv ad object.
	TvAd *BidResponse_Bid_TvAd `protobuf:"bytes,30,opt,name=tv_ad,json=tvAd" json:"tv_ad,omitempty"`
	// Key-value entries with unique keys.
	// Supported entries:
	// key: "tv_id", value(example): "123456",
	// key: "is_auto_deeplink", value: "true".
	ExtendedEntries []*Entry `protobuf:"bytes,31,rep,name=extended_entries,json=extendedEntries" json:"extended_entries,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0}
}

func (x *BidResponse_Bid) GetAdzoneId() string {
	if x != nil && x.AdzoneId != nil {
		return *x.AdzoneId
	}
	return ""
}

func (x *BidResponse_Bid) GetAdmvideo() *BidResponse_Bid_AdmVideo {
	if x != nil {
		return x.Admvideo
	}
	return nil
}

func (x *BidResponse_Bid) GetAdmnative() *BidResponse_Bid_AdmNative {
	if x != nil {
		return x.Admnative
	}
	return nil
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil && x.Crid != nil {
		return *x.Crid
	}
	return ""
}

func (x *BidResponse_Bid) GetPrice() int32 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetWinNoticeUrl() []string {
	if x != nil {
		return x.WinNoticeUrl
	}
	return nil
}

func (x *BidResponse_Bid) GetMiniAppName() string {
	if x != nil && x.MiniAppName != nil {
		return *x.MiniAppName
	}
	return ""
}

func (x *BidResponse_Bid) GetMiniAppPath() string {
	if x != nil && x.MiniAppPath != nil {
		return *x.MiniAppPath
	}
	return ""
}

func (x *BidResponse_Bid) GetAction() BidResponse_Bid_AdActionType {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return BidResponse_Bid_OPEN_IN_WEBVIEW
}

func (x *BidResponse_Bid) GetAdUrl() string {
	if x != nil && x.AdUrl != nil {
		return *x.AdUrl
	}
	return ""
}

func (x *BidResponse_Bid) GetDetailPageUrl() string {
	if x != nil && x.DetailPageUrl != nil {
		return *x.DetailPageUrl
	}
	return ""
}

func (x *BidResponse_Bid) GetAppDescPageUrl() string {
	if x != nil && x.AppDescPageUrl != nil {
		return *x.AppDescPageUrl
	}
	return ""
}

func (x *BidResponse_Bid) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Bid) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *BidResponse_Bid) GetApkName() string {
	if x != nil && x.ApkName != nil {
		return *x.ApkName
	}
	return ""
}

func (x *BidResponse_Bid) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Bid) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *BidResponse_Bid) GetAppDeveloper() string {
	if x != nil && x.AppDeveloper != nil {
		return *x.AppDeveloper
	}
	return ""
}

func (x *BidResponse_Bid) GetAppPermission() string {
	if x != nil && x.AppPermission != nil {
		return *x.AppPermission
	}
	return ""
}

func (x *BidResponse_Bid) GetAppPrivacy() string {
	if x != nil && x.AppPrivacy != nil {
		return *x.AppPrivacy
	}
	return ""
}

func (x *BidResponse_Bid) GetAppFeature() string {
	if x != nil && x.AppFeature != nil {
		return *x.AppFeature
	}
	return ""
}

func (x *BidResponse_Bid) GetCreativeType() BidResponse_Bid_CreativeType {
	if x != nil && x.CreativeType != nil {
		return *x.CreativeType
	}
	return BidResponse_Bid_IMG
}

func (x *BidResponse_Bid) GetCreativeDirection() int32 {
	if x != nil && x.CreativeDirection != nil {
		return *x.CreativeDirection
	}
	return 0
}

func (x *BidResponse_Bid) GetLink() *BidResponse_Bid_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

func (x *BidResponse_Bid) GetAdVideo() *BidResponse_Bid_AdVideo {
	if x != nil {
		return x.AdVideo
	}
	return nil
}

func (x *BidResponse_Bid) GetOpening() *BidResponse_Bid_Opening {
	if x != nil {
		return x.Opening
	}
	return nil
}

func (x *BidResponse_Bid) GetTvAd() *BidResponse_Bid_TvAd {
	if x != nil {
		return x.TvAd
	}
	return nil
}

func (x *BidResponse_Bid) GetExtendedEntries() []*Entry {
	if x != nil {
		return x.ExtendedEntries
	}
	return nil
}

type BidResponse_Bid_AdVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration *int32 `protobuf:"varint,1,opt,name=duration" json:"duration,omitempty"`
}

func (x *BidResponse_Bid_AdVideo) Reset() {
	*x = BidResponse_Bid_AdVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_AdVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_AdVideo) ProtoMessage() {}

func (x *BidResponse_Bid_AdVideo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_AdVideo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_AdVideo) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *BidResponse_Bid_AdVideo) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

type BidResponse_Bid_Opening struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type *BidResponse_Bid_Opening_Type `protobuf:"varint,1,opt,name=type,enum=go.micro.service.iqyi.BidResponse_Bid_Opening_Type" json:"type,omitempty"`
}

func (x *BidResponse_Bid_Opening) Reset() {
	*x = BidResponse_Bid_Opening{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Opening) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Opening) ProtoMessage() {}

func (x *BidResponse_Bid_Opening) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Opening.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Opening) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 1}
}

func (x *BidResponse_Bid_Opening) GetType() BidResponse_Bid_Opening_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidResponse_Bid_Opening_NON_IMMERSIVE
}

type BidResponse_Bid_TvAd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Html5 *BidResponse_Bid_TvAd_Html5 `protobuf:"bytes,1,opt,name=html5" json:"html5,omitempty"`
}

func (x *BidResponse_Bid_TvAd) Reset() {
	*x = BidResponse_Bid_TvAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_TvAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_TvAd) ProtoMessage() {}

func (x *BidResponse_Bid_TvAd) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_TvAd.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_TvAd) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 2}
}

func (x *BidResponse_Bid_TvAd) GetHtml5() *BidResponse_Bid_TvAd_Html5 {
	if x != nil {
		return x.Html5
	}
	return nil
}

type BidResponse_Bid_AdmVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Image objects for image creative.
	Imgs []*BidResponse_Bid_Image `protobuf:"bytes,1,rep,name=imgs" json:"imgs,omitempty"`
	// Video object for video creative.
	Video *BidResponse_Bid_Video `protobuf:"bytes,2,opt,name=video" json:"video,omitempty"`
	// Ad title.
	Title *string `protobuf:"bytes,3,opt,name=title" json:"title,omitempty"`
	// Description.
	Desc *string `protobuf:"bytes,4,opt,name=desc" json:"desc,omitempty"`
	// Destination Link.
	Link *BidResponse_Bid_Link `protobuf:"bytes,7,opt,name=link" json:"link,omitempty"`
	// Name of download package. Should be present in case of download ad.
	PackageName *string `protobuf:"bytes,8,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// Name of download app.
	AppName *string `protobuf:"bytes,9,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	// Icon url of app. Should be present in case of download ad.
	AppIcon *string `protobuf:"bytes,10,opt,name=app_icon,json=appIcon" json:"app_icon,omitempty"`
	// Version of download app. Should be present in case of download ad.
	AppVersion *string `protobuf:"bytes,11,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
	// If is_marked is 0, it is use to describe ad source.
	AdSourceMark *string `protobuf:"bytes,12,opt,name=ad_source_mark,json=adSourceMark" json:"ad_source_mark,omitempty"`
	// The number of seconds since ad start, at this time
	// "trueview_trackers" should be sent.
	TrueviewTimePoint *int32 `protobuf:"varint,13,opt,name=trueview_time_point,json=trueviewTimePoint" json:"trueview_time_point,omitempty"`
	// Tracking urls of trueview event.
	TrueviewTrackers []string `protobuf:"bytes,14,rep,name=trueview_trackers,json=trueviewTrackers" json:"trueview_trackers,omitempty"`
	// The number of seconds since ad start, at this time user can
	// skip this ad.
	SkippableTimePoint *int32 `protobuf:"varint,15,opt,name=skippable_time_point,json=skippableTimePoint" json:"skippable_time_point,omitempty"`
	// Tracking urls of skipping this ad.
	TrueviewSkipTrackers []string `protobuf:"bytes,16,rep,name=trueview_skip_trackers,json=trueviewSkipTrackers" json:"trueview_skip_trackers,omitempty"`
}

func (x *BidResponse_Bid_AdmVideo) Reset() {
	*x = BidResponse_Bid_AdmVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_AdmVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_AdmVideo) ProtoMessage() {}

func (x *BidResponse_Bid_AdmVideo) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_AdmVideo.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_AdmVideo) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 3}
}

func (x *BidResponse_Bid_AdmVideo) GetImgs() []*BidResponse_Bid_Image {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidResponse_Bid_AdmVideo) GetVideo() *BidResponse_Bid_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Bid_AdmVideo) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetLink() *BidResponse_Bid_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

func (x *BidResponse_Bid_AdmVideo) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetAppIcon() string {
	if x != nil && x.AppIcon != nil {
		return *x.AppIcon
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetAdSourceMark() string {
	if x != nil && x.AdSourceMark != nil {
		return *x.AdSourceMark
	}
	return ""
}

func (x *BidResponse_Bid_AdmVideo) GetTrueviewTimePoint() int32 {
	if x != nil && x.TrueviewTimePoint != nil {
		return *x.TrueviewTimePoint
	}
	return 0
}

func (x *BidResponse_Bid_AdmVideo) GetTrueviewTrackers() []string {
	if x != nil {
		return x.TrueviewTrackers
	}
	return nil
}

func (x *BidResponse_Bid_AdmVideo) GetSkippableTimePoint() int32 {
	if x != nil && x.SkippableTimePoint != nil {
		return *x.SkippableTimePoint
	}
	return 0
}

func (x *BidResponse_Bid_AdmVideo) GetTrueviewSkipTrackers() []string {
	if x != nil {
		return x.TrueviewSkipTrackers
	}
	return nil
}

type BidResponse_Bid_AdmNative struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Titile description.
	Title *string `protobuf:"bytes,1,opt,name=title" json:"title,omitempty"`
	// Image objects for image creative.
	Imgs []*BidResponse_Bid_Image `protobuf:"bytes,2,rep,name=imgs" json:"imgs,omitempty"`
	// Video object for video creative.
	Video *BidResponse_Bid_Video `protobuf:"bytes,3,opt,name=video" json:"video,omitempty"`
	// Link object for call to actions.
	Link *BidResponse_Bid_Link `protobuf:"bytes,4,opt,name=link" json:"link,omitempty"`
	// Name of download package. Should be present in case of download ad.
	PackageName *string `protobuf:"bytes,5,opt,name=package_name,json=packageName" json:"package_name,omitempty"`
	// Name of download app.
	AppName *string `protobuf:"bytes,6,opt,name=app_name,json=appName" json:"app_name,omitempty"`
	// Icon url of app. Should be present in case of download ad.
	AppIcon *string `protobuf:"bytes,7,opt,name=app_icon,json=appIcon" json:"app_icon,omitempty"`
	// Version of download app. Should be present in case of download ad.
	AppVersion *string `protobuf:"bytes,8,opt,name=app_version,json=appVersion" json:"app_version,omitempty"`
}

func (x *BidResponse_Bid_AdmNative) Reset() {
	*x = BidResponse_Bid_AdmNative{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_AdmNative) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_AdmNative) ProtoMessage() {}

func (x *BidResponse_Bid_AdmNative) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_AdmNative.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_AdmNative) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 4}
}

func (x *BidResponse_Bid_AdmNative) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *BidResponse_Bid_AdmNative) GetImgs() []*BidResponse_Bid_Image {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidResponse_Bid_AdmNative) GetVideo() *BidResponse_Bid_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Bid_AdmNative) GetLink() *BidResponse_Bid_Link {
	if x != nil {
		return x.Link
	}
	return nil
}

func (x *BidResponse_Bid_AdmNative) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *BidResponse_Bid_AdmNative) GetAppName() string {
	if x != nil && x.AppName != nil {
		return *x.AppName
	}
	return ""
}

func (x *BidResponse_Bid_AdmNative) GetAppIcon() string {
	if x != nil && x.AppIcon != nil {
		return *x.AppIcon
	}
	return ""
}

func (x *BidResponse_Bid_AdmNative) GetAppVersion() string {
	if x != nil && x.AppVersion != nil {
		return *x.AppVersion
	}
	return ""
}

type BidResponse_Bid_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// URL of the image asset.
	Url *string `protobuf:"bytes,1,req,name=url" json:"url,omitempty"`
	// Width of the image in pixels.
	W *int32 `protobuf:"varint,2,opt,name=w" json:"w,omitempty"`
	// Height of the image in pixels.
	H *int32 `protobuf:"varint,3,opt,name=h" json:"h,omitempty"`
	//Type of the image
	Type *BidResponse_Bid_Image_ImageAssetType `protobuf:"varint,4,opt,name=type,enum=go.micro.service.iqyi.BidResponse_Bid_Image_ImageAssetType" json:"type,omitempty"`
}

func (x *BidResponse_Bid_Image) Reset() {
	*x = BidResponse_Bid_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Image) ProtoMessage() {}

func (x *BidResponse_Bid_Image) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Image.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Image) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 5}
}

func (x *BidResponse_Bid_Image) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Bid_Image) GetW() int32 {
	if x != nil && x.W != nil {
		return *x.W
	}
	return 0
}

func (x *BidResponse_Bid_Image) GetH() int32 {
	if x != nil && x.H != nil {
		return *x.H
	}
	return 0
}

func (x *BidResponse_Bid_Image) GetType() BidResponse_Bid_Image_ImageAssetType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return BidResponse_Bid_Image_ICON
}

type BidResponse_Bid_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Video or image creative url.
	Url *string `protobuf:"bytes,1,req,name=url" json:"url,omitempty"`
	// Duration of video in seconds.
	Duration *int32 `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	// Description.
	Desc *string `protobuf:"bytes,4,opt,name=desc" json:"desc,omitempty"`
	// Cover view at begining of video.
	StartCover *string `protobuf:"bytes,5,opt,name=start_cover,json=startCover" json:"start_cover,omitempty"`
	// Cover view at end of video.
	CompleteCover *string `protobuf:"bytes,6,opt,name=complete_cover,json=completeCover" json:"complete_cover,omitempty"`
}

func (x *BidResponse_Bid_Video) Reset() {
	*x = BidResponse_Bid_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Video) ProtoMessage() {}

func (x *BidResponse_Bid_Video) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Video) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 6}
}

func (x *BidResponse_Bid_Video) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Bid_Video) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *BidResponse_Bid_Video) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *BidResponse_Bid_Video) GetStartCover() string {
	if x != nil && x.StartCover != nil {
		return *x.StartCover
	}
	return ""
}

func (x *BidResponse_Bid_Video) GetCompleteCover() string {
	if x != nil && x.CompleteCover != nil {
		return *x.CompleteCover
	}
	return ""
}

type BidResponse_Bid_DownloadTracker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start download url trakers.
	Startdownload []string `protobuf:"bytes,1,rep,name=startdownload" json:"startdownload,omitempty"`
	// finish download url trakers.
	Finishdownload []string `protobuf:"bytes,2,rep,name=finishdownload" json:"finishdownload,omitempty"`
	// start install url trakers.
	Startinstall []string `protobuf:"bytes,3,rep,name=startinstall" json:"startinstall,omitempty"`
	// finish install url trakers.
	Finishinstall []string `protobuf:"bytes,4,rep,name=finishinstall" json:"finishinstall,omitempty"`
}

func (x *BidResponse_Bid_DownloadTracker) Reset() {
	*x = BidResponse_Bid_DownloadTracker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_DownloadTracker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_DownloadTracker) ProtoMessage() {}

func (x *BidResponse_Bid_DownloadTracker) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_DownloadTracker.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_DownloadTracker) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 7}
}

func (x *BidResponse_Bid_DownloadTracker) GetStartdownload() []string {
	if x != nil {
		return x.Startdownload
	}
	return nil
}

func (x *BidResponse_Bid_DownloadTracker) GetFinishdownload() []string {
	if x != nil {
		return x.Finishdownload
	}
	return nil
}

func (x *BidResponse_Bid_DownloadTracker) GetStartinstall() []string {
	if x != nil {
		return x.Startinstall
	}
	return nil
}

func (x *BidResponse_Bid_DownloadTracker) GetFinishinstall() []string {
	if x != nil {
		return x.Finishinstall
	}
	return nil
}

type BidResponse_Bid_Link struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Landing URL of the clickable link.
	Curl *string `protobuf:"bytes,1,req,name=curl" json:"curl,omitempty"`
	// Array of impression tracking URLs. At least one object.
	Imptrackers []string `protobuf:"bytes,2,rep,name=imptrackers" json:"imptrackers,omitempty"`
	// Third-party tracker URLs to be fired on click of the URL.
	Clicktrackers []string `protobuf:"bytes,3,rep,name=clicktrackers" json:"clicktrackers,omitempty"`
	// download trackers
	Downloadtrackers *BidResponse_Bid_DownloadTracker `protobuf:"bytes,4,opt,name=downloadtrackers" json:"downloadtrackers,omitempty"`
	// deeplink.
	Deeplink *string `protobuf:"bytes,5,opt,name=deeplink" json:"deeplink,omitempty"`
	// deeplink trackers URLs.
	Deeplinktrackers []string `protobuf:"bytes,6,rep,name=deeplinktrackers" json:"deeplinktrackers,omitempty"`
	// Tracking urls of video start. At least one object.
	Starttrackers []string `protobuf:"bytes,7,rep,name=starttrackers" json:"starttrackers,omitempty"`
	// Tracking urls of video complete. At least one object.
	Completetrackers []string `protobuf:"bytes,8,rep,name=completetrackers" json:"completetrackers,omitempty"`
	// Tracking urls of first quartile point of video duration.
	FirstQuartileTrackers []string `protobuf:"bytes,9,rep,name=first_quartile_trackers,json=firstQuartileTrackers" json:"first_quartile_trackers,omitempty"`
	// Tracking urls of middle point of video duration.
	MidPointTrackers []string `protobuf:"bytes,10,rep,name=mid_point_trackers,json=midPointTrackers" json:"mid_point_trackers,omitempty"`
	// Tracking urls of third quartile point of video duration.
	ThirdQuartileTrackers []string `protobuf:"bytes,11,rep,name=third_quartile_trackers,json=thirdQuartileTrackers" json:"third_quartile_trackers,omitempty"`
	// Tracking urls of conversion, refer to api doc for conversion type
	// and replace the macro.
	ConversionTrackers []string `protobuf:"bytes,12,rep,name=conversion_trackers,json=conversionTrackers" json:"conversion_trackers,omitempty"`
}

func (x *BidResponse_Bid_Link) Reset() {
	*x = BidResponse_Bid_Link{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_Link) ProtoMessage() {}

func (x *BidResponse_Bid_Link) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_Link.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_Link) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 8}
}

func (x *BidResponse_Bid_Link) GetCurl() string {
	if x != nil && x.Curl != nil {
		return *x.Curl
	}
	return ""
}

func (x *BidResponse_Bid_Link) GetImptrackers() []string {
	if x != nil {
		return x.Imptrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetClicktrackers() []string {
	if x != nil {
		return x.Clicktrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetDownloadtrackers() *BidResponse_Bid_DownloadTracker {
	if x != nil {
		return x.Downloadtrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetDeeplink() string {
	if x != nil && x.Deeplink != nil {
		return *x.Deeplink
	}
	return ""
}

func (x *BidResponse_Bid_Link) GetDeeplinktrackers() []string {
	if x != nil {
		return x.Deeplinktrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetStarttrackers() []string {
	if x != nil {
		return x.Starttrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetCompletetrackers() []string {
	if x != nil {
		return x.Completetrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetFirstQuartileTrackers() []string {
	if x != nil {
		return x.FirstQuartileTrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetMidPointTrackers() []string {
	if x != nil {
		return x.MidPointTrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetThirdQuartileTrackers() []string {
	if x != nil {
		return x.ThirdQuartileTrackers
	}
	return nil
}

func (x *BidResponse_Bid_Link) GetConversionTrackers() []string {
	if x != nil {
		return x.ConversionTrackers
	}
	return nil
}

type BidResponse_Bid_TvAd_Html5 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url             *string  `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	XScale          *float32 `protobuf:"fixed32,2,opt,name=x_scale,json=xScale" json:"x_scale,omitempty"`
	YScale          *float32 `protobuf:"fixed32,3,opt,name=y_scale,json=yScale" json:"y_scale,omitempty"`
	MaxWidthScale   *float32 `protobuf:"fixed32,4,opt,name=max_width_scale,json=maxWidthScale" json:"max_width_scale,omitempty"`
	MaxHeightScale  *float32 `protobuf:"fixed32,5,opt,name=max_height_scale,json=maxHeightScale" json:"max_height_scale,omitempty"`
	Width           *int32   `protobuf:"varint,6,opt,name=width" json:"width,omitempty"`
	Height          *int32   `protobuf:"varint,7,opt,name=height" json:"height,omitempty"`
	QrOverlayAction *int32   `protobuf:"varint,8,opt,name=qr_overlay_action,json=qrOverlayAction" json:"qr_overlay_action,omitempty"`
}

func (x *BidResponse_Bid_TvAd_Html5) Reset() {
	*x = BidResponse_Bid_TvAd_Html5{}
	if protoimpl.UnsafeEnabled {
		mi := &file_iqiyi_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid_TvAd_Html5) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid_TvAd_Html5) ProtoMessage() {}

func (x *BidResponse_Bid_TvAd_Html5) ProtoReflect() protoreflect.Message {
	mi := &file_iqiyi_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid_TvAd_Html5.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid_TvAd_Html5) Descriptor() ([]byte, []int) {
	return file_iqiyi_proto_rawDescGZIP(), []int{2, 0, 2, 0}
}

func (x *BidResponse_Bid_TvAd_Html5) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *BidResponse_Bid_TvAd_Html5) GetXScale() float32 {
	if x != nil && x.XScale != nil {
		return *x.XScale
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetYScale() float32 {
	if x != nil && x.YScale != nil {
		return *x.YScale
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetMaxWidthScale() float32 {
	if x != nil && x.MaxWidthScale != nil {
		return *x.MaxWidthScale
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetMaxHeightScale() float32 {
	if x != nil && x.MaxHeightScale != nil {
		return *x.MaxHeightScale
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *BidResponse_Bid_TvAd_Html5) GetQrOverlayAction() int32 {
	if x != nil && x.QrOverlayAction != nil {
		return *x.QrOverlayAction
	}
	return 0
}

var File_iqiyi_proto protoreflect.FileDescriptor

var file_iqiyi_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x69, 0x71, 0x69, 0x79, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x22, 0x2f, 0x0a, 0x05, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xfe, 0x2f, 0x0a, 0x0a, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x02, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x37, 0x0a, 0x03, 0x69, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x02, 0x28,
	0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x53, 0x69, 0x74, 0x65, 0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x03, 0x61,
	0x70, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52,
	0x03, 0x61, 0x70, 0x70, 0x12, 0x40, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x04, 0x74, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x04, 0x74, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a,
	0x10, 0x64, 0x65, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x52, 0x0f, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x73, 0x1a, 0xd1, 0x0b, 0x0a, 0x03, 0x49,
	0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61, 0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x41,
	0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e,
	0x41, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41,
	0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x44, 0x0a, 0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x06, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x1a, 0xbf, 0x03, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x0c, 0x0a,
	0x01, 0x77, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68,
	0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28, 0x05, 0x52, 0x0b,
	0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d,
	0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x02, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x05, 0x20, 0x02, 0x28,
	0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x58, 0x0a,
	0x09, 0x6c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x02, 0x28, 0x0e,
	0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x2e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x09, 0x6c, 0x69,
	0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x61, 0x64, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x61, 0x64, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x49, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x22, 0x37, 0x0a, 0x0e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x0a,
	0x0a, 0x06, 0x4c, 0x49, 0x4e, 0x45, 0x41, 0x52, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f,
	0x4e, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x41, 0x52, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41,
	0x55, 0x53, 0x45, 0x10, 0x03, 0x1a, 0xe3, 0x04, 0x0a, 0x06, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x12, 0x46, 0x0a,
	0x04, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69,
	0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49,
	0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x48, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12,
	0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x61, 0x64, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x61, 0x64, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x1a, 0xd2, 0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x55, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79,
	0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77,
	0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x12,
	0x0a, 0x04, 0x77, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x77, 0x6d,
	0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x68, 0x6d, 0x69, 0x6e, 0x22, 0x2e, 0x0a, 0x0e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x4f, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x4d, 0x41, 0x49, 0x4e, 0x10, 0x03, 0x1a, 0xb2, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x01, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c,
	0x0a, 0x01, 0x68, 0x18, 0x02, 0x20, 0x02, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x02, 0x28,
	0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x02, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x49, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x31, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x22, 0x35, 0x0a, 0x06, 0x41,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x46, 0x45, 0x45, 0x44, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x50,
	0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x55, 0x53, 0x45,
	0x10, 0x03, 0x22, 0x3a, 0x0a, 0x0b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x41, 0x4e, 0x59, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x46, 0x4c, 0x56, 0x10, 0x01, 0x12,
	0x0d, 0x0a, 0x09, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x4d, 0x50, 0x34, 0x10, 0x02, 0x1a, 0x86,
	0x03, 0x0a, 0x04, 0x53, 0x69, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x63, 0x61, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x66,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x12, 0x49, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x43,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x1a, 0xef, 0x02, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x70, 0x61, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x09, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x52, 0x09, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x75, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x1a, 0x59, 0x0a, 0x09, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x1a, 0xc4, 0x08, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x70, 0x69, 0x73, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x65, 0x70, 0x69, 0x73, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x65, 0x6e, 0x72,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x65, 0x6e, 0x72, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x6c, 0x62, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x6c, 0x62, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x61, 0x74, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x51, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x64,
	0x71, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63,
	0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x64, 0x71, 0x12, 0x52, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x72,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69,
	0x70, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x60, 0x0a, 0x0e, 0x71,
	0x61, 0x67, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2e, 0x51,
	0x41, 0x47, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0e, 0x71,
	0x61, 0x67, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x5c, 0x0a, 0x11, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x13,
	0x0a, 0x0f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x4f, 0x46, 0x45, 0x53, 0x53, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x4f, 0x53, 0x55, 0x4d, 0x45,
	0x52, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x22, 0x6b, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x49, 0x44,
	0x45, 0x4f, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x41, 0x4d, 0x45, 0x10, 0x02, 0x12, 0x09,
	0x0a, 0x05, 0x4d, 0x55, 0x53, 0x49, 0x43, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45,
	0x58, 0x54, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x06, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x07, 0x22, 0x45, 0x0a, 0x0e, 0x51, 0x41, 0x47, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x55,
	0x44, 0x49, 0x45, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x56, 0x45,
	0x52, 0x59, 0x4f, 0x4e, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x31, 0x32, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x03, 0x1a, 0x58, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x63, 0x61, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x1a, 0xdc, 0x0c, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x02, 0x75, 0x61,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x37, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x64, 0x66, 0x61, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x69, 0x64, 0x66, 0x61, 0x4d, 0x64, 0x35, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x65, 0x69,
	0x5f, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x65, 0x69,
	0x4d, 0x64, 0x35, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x69, 0x64, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x61,
	0x69, 0x64, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61,
	0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x4d, 0x61, 0x63,
	0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x0c, 0x0a, 0x01, 0x77,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x68, 0x12, 0x5f, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x74, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x74, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x09, 0x63, 0x61, 0x69, 0x64, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79,
	0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x61,
	0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x53, 0x65, 0x63, 0x12, 0x26, 0x0a,
	0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x4f, 0x66, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f,
	0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x29, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x65, 0x6d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6d, 0x65, 0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x34, 0x0a, 0x04, 0x43, 0x61, 0x69, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x1a, 0x4d, 0x0a, 0x08,
	0x43, 0x61, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x04, 0x63, 0x61, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x43, 0x61, 0x69, 0x64, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x22, 0x53, 0x0a, 0x0a, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54,
	0x45, 0x52, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x54, 0x56, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x41, 0x44, 0x10, 0x05,
	0x22, 0x49, 0x0a, 0x06, 0x54, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x56, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x54, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x56, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x42, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x56, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x50, 0x54, 0x56, 0x10, 0x03, 0x22, 0x86, 0x01, 0x0a, 0x0e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x54, 0x48, 0x45, 0x52, 0x4e,
	0x45, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x49, 0x46, 0x49, 0x10, 0x02, 0x12, 0x10,
	0x0a, 0x0c, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03,
	0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x32, 0x47, 0x10, 0x04, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x33, 0x47, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45,
	0x4c, 0x4c, 0x5f, 0x34, 0x47, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x45, 0x4c, 0x4c, 0x5f,
	0x35, 0x47, 0x10, 0x07, 0x1a, 0x8c, 0x02, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x72,
	0x6f, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x76, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x46,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x47, 0x65, 0x6f, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x3b, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x50, 0x53, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x50, 0x10, 0x02,
	0x12, 0x11, 0x0a, 0x0d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45,
	0x44, 0x10, 0x03, 0x1a, 0x8a, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x34, 0x0a, 0x0e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbd, 0x21, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x02,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x12, 0x47, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71,
	0x79, 0x69, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x62,
	0x75, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xfb, 0x1f, 0x0a, 0x03, 0x42, 0x69, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x7a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a,
	0x08, 0x61, 0x64, 0x6d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x52, 0x08, 0x61, 0x64, 0x6d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x4e, 0x0a, 0x09, 0x61, 0x64,
	0x6d, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x6d, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x09, 0x61, 0x64, 0x6d, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69,
	0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x69,
	0x6e, 0x69, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x4b, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x29,
	0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x44, 0x65,
	0x73, 0x63, 0x50, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x63, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x63, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x58, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3f, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b,
	0x12, 0x49, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x41, 0x64, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x07, 0x61, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x48, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x40, 0x0a, 0x05, 0x74, 0x76, 0x5f, 0x61, 0x64, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x54, 0x76, 0x41,
	0x64, 0x52, 0x04, 0x74, 0x76, 0x41, 0x64, 0x12, 0x47, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x1a, 0x25, 0x0a, 0x07, 0x41, 0x64, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x9b, 0x01, 0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x47, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x47, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x4e, 0x5f, 0x49, 0x4d, 0x4d, 0x45,
	0x52, 0x53, 0x49, 0x56, 0x45, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4e, 0x4f, 0x4e, 0x5f, 0x49,
	0x4d, 0x4d, 0x45, 0x52, 0x53, 0x49, 0x56, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4d, 0x4d, 0x45, 0x52, 0x53,
	0x49, 0x56, 0x45, 0x10, 0x02, 0x1a, 0xc9, 0x02, 0x0a, 0x04, 0x54, 0x76, 0x41, 0x64, 0x12, 0x47,
	0x0a, 0x05, 0x68, 0x74, 0x6d, 0x6c, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e,
	0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x54, 0x76, 0x41, 0x64, 0x2e, 0x48, 0x74, 0x6d, 0x6c, 0x35,
	0x52, 0x05, 0x68, 0x74, 0x6d, 0x6c, 0x35, 0x1a, 0xf7, 0x01, 0x0a, 0x05, 0x48, 0x74, 0x6d, 0x6c,
	0x35, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x78, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x78, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x79, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x79,
	0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d,
	0x6d, 0x61, 0x78, 0x57, 0x69, 0x64, 0x74, 0x68, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x6d, 0x61, 0x78, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x73, 0x63, 0x61, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x71, 0x72, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x6c, 0x61, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x71, 0x72, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0xe0, 0x04, 0x0a, 0x08, 0x41, 0x64, 0x6d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x40,
	0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x69, 0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73,
	0x12, 0x42, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3f,
	0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x64, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12,
	0x2e, 0x0a, 0x13, 0x74, 0x72, 0x75, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x74, 0x72,
	0x75, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x2b, 0x0a, 0x11, 0x74, 0x72, 0x75, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x75, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x73, 0x6b, 0x69, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x6b, 0x69, 0x70,
	0x70, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x34,
	0x0a, 0x16, 0x74, 0x72, 0x75, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x6b, 0x69, 0x70, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14,
	0x74, 0x72, 0x75, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x6b, 0x69, 0x70, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x1a, 0xe2, 0x02, 0x0a, 0x09, 0x41, 0x64, 0x6d, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79,
	0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69,
	0x64, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x3f,
	0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x42, 0x69, 0x64, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xb6, 0x01, 0x0a, 0x05, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x0c, 0x0a, 0x01, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x77, 0x12, 0x0c, 0x0a, 0x01, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x68, 0x12, 0x4f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x2e, 0x0a, 0x0e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x4c, 0x4f, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x41, 0x49, 0x4e,
	0x10, 0x03, 0x1a, 0x91, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x1a, 0xa9, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x24, 0x0a, 0x0d,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x1a, 0xaf, 0x04, 0x0a, 0x04, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x04, 0x63, 0x75, 0x72, 0x6c, 0x12,
	0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x73, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x62, 0x0a, 0x10, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x69, 0x71, 0x79, 0x69, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x64, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x52, 0x10, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x10, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x71,
	0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x66, 0x69, 0x72, 0x73, 0x74, 0x51, 0x75, 0x61,
	0x72, 0x74, 0x69, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x2c, 0x0a,
	0x12, 0x6d, 0x69, 0x64, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x69, 0x64, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x74,
	0x68, 0x69, 0x72, 0x64, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x74, 0x68,
	0x69, 0x72, 0x64, 0x51, 0x75, 0x61, 0x72, 0x74, 0x69, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x65, 0x72, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x73, 0x22, 0x7c, 0x0a, 0x0c, 0x41, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x5f,
	0x57, 0x45, 0x42, 0x56, 0x49, 0x45, 0x57, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x50, 0x45,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x41, 0x50, 0x50,
	0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x55,
	0x4e, 0x49, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x04, 0x12,
	0x11, 0x0a, 0x0d, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x41, 0x50, 0x50,
	0x10, 0x05, 0x22, 0x22, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4d, 0x47, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x56,
	0x49, 0x44, 0x45, 0x4f, 0x10, 0x01, 0x22, 0x3e, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x4f, 0x52, 0x49,
	0x5a, 0x4f, 0x4e, 0x54, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x56, 0x45, 0x52, 0x54,
	0x49, 0x43, 0x41, 0x4c, 0x10, 0x02, 0x22, 0xc0, 0x01, 0x0a, 0x14, 0x51, 0x78, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2a, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x01, 0x30, 0x52, 0x0e, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x0e, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x3a, 0x01, 0x30, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2a, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64,
	0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x01,
	0x30, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x26, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x01, 0x30, 0x52, 0x0c, 0x73, 0x6b, 0x69,
	0x70, 0x70, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x1a, 0x5a, 0x18, 0x2e, 0x2f, 0x3b,
	0x67, 0x6f, 0x5f, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x71, 0x79, 0x69,
}

var (
	file_iqiyi_proto_rawDescOnce sync.Once
	file_iqiyi_proto_rawDescData = file_iqiyi_proto_rawDesc
)

func file_iqiyi_proto_rawDescGZIP() []byte {
	file_iqiyi_proto_rawDescOnce.Do(func() {
		file_iqiyi_proto_rawDescData = protoimpl.X.CompressGZIP(file_iqiyi_proto_rawDescData)
	})
	return file_iqiyi_proto_rawDescData
}

var file_iqiyi_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_iqiyi_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_iqiyi_proto_goTypes = []interface{}{
	(BidRequest_Imp_AdType)(0),                      // 0: go.micro.service.iqyi.BidRequest.Imp.AdType
	(BidRequest_Imp_VideoFormat)(0),                 // 1: go.micro.service.iqyi.BidRequest.Imp.VideoFormat
	(BidRequest_Imp_Video_VideoLinearity)(0),        // 2: go.micro.service.iqyi.BidRequest.Imp.Video.VideoLinearity
	(BidRequest_Imp_Native_Image_ImageAssetType)(0), // 3: go.micro.service.iqyi.BidRequest.Imp.Native.Image.ImageAssetType
	(BidRequest_Content_ProductionQuality)(0),       // 4: go.micro.service.iqyi.BidRequest.Content.ProductionQuality
	(BidRequest_Content_ContentContext)(0),          // 5: go.micro.service.iqyi.BidRequest.Content.ContentContext
	(BidRequest_Content_QAGMediaRating)(0),          // 6: go.micro.service.iqyi.BidRequest.Content.QAGMediaRating
	(BidRequest_Device_DeviceType)(0),               // 7: go.micro.service.iqyi.BidRequest.Device.DeviceType
	(BidRequest_Device_TvType)(0),                   // 8: go.micro.service.iqyi.BidRequest.Device.TvType
	(BidRequest_Device_ConnectionType)(0),           // 9: go.micro.service.iqyi.BidRequest.Device.ConnectionType
	(BidRequest_Geo_LocationType)(0),                // 10: go.micro.service.iqyi.BidRequest.Geo.LocationType
	(BidResponse_Bid_AdActionType)(0),               // 11: go.micro.service.iqyi.BidResponse.Recall.AdActionType
	(BidResponse_Bid_CreativeType)(0),               // 12: go.micro.service.iqyi.BidResponse.Recall.CreativeType
	(BidResponse_Bid_CreativeDirection)(0),          // 13: go.micro.service.iqyi.BidResponse.Recall.CreativeDirection
	(BidResponse_Bid_Opening_Type)(0),               // 14: go.micro.service.iqyi.BidResponse.Recall.Opening.Type
	(BidResponse_Bid_Image_ImageAssetType)(0),       // 15: go.micro.service.iqyi.BidResponse.Recall.Image.ImageAssetType
	(*Entry)(nil),                           // 16: go.micro.service.iqyi.Entry
	(*BidRequest)(nil),                      // 17: go.micro.service.iqyi.BidRequest
	(*BidResponse)(nil),                     // 18: go.micro.service.iqyi.BidResponse
	(*QxForbiddenCacheInfo)(nil),            // 19: go.micro.service.iqyi.QxForbiddenCacheInfo
	(*BidRequest_Imp)(nil),                  // 20: go.micro.service.iqyi.BidRequest.Imp
	(*BidRequest_Site)(nil),                 // 21: go.micro.service.iqyi.BidRequest.Site
	(*BidRequest_App)(nil),                  // 22: go.micro.service.iqyi.BidRequest.App
	(*BidRequest_Publisher)(nil),            // 23: go.micro.service.iqyi.BidRequest.Publisher
	(*BidRequest_Content)(nil),              // 24: go.micro.service.iqyi.BidRequest.Content
	(*BidRequest_Producer)(nil),             // 25: go.micro.service.iqyi.BidRequest.Producer
	(*BidRequest_Device)(nil),               // 26: go.micro.service.iqyi.BidRequest.Device
	(*BidRequest_Geo)(nil),                  // 27: go.micro.service.iqyi.BidRequest.Geo
	(*BidRequest_User)(nil),                 // 28: go.micro.service.iqyi.BidRequest.User
	(*BidRequest_DeduplicatedId)(nil),       // 29: go.micro.service.iqyi.BidRequest.DeduplicatedId
	(*BidRequest_Imp_Video)(nil),            // 30: go.micro.service.iqyi.BidRequest.Imp.Video
	(*BidRequest_Imp_Native)(nil),           // 31: go.micro.service.iqyi.BidRequest.Imp.Native
	(*BidRequest_Imp_Native_Image)(nil),     // 32: go.micro.service.iqyi.BidRequest.Imp.Native.Image
	(*BidRequest_Imp_Native_Video)(nil),     // 33: go.micro.service.iqyi.BidRequest.Imp.Native.Video
	(*BidRequest_Device_Caid)(nil),          // 34: go.micro.service.iqyi.BidRequest.Device.CaidVersions
	(*BidRequest_Device_CaidInfo)(nil),      // 35: go.micro.service.iqyi.BidRequest.Device.CaidInfo
	(*BidResponse_Bid)(nil),                 // 36: go.micro.service.iqyi.BidResponse.Recall
	(*BidResponse_Bid_AdVideo)(nil),         // 37: go.micro.service.iqyi.BidResponse.Recall.AdVideo
	(*BidResponse_Bid_Opening)(nil),         // 38: go.micro.service.iqyi.BidResponse.Recall.Opening
	(*BidResponse_Bid_TvAd)(nil),            // 39: go.micro.service.iqyi.BidResponse.Recall.TvAd
	(*BidResponse_Bid_AdmVideo)(nil),        // 40: go.micro.service.iqyi.BidResponse.Recall.AdmVideo
	(*BidResponse_Bid_AdmNative)(nil),       // 41: go.micro.service.iqyi.BidResponse.Recall.AdmNative
	(*BidResponse_Bid_Image)(nil),           // 42: go.micro.service.iqyi.BidResponse.Recall.Image
	(*BidResponse_Bid_Video)(nil),           // 43: go.micro.service.iqyi.BidResponse.Recall.Video
	(*BidResponse_Bid_DownloadTracker)(nil), // 44: go.micro.service.iqyi.BidResponse.Recall.DownloadTracker
	(*BidResponse_Bid_Link)(nil),            // 45: go.micro.service.iqyi.BidResponse.Recall.Link
	(*BidResponse_Bid_TvAd_Html5)(nil),      // 46: go.micro.service.iqyi.BidResponse.Recall.TvAd.Html5
}
var file_iqiyi_proto_depIdxs = []int32{
	20, // 0: go.micro.service.iqyi.BidRequest.imp:type_name -> go.micro.service.iqyi.BidRequest.Imp
	21, // 1: go.micro.service.iqyi.BidRequest.site:type_name -> go.micro.service.iqyi.BidRequest.Site
	22, // 2: go.micro.service.iqyi.BidRequest.app:type_name -> go.micro.service.iqyi.BidRequest.App
	26, // 3: go.micro.service.iqyi.BidRequest.device:type_name -> go.micro.service.iqyi.BidRequest.Device
	28, // 4: go.micro.service.iqyi.BidRequest.user:type_name -> go.micro.service.iqyi.BidRequest.User
	29, // 5: go.micro.service.iqyi.BidRequest.deduplicated_ids:type_name -> go.micro.service.iqyi.BidRequest.DeduplicatedId
	36, // 6: go.micro.service.iqyi.BidResponse.bid:type_name -> go.micro.service.iqyi.BidResponse.Recall
	16, // 7: go.micro.service.iqyi.BidResponse.extended_entries:type_name -> go.micro.service.iqyi.Entry
	0,  // 8: go.micro.service.iqyi.BidRequest.Imp.ad_type:type_name -> go.micro.service.iqyi.BidRequest.Imp.AdType
	30, // 9: go.micro.service.iqyi.BidRequest.Imp.video:type_name -> go.micro.service.iqyi.BidRequest.Imp.Video
	31, // 10: go.micro.service.iqyi.BidRequest.Imp.native:type_name -> go.micro.service.iqyi.BidRequest.Imp.Native
	23, // 11: go.micro.service.iqyi.BidRequest.Site.publisher:type_name -> go.micro.service.iqyi.BidRequest.Publisher
	24, // 12: go.micro.service.iqyi.BidRequest.Site.content:type_name -> go.micro.service.iqyi.BidRequest.Content
	23, // 13: go.micro.service.iqyi.BidRequest.App.publisher:type_name -> go.micro.service.iqyi.BidRequest.Publisher
	24, // 14: go.micro.service.iqyi.BidRequest.App.content:type_name -> go.micro.service.iqyi.BidRequest.Content
	4,  // 15: go.micro.service.iqyi.BidRequest.Content.prodq:type_name -> go.micro.service.iqyi.BidRequest.Content.ProductionQuality
	5,  // 16: go.micro.service.iqyi.BidRequest.Content.context:type_name -> go.micro.service.iqyi.BidRequest.Content.ContentContext
	25, // 17: go.micro.service.iqyi.BidRequest.Content.producer:type_name -> go.micro.service.iqyi.BidRequest.Producer
	6,  // 18: go.micro.service.iqyi.BidRequest.Content.qagmediarating:type_name -> go.micro.service.iqyi.BidRequest.Content.QAGMediaRating
	27, // 19: go.micro.service.iqyi.BidRequest.Device.geo:type_name -> go.micro.service.iqyi.BidRequest.Geo
	9,  // 20: go.micro.service.iqyi.BidRequest.Device.connectiontype:type_name -> go.micro.service.iqyi.BidRequest.Device.ConnectionType
	7,  // 21: go.micro.service.iqyi.BidRequest.Device.devicetype:type_name -> go.micro.service.iqyi.BidRequest.Device.DeviceType
	35, // 22: go.micro.service.iqyi.BidRequest.Device.caid_info:type_name -> go.micro.service.iqyi.BidRequest.Device.CaidInfo
	10, // 23: go.micro.service.iqyi.BidRequest.Geo.type:type_name -> go.micro.service.iqyi.BidRequest.Geo.LocationType
	2,  // 24: go.micro.service.iqyi.BidRequest.Imp.Video.linearity:type_name -> go.micro.service.iqyi.BidRequest.Imp.Video.VideoLinearity
	1,  // 25: go.micro.service.iqyi.BidRequest.Imp.Video.format:type_name -> go.micro.service.iqyi.BidRequest.Imp.VideoFormat
	32, // 26: go.micro.service.iqyi.BidRequest.Imp.Native.imgs:type_name -> go.micro.service.iqyi.BidRequest.Imp.Native.Image
	33, // 27: go.micro.service.iqyi.BidRequest.Imp.Native.video:type_name -> go.micro.service.iqyi.BidRequest.Imp.Native.Video
	3,  // 28: go.micro.service.iqyi.BidRequest.Imp.Native.Image.type:type_name -> go.micro.service.iqyi.BidRequest.Imp.Native.Image.ImageAssetType
	1,  // 29: go.micro.service.iqyi.BidRequest.Imp.Native.Video.format:type_name -> go.micro.service.iqyi.BidRequest.Imp.VideoFormat
	34, // 30: go.micro.service.iqyi.BidRequest.Device.CaidInfo.caid:type_name -> go.micro.service.iqyi.BidRequest.Device.CaidVersions
	40, // 31: go.micro.service.iqyi.BidResponse.Recall.admvideo:type_name -> go.micro.service.iqyi.BidResponse.Recall.AdmVideo
	41, // 32: go.micro.service.iqyi.BidResponse.Recall.admnative:type_name -> go.micro.service.iqyi.BidResponse.Recall.AdmNative
	11, // 33: go.micro.service.iqyi.BidResponse.Recall.action:type_name -> go.micro.service.iqyi.BidResponse.Recall.AdActionType
	12, // 34: go.micro.service.iqyi.BidResponse.Recall.creative_type:type_name -> go.micro.service.iqyi.BidResponse.Recall.CreativeType
	45, // 35: go.micro.service.iqyi.BidResponse.Recall.link:type_name -> go.micro.service.iqyi.BidResponse.Recall.Link
	37, // 36: go.micro.service.iqyi.BidResponse.Recall.ad_video:type_name -> go.micro.service.iqyi.BidResponse.Recall.AdVideo
	38, // 37: go.micro.service.iqyi.BidResponse.Recall.opening:type_name -> go.micro.service.iqyi.BidResponse.Recall.Opening
	39, // 38: go.micro.service.iqyi.BidResponse.Recall.tv_ad:type_name -> go.micro.service.iqyi.BidResponse.Recall.TvAd
	16, // 39: go.micro.service.iqyi.BidResponse.Recall.extended_entries:type_name -> go.micro.service.iqyi.Entry
	14, // 40: go.micro.service.iqyi.BidResponse.Recall.Opening.type:type_name -> go.micro.service.iqyi.BidResponse.Recall.Opening.Type
	46, // 41: go.micro.service.iqyi.BidResponse.Recall.TvAd.html5:type_name -> go.micro.service.iqyi.BidResponse.Recall.TvAd.Html5
	42, // 42: go.micro.service.iqyi.BidResponse.Recall.AdmVideo.imgs:type_name -> go.micro.service.iqyi.BidResponse.Recall.Image
	43, // 43: go.micro.service.iqyi.BidResponse.Recall.AdmVideo.video:type_name -> go.micro.service.iqyi.BidResponse.Recall.Video
	45, // 44: go.micro.service.iqyi.BidResponse.Recall.AdmVideo.link:type_name -> go.micro.service.iqyi.BidResponse.Recall.Link
	42, // 45: go.micro.service.iqyi.BidResponse.Recall.AdmNative.imgs:type_name -> go.micro.service.iqyi.BidResponse.Recall.Image
	43, // 46: go.micro.service.iqyi.BidResponse.Recall.AdmNative.video:type_name -> go.micro.service.iqyi.BidResponse.Recall.Video
	45, // 47: go.micro.service.iqyi.BidResponse.Recall.AdmNative.link:type_name -> go.micro.service.iqyi.BidResponse.Recall.Link
	15, // 48: go.micro.service.iqyi.BidResponse.Recall.Image.type:type_name -> go.micro.service.iqyi.BidResponse.Recall.Image.ImageAssetType
	44, // 49: go.micro.service.iqyi.BidResponse.Recall.Link.downloadtrackers:type_name -> go.micro.service.iqyi.BidResponse.Recall.DownloadTracker
	50, // [50:50] is the sub-list for method output_type
	50, // [50:50] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_iqiyi_proto_init() }
func file_iqiyi_proto_init() {
	if File_iqiyi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_iqiyi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Entry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QxForbiddenCacheInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Site); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Publisher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Producer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_DeduplicatedId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp_Native_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_Caid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device_CaidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_AdVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Opening); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_TvAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_AdmVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_AdmNative); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_DownloadTracker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_Link); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_iqiyi_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid_TvAd_Html5); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_iqiyi_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_iqiyi_proto_goTypes,
		DependencyIndexes: file_iqiyi_proto_depIdxs,
		EnumInfos:         file_iqiyi_proto_enumTypes,
		MessageInfos:      file_iqiyi_proto_msgTypes,
	}.Build()
	File_iqiyi_proto = out.File
	file_iqiyi_proto_rawDesc = nil
	file_iqiyi_proto_goTypes = nil
	file_iqiyi_proto_depIdxs = nil
}
