syntax = "proto3";

package go.micro.service.adx4dsp;

option go_package = "./;go_micro_service_adx4dsp";


enum STATUS {
  OK = 0; //成功
}

message ResponseStatus {
  STATUS code = 1;
  string msg = 2;
}

message CaidFactors {
  string bootTimeInSec = 1; // 设备启动时间（秒） 示例值: "1595643553"
  string countryCode = 2; // 国家 示例值: "CN"
  string language = 3; // 语言 示例值: "zh-Hans-CN"
  string deviceName = 4; // 设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
  string systemVersion = 5; // 系统版本 示例值: "14.0"
  string machine = 6; // 设备Machine 示例值: "iPhone10,3"
  string carrierInfo = 7; // 运营商 示例值: "中国移动"
  string memory = 8; // 物理内存容量 示例值: "3955589120"
  string disk = 9; // 硬盘容量 示例值: "63900340224"
  string sysFileTime = 10; // 系统更新时间 示例值: "1595214620.383940"
  string model = 11; // 设备Model 示例值: "D22AP"
  string timeZone = 12; // 时区 示例值: "28800"
  string mntId = 13; // 挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
  string deviceInitTime = 14; // 设备初始化时间 示例值: "1632467920.301150749"
}

message CaidWithFactors {
  CaidFactors  factors = 1;
  string caid  = 2;
  string caidLast  = 5;
  string caidVersion = 3;
  string caidVersionLast = 6;
  string caidGenTime = 4;
}

message BidRequest {
  string id = 1;
  repeated Imp imp = 2;
  App app = 3;
  Device device = 4;
  User user = 5;
  string apiVersion = 6;
  repeated string installedApp = 7;
  repeated int64 appendix01 = 8;
  Ext ext =10;

  message Ext{
    SdkBidExt sdkBidExt = 1;
  }
  message SdkBidExt{
      string ylhBuyerId = 1; // 优量汇SDK获取的buyerID
      string ylhOpensdkVer = 3; // 优量汇SDKopenSDK 版本，用于判断是否能支持微信小程序广告
      bool ylhSupportSplashZoomout =5 ; // 优量汇SDK是否支持开屏 V+
      string ylhSdkInfo = 6; // 优量汇SDK获取的SDKInfo，缺失时将影响广告推荐效果，强烈建议开发者全量上传
  }
  message Imp{
    string id = 7;
    string tagId = 8;
    AdslotSize adslotSize = 9;
    double bidfloor = 10;
    bool deeplink = 11;
    int32 secure = 12;
    repeated Deal deals = 13;
  }


  message AdslotSize{
    int32 width = 14;
    int32 height = 15;
    repeated string mimes = 16;
    int32 size = 17;
    int32 titleLength = 18;
    int32 descLength = 19;
    int32 minDuration = 20;
    int32 maxDuration = 21;
  }

  message App{
    string appName = 22;
    string bundle = 23;
    string appVersion = 24;
  }

  message Device{
    string os = 25;
    string osv = 26;
    string imei = 27;
    string imeiMd5 = 28;
    string oaid = 29;
    string oaidMd5 = 30;
    string androidId = 31;
    string idfa = 32;
    string idfaMd5 = 33;
    string mac = 34;
    string macMd5 = 35;
    string ip = 36;
    string ipV6 = 37;
    string ua = 38;
    int32 connectionType = 39;
    string brand = 40;
    string make = 41;
    string model= 42;
    string hwv = 43;
    int32 carrier = 44;
    string mccMnc = 45;
    int32 screenHeight = 46;
    int32 screenWidth = 47;
    int32 ppi = 48;
    Geo geo = 49;
    string appList = 50;
    string bootMark = 51;
    string updateMark = 52;
    string updateMark1 = 74; // 爱奇艺专用
    string verCodeOfHms = 53;
    string verCodeOfAG = 54;
    string romVersion  = 55;
    int32 orientation = 56;
    string bootTimeSec = 57;
    string phoneName = 58;
    int64 memorySize = 59;
    int64 diskSize = 60;
    string osUpdateTimeSec = 61;
    string modelCode  = 62;
    string timeZone  = 63;
    // 废弃
    string fileTime  = 64;
    string deviceBirthTime  = 65;
    string caidVersion  = 66;
    string caid = 67;
    string paid = 68;
    CaidWithFactors caidWithFactors = 69;
    bool wxInstalled = 70;
  }

  message Deal{
    string id = 57;
    double bidfloor = 58;
    int32 deal_type = 105;
    double pdfloor = 106;
  }

  message Geo{
    int32 type = 59;
    double lat = 60;
    double lon = 61;
    string country  = 62;
  }

  message User{
    int32 age = 63;
    int32 gender = 64;
  }
}

message BidResponse {
  string id = 65;
  string bidid = 66;
  repeated SeatBid seatbid = 67;
  ResponseExt ext = 78;

  message SeatBid{
    repeated Bid bid = 68;
  }

  message Bid{
    string impid = 69;
    int32 adType = 70;
    int32 adStyle = 71;
    Item  items = 72;
    double price = 73;
    string nurl = 74;
    string cid = 107;
    string crid = 99;
    string apiName = 104;
    string lurl = 77;
  }

  message ResponseExt{
    SdkResponseExt SdkExt = 1;
  }

  message SdkResponseExt{
    string ylhtoken = 1;
  }

  message Item{
    string title = 75;
    string desc = 76;
    string icon = 77;
    string html = 78;
    int32 mediaStyle = 79;
    string downloadUrl = 80;
    DownloadAppInfo downloadAppInfo = 81;
    string clickUrl = 82;
    string dplUrl = 83;
    repeated string imgs = 84;
    repeated string exposalUrls = 85;
    repeated string clickMonitorUrls = 86;
    Video video = 87;
    string miniProgramId = 88;
    string miniProgramPath = 89;
    int32 miniProgramType = 90;
    string miniProgramExtData = 99;
    repeated string miniProgramSuccessTrackUrls = 91;
    repeated string downloadTrackUrls = 92;
    repeated string downloadedTrackUrls = 93;
    repeated string installedTrackUrls = 94;
    repeated string dpSuccessTrackUrls = 95;
    repeated string actionTrackUrls = 96;
    string packageName = 97;
    repeated MonitorUrl monitorUrls = 98;
  }

  message MonitorUrl{
    string eventType = 1;
    repeated string urls = 2;
  }

  message DownloadAppInfo{
    string appName = 88;
    string developer = 89;
    string version = 90;
    string packetSize = 91;
    string privacy = 92;
    string permission = 93;
    string desc = 101;
    string descURL = 102;
  }

  message Video{
    string videoUrl = 94;
    int32 videoDuration = 95;
    string videoStartUrl = 96;
    string videoFinishUrl = 97;
    string videoVastXml = 98;
    string videoEndImgurl = 99;
    string videoPreImgurl = 100;
  }
}
