// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.3
// source: adx4dsp.proto

package go_micro_service_adx4dsp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type STATUS int32

const (
	STATUS_OK STATUS = 0 //成功
)

// Enum value maps for STATUS.
var (
	STATUS_name = map[int32]string{
		0: "OK",
	}
	STATUS_value = map[string]int32{
		"OK": 0,
	}
)

func (x STATUS) Enum() *STATUS {
	p := new(STATUS)
	*p = x
	return p
}

func (x STATUS) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (STATUS) Descriptor() protoreflect.EnumDescriptor {
	return file_adx4dsp_proto_enumTypes[0].Descriptor()
}

func (STATUS) Type() protoreflect.EnumType {
	return &file_adx4dsp_proto_enumTypes[0]
}

func (x STATUS) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use STATUS.Descriptor instead.
func (STATUS) EnumDescriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{0}
}

type ResponseStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code STATUS `protobuf:"varint,1,opt,name=code,proto3,enum=go.micro.service.adx4dsp.STATUS" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ResponseStatus) Reset() {
	*x = ResponseStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseStatus) ProtoMessage() {}

func (x *ResponseStatus) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseStatus.ProtoReflect.Descriptor instead.
func (*ResponseStatus) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{0}
}

func (x *ResponseStatus) GetCode() STATUS {
	if x != nil {
		return x.Code
	}
	return STATUS_OK
}

func (x *ResponseStatus) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CaidFactors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BootTimeInSec  string `protobuf:"bytes,1,opt,name=bootTimeInSec,proto3" json:"bootTimeInSec,omitempty"`    // 设备启动时间（秒） 示例值: "1595643553"
	CountryCode    string `protobuf:"bytes,2,opt,name=countryCode,proto3" json:"countryCode,omitempty"`        // 国家 示例值: "CN"
	Language       string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`              // 语言 示例值: "zh-Hans-CN"
	DeviceName     string `protobuf:"bytes,4,opt,name=deviceName,proto3" json:"deviceName,omitempty"`          // 设备名称 示例值: "e910dddb2748c36b47fcde5dd720eec1"
	SystemVersion  string `protobuf:"bytes,5,opt,name=systemVersion,proto3" json:"systemVersion,omitempty"`    // 系统版本 示例值: "14.0"
	Machine        string `protobuf:"bytes,6,opt,name=machine,proto3" json:"machine,omitempty"`                // 设备Machine 示例值: "iPhone10,3"
	CarrierInfo    string `protobuf:"bytes,7,opt,name=carrierInfo,proto3" json:"carrierInfo,omitempty"`        // 运营商 示例值: "中国移动"
	Memory         string `protobuf:"bytes,8,opt,name=memory,proto3" json:"memory,omitempty"`                  // 物理内存容量 示例值: "3955589120"
	Disk           string `protobuf:"bytes,9,opt,name=disk,proto3" json:"disk,omitempty"`                      // 硬盘容量 示例值: "63900340224"
	SysFileTime    string `protobuf:"bytes,10,opt,name=sysFileTime,proto3" json:"sysFileTime,omitempty"`       // 系统更新时间 示例值: "1595214620.383940"
	Model          string `protobuf:"bytes,11,opt,name=model,proto3" json:"model,omitempty"`                   // 设备Model 示例值: "D22AP"
	TimeZone       string `protobuf:"bytes,12,opt,name=timeZone,proto3" json:"timeZone,omitempty"`             // 时区 示例值: "28800"
	MntId          string `protobuf:"bytes,13,opt,name=mntId,proto3" json:"mntId,omitempty"`                   // 挂载ID mnt_id 示例值: "80825948939346695D0D7DD52CB405D11A80344027A07803D5F8410346398776C879BF6BD67627@/dev/disk1s1"
	DeviceInitTime string `protobuf:"bytes,14,opt,name=deviceInitTime,proto3" json:"deviceInitTime,omitempty"` // 设备初始化时间 示例值: "1632467920.301150749"
}

func (x *CaidFactors) Reset() {
	*x = CaidFactors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaidFactors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaidFactors) ProtoMessage() {}

func (x *CaidFactors) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaidFactors.ProtoReflect.Descriptor instead.
func (*CaidFactors) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{1}
}

func (x *CaidFactors) GetBootTimeInSec() string {
	if x != nil {
		return x.BootTimeInSec
	}
	return ""
}

func (x *CaidFactors) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *CaidFactors) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CaidFactors) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CaidFactors) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *CaidFactors) GetMachine() string {
	if x != nil {
		return x.Machine
	}
	return ""
}

func (x *CaidFactors) GetCarrierInfo() string {
	if x != nil {
		return x.CarrierInfo
	}
	return ""
}

func (x *CaidFactors) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *CaidFactors) GetDisk() string {
	if x != nil {
		return x.Disk
	}
	return ""
}

func (x *CaidFactors) GetSysFileTime() string {
	if x != nil {
		return x.SysFileTime
	}
	return ""
}

func (x *CaidFactors) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CaidFactors) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *CaidFactors) GetMntId() string {
	if x != nil {
		return x.MntId
	}
	return ""
}

func (x *CaidFactors) GetDeviceInitTime() string {
	if x != nil {
		return x.DeviceInitTime
	}
	return ""
}

type CaidWithFactors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Factors         *CaidFactors `protobuf:"bytes,1,opt,name=factors,proto3" json:"factors,omitempty"`
	Caid            string       `protobuf:"bytes,2,opt,name=caid,proto3" json:"caid,omitempty"`
	CaidLast        string       `protobuf:"bytes,5,opt,name=caidLast,proto3" json:"caidLast,omitempty"`
	CaidVersion     string       `protobuf:"bytes,3,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	CaidVersionLast string       `protobuf:"bytes,6,opt,name=caidVersionLast,proto3" json:"caidVersionLast,omitempty"`
	CaidGenTime     string       `protobuf:"bytes,4,opt,name=caidGenTime,proto3" json:"caidGenTime,omitempty"`
}

func (x *CaidWithFactors) Reset() {
	*x = CaidWithFactors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaidWithFactors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaidWithFactors) ProtoMessage() {}

func (x *CaidWithFactors) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaidWithFactors.ProtoReflect.Descriptor instead.
func (*CaidWithFactors) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{2}
}

func (x *CaidWithFactors) GetFactors() *CaidFactors {
	if x != nil {
		return x.Factors
	}
	return nil
}

func (x *CaidWithFactors) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *CaidWithFactors) GetCaidLast() string {
	if x != nil {
		return x.CaidLast
	}
	return ""
}

func (x *CaidWithFactors) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *CaidWithFactors) GetCaidVersionLast() string {
	if x != nil {
		return x.CaidVersionLast
	}
	return ""
}

func (x *CaidWithFactors) GetCaidGenTime() string {
	if x != nil {
		return x.CaidGenTime
	}
	return ""
}

type BidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Imp          []*BidRequest_Imp  `protobuf:"bytes,2,rep,name=imp,proto3" json:"imp,omitempty"`
	App          *BidRequest_App    `protobuf:"bytes,3,opt,name=app,proto3" json:"app,omitempty"`
	Device       *BidRequest_Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	User         *BidRequest_User   `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	ApiVersion   string             `protobuf:"bytes,6,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	InstalledApp []string           `protobuf:"bytes,7,rep,name=installedApp,proto3" json:"installedApp,omitempty"`
	Appendix01   []int64            `protobuf:"varint,8,rep,packed,name=appendix01,proto3" json:"appendix01,omitempty"`
	Ext          *BidRequest_Ext    `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidRequest) Reset() {
	*x = BidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest) ProtoMessage() {}

func (x *BidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest.ProtoReflect.Descriptor instead.
func (*BidRequest) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3}
}

func (x *BidRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest) GetImp() []*BidRequest_Imp {
	if x != nil {
		return x.Imp
	}
	return nil
}

func (x *BidRequest) GetApp() *BidRequest_App {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BidRequest) GetDevice() *BidRequest_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *BidRequest) GetUser() *BidRequest_User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BidRequest) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BidRequest) GetInstalledApp() []string {
	if x != nil {
		return x.InstalledApp
	}
	return nil
}

func (x *BidRequest) GetAppendix01() []int64 {
	if x != nil {
		return x.Appendix01
	}
	return nil
}

func (x *BidRequest) GetExt() *BidRequest_Ext {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string                   `protobuf:"bytes,65,opt,name=id,proto3" json:"id,omitempty"`
	Bidid   string                   `protobuf:"bytes,66,opt,name=bidid,proto3" json:"bidid,omitempty"`
	Seatbid []*BidResponse_SeatBid   `protobuf:"bytes,67,rep,name=seatbid,proto3" json:"seatbid,omitempty"`
	Ext     *BidResponse_ResponseExt `protobuf:"bytes,78,opt,name=ext,proto3" json:"ext,omitempty"`
}

func (x *BidResponse) Reset() {
	*x = BidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse) ProtoMessage() {}

func (x *BidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse.ProtoReflect.Descriptor instead.
func (*BidResponse) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4}
}

func (x *BidResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidResponse) GetBidid() string {
	if x != nil {
		return x.Bidid
	}
	return ""
}

func (x *BidResponse) GetSeatbid() []*BidResponse_SeatBid {
	if x != nil {
		return x.Seatbid
	}
	return nil
}

func (x *BidResponse) GetExt() *BidResponse_ResponseExt {
	if x != nil {
		return x.Ext
	}
	return nil
}

type BidRequest_Ext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkBidExt *BidRequest_SdkBidExt `protobuf:"bytes,1,opt,name=sdkBidExt,proto3" json:"sdkBidExt,omitempty"`
}

func (x *BidRequest_Ext) Reset() {
	*x = BidRequest_Ext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Ext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Ext) ProtoMessage() {}

func (x *BidRequest_Ext) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Ext.ProtoReflect.Descriptor instead.
func (*BidRequest_Ext) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 0}
}

func (x *BidRequest_Ext) GetSdkBidExt() *BidRequest_SdkBidExt {
	if x != nil {
		return x.SdkBidExt
	}
	return nil
}

type BidRequest_SdkBidExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	YlhBuyerId              string `protobuf:"bytes,1,opt,name=ylhBuyerId,proto3" json:"ylhBuyerId,omitempty"`                            // 优量汇SDK获取的buyerID
	YlhOpensdkVer           string `protobuf:"bytes,3,opt,name=ylhOpensdkVer,proto3" json:"ylhOpensdkVer,omitempty"`                      // 优量汇SDKopenSDK 版本，用于判断是否能支持微信小程序广告
	YlhSupportSplashZoomout bool   `protobuf:"varint,5,opt,name=ylhSupportSplashZoomout,proto3" json:"ylhSupportSplashZoomout,omitempty"` // 优量汇SDK是否支持开屏 V+
	YlhSdkInfo              string `protobuf:"bytes,6,opt,name=ylhSdkInfo,proto3" json:"ylhSdkInfo,omitempty"`                            // 优量汇SDK获取的SDKInfo，缺失时将影响广告推荐效果，强烈建议开发者全量上传
}

func (x *BidRequest_SdkBidExt) Reset() {
	*x = BidRequest_SdkBidExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_SdkBidExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_SdkBidExt) ProtoMessage() {}

func (x *BidRequest_SdkBidExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_SdkBidExt.ProtoReflect.Descriptor instead.
func (*BidRequest_SdkBidExt) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 1}
}

func (x *BidRequest_SdkBidExt) GetYlhBuyerId() string {
	if x != nil {
		return x.YlhBuyerId
	}
	return ""
}

func (x *BidRequest_SdkBidExt) GetYlhOpensdkVer() string {
	if x != nil {
		return x.YlhOpensdkVer
	}
	return ""
}

func (x *BidRequest_SdkBidExt) GetYlhSupportSplashZoomout() bool {
	if x != nil {
		return x.YlhSupportSplashZoomout
	}
	return false
}

func (x *BidRequest_SdkBidExt) GetYlhSdkInfo() string {
	if x != nil {
		return x.YlhSdkInfo
	}
	return ""
}

type BidRequest_Imp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string                 `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	TagId      string                 `protobuf:"bytes,8,opt,name=tagId,proto3" json:"tagId,omitempty"`
	AdslotSize *BidRequest_AdslotSize `protobuf:"bytes,9,opt,name=adslotSize,proto3" json:"adslotSize,omitempty"`
	Bidfloor   float64                `protobuf:"fixed64,10,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	Deeplink   bool                   `protobuf:"varint,11,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	Secure     int32                  `protobuf:"varint,12,opt,name=secure,proto3" json:"secure,omitempty"`
	Deals      []*BidRequest_Deal     `protobuf:"bytes,13,rep,name=deals,proto3" json:"deals,omitempty"`
}

func (x *BidRequest_Imp) Reset() {
	*x = BidRequest_Imp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Imp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Imp) ProtoMessage() {}

func (x *BidRequest_Imp) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Imp.ProtoReflect.Descriptor instead.
func (*BidRequest_Imp) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 2}
}

func (x *BidRequest_Imp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Imp) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *BidRequest_Imp) GetAdslotSize() *BidRequest_AdslotSize {
	if x != nil {
		return x.AdslotSize
	}
	return nil
}

func (x *BidRequest_Imp) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Imp) GetDeeplink() bool {
	if x != nil {
		return x.Deeplink
	}
	return false
}

func (x *BidRequest_Imp) GetSecure() int32 {
	if x != nil {
		return x.Secure
	}
	return 0
}

func (x *BidRequest_Imp) GetDeals() []*BidRequest_Deal {
	if x != nil {
		return x.Deals
	}
	return nil
}

type BidRequest_AdslotSize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width       int32    `protobuf:"varint,14,opt,name=width,proto3" json:"width,omitempty"`
	Height      int32    `protobuf:"varint,15,opt,name=height,proto3" json:"height,omitempty"`
	Mimes       []string `protobuf:"bytes,16,rep,name=mimes,proto3" json:"mimes,omitempty"`
	Size        int32    `protobuf:"varint,17,opt,name=size,proto3" json:"size,omitempty"`
	TitleLength int32    `protobuf:"varint,18,opt,name=titleLength,proto3" json:"titleLength,omitempty"`
	DescLength  int32    `protobuf:"varint,19,opt,name=descLength,proto3" json:"descLength,omitempty"`
	MinDuration int32    `protobuf:"varint,20,opt,name=minDuration,proto3" json:"minDuration,omitempty"`
	MaxDuration int32    `protobuf:"varint,21,opt,name=maxDuration,proto3" json:"maxDuration,omitempty"`
}

func (x *BidRequest_AdslotSize) Reset() {
	*x = BidRequest_AdslotSize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_AdslotSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_AdslotSize) ProtoMessage() {}

func (x *BidRequest_AdslotSize) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_AdslotSize.ProtoReflect.Descriptor instead.
func (*BidRequest_AdslotSize) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 3}
}

func (x *BidRequest_AdslotSize) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMimes() []string {
	if x != nil {
		return x.Mimes
	}
	return nil
}

func (x *BidRequest_AdslotSize) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetTitleLength() int32 {
	if x != nil {
		return x.TitleLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetDescLength() int32 {
	if x != nil {
		return x.DescLength
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMinDuration() int32 {
	if x != nil {
		return x.MinDuration
	}
	return 0
}

func (x *BidRequest_AdslotSize) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

type BidRequest_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,22,opt,name=appName,proto3" json:"appName,omitempty"`
	Bundle     string `protobuf:"bytes,23,opt,name=bundle,proto3" json:"bundle,omitempty"`
	AppVersion string `protobuf:"bytes,24,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
}

func (x *BidRequest_App) Reset() {
	*x = BidRequest_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_App) ProtoMessage() {}

func (x *BidRequest_App) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_App.ProtoReflect.Descriptor instead.
func (*BidRequest_App) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 4}
}

func (x *BidRequest_App) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidRequest_App) GetBundle() string {
	if x != nil {
		return x.Bundle
	}
	return ""
}

func (x *BidRequest_App) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type BidRequest_Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os              string          `protobuf:"bytes,25,opt,name=os,proto3" json:"os,omitempty"`
	Osv             string          `protobuf:"bytes,26,opt,name=osv,proto3" json:"osv,omitempty"`
	Imei            string          `protobuf:"bytes,27,opt,name=imei,proto3" json:"imei,omitempty"`
	ImeiMd5         string          `protobuf:"bytes,28,opt,name=imeiMd5,proto3" json:"imeiMd5,omitempty"`
	Oaid            string          `protobuf:"bytes,29,opt,name=oaid,proto3" json:"oaid,omitempty"`
	OaidMd5         string          `protobuf:"bytes,30,opt,name=oaidMd5,proto3" json:"oaidMd5,omitempty"`
	AndroidId       string          `protobuf:"bytes,31,opt,name=androidId,proto3" json:"androidId,omitempty"`
	Idfa            string          `protobuf:"bytes,32,opt,name=idfa,proto3" json:"idfa,omitempty"`
	IdfaMd5         string          `protobuf:"bytes,33,opt,name=idfaMd5,proto3" json:"idfaMd5,omitempty"`
	Mac             string          `protobuf:"bytes,34,opt,name=mac,proto3" json:"mac,omitempty"`
	MacMd5          string          `protobuf:"bytes,35,opt,name=macMd5,proto3" json:"macMd5,omitempty"`
	Ip              string          `protobuf:"bytes,36,opt,name=ip,proto3" json:"ip,omitempty"`
	IpV6            string          `protobuf:"bytes,37,opt,name=ipV6,proto3" json:"ipV6,omitempty"`
	Ua              string          `protobuf:"bytes,38,opt,name=ua,proto3" json:"ua,omitempty"`
	ConnectionType  int32           `protobuf:"varint,39,opt,name=connectionType,proto3" json:"connectionType,omitempty"`
	Brand           string          `protobuf:"bytes,40,opt,name=brand,proto3" json:"brand,omitempty"`
	Make            string          `protobuf:"bytes,41,opt,name=make,proto3" json:"make,omitempty"`
	Model           string          `protobuf:"bytes,42,opt,name=model,proto3" json:"model,omitempty"`
	Hwv             string          `protobuf:"bytes,43,opt,name=hwv,proto3" json:"hwv,omitempty"`
	Carrier         int32           `protobuf:"varint,44,opt,name=carrier,proto3" json:"carrier,omitempty"`
	MccMnc          string          `protobuf:"bytes,45,opt,name=mccMnc,proto3" json:"mccMnc,omitempty"`
	ScreenHeight    int32           `protobuf:"varint,46,opt,name=screenHeight,proto3" json:"screenHeight,omitempty"`
	ScreenWidth     int32           `protobuf:"varint,47,opt,name=screenWidth,proto3" json:"screenWidth,omitempty"`
	Ppi             int32           `protobuf:"varint,48,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Geo             *BidRequest_Geo `protobuf:"bytes,49,opt,name=geo,proto3" json:"geo,omitempty"`
	AppList         string          `protobuf:"bytes,50,opt,name=appList,proto3" json:"appList,omitempty"`
	BootMark        string          `protobuf:"bytes,51,opt,name=bootMark,proto3" json:"bootMark,omitempty"`
	UpdateMark      string          `protobuf:"bytes,52,opt,name=updateMark,proto3" json:"updateMark,omitempty"`
	UpdateMark1     string          `protobuf:"bytes,74,opt,name=updateMark1,proto3" json:"updateMark1,omitempty"` // 爱奇艺专用
	VerCodeOfHms    string          `protobuf:"bytes,53,opt,name=verCodeOfHms,proto3" json:"verCodeOfHms,omitempty"`
	VerCodeOfAG     string          `protobuf:"bytes,54,opt,name=verCodeOfAG,proto3" json:"verCodeOfAG,omitempty"`
	RomVersion      string          `protobuf:"bytes,55,opt,name=romVersion,proto3" json:"romVersion,omitempty"`
	Orientation     int32           `protobuf:"varint,56,opt,name=orientation,proto3" json:"orientation,omitempty"`
	BootTimeSec     string          `protobuf:"bytes,57,opt,name=bootTimeSec,proto3" json:"bootTimeSec,omitempty"`
	PhoneName       string          `protobuf:"bytes,58,opt,name=phoneName,proto3" json:"phoneName,omitempty"`
	MemorySize      int64           `protobuf:"varint,59,opt,name=memorySize,proto3" json:"memorySize,omitempty"`
	DiskSize        int64           `protobuf:"varint,60,opt,name=diskSize,proto3" json:"diskSize,omitempty"`
	OsUpdateTimeSec string          `protobuf:"bytes,61,opt,name=osUpdateTimeSec,proto3" json:"osUpdateTimeSec,omitempty"`
	ModelCode       string          `protobuf:"bytes,62,opt,name=modelCode,proto3" json:"modelCode,omitempty"`
	TimeZone        string          `protobuf:"bytes,63,opt,name=timeZone,proto3" json:"timeZone,omitempty"`
	// 废弃
	FileTime        string           `protobuf:"bytes,64,opt,name=fileTime,proto3" json:"fileTime,omitempty"`
	DeviceBirthTime string           `protobuf:"bytes,65,opt,name=deviceBirthTime,proto3" json:"deviceBirthTime,omitempty"`
	CaidVersion     string           `protobuf:"bytes,66,opt,name=caidVersion,proto3" json:"caidVersion,omitempty"`
	Caid            string           `protobuf:"bytes,67,opt,name=caid,proto3" json:"caid,omitempty"`
	Paid            string           `protobuf:"bytes,68,opt,name=paid,proto3" json:"paid,omitempty"`
	CaidWithFactors *CaidWithFactors `protobuf:"bytes,69,opt,name=caidWithFactors,proto3" json:"caidWithFactors,omitempty"`
	WxInstalled     bool             `protobuf:"varint,70,opt,name=wxInstalled,proto3" json:"wxInstalled,omitempty"`
}

func (x *BidRequest_Device) Reset() {
	*x = BidRequest_Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Device) ProtoMessage() {}

func (x *BidRequest_Device) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Device.ProtoReflect.Descriptor instead.
func (*BidRequest_Device) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 5}
}

func (x *BidRequest_Device) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *BidRequest_Device) GetOsv() string {
	if x != nil {
		return x.Osv
	}
	return ""
}

func (x *BidRequest_Device) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *BidRequest_Device) GetImeiMd5() string {
	if x != nil {
		return x.ImeiMd5
	}
	return ""
}

func (x *BidRequest_Device) GetOaid() string {
	if x != nil {
		return x.Oaid
	}
	return ""
}

func (x *BidRequest_Device) GetOaidMd5() string {
	if x != nil {
		return x.OaidMd5
	}
	return ""
}

func (x *BidRequest_Device) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *BidRequest_Device) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *BidRequest_Device) GetIdfaMd5() string {
	if x != nil {
		return x.IdfaMd5
	}
	return ""
}

func (x *BidRequest_Device) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BidRequest_Device) GetMacMd5() string {
	if x != nil {
		return x.MacMd5
	}
	return ""
}

func (x *BidRequest_Device) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *BidRequest_Device) GetIpV6() string {
	if x != nil {
		return x.IpV6
	}
	return ""
}

func (x *BidRequest_Device) GetUa() string {
	if x != nil {
		return x.Ua
	}
	return ""
}

func (x *BidRequest_Device) GetConnectionType() int32 {
	if x != nil {
		return x.ConnectionType
	}
	return 0
}

func (x *BidRequest_Device) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *BidRequest_Device) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *BidRequest_Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *BidRequest_Device) GetHwv() string {
	if x != nil {
		return x.Hwv
	}
	return ""
}

func (x *BidRequest_Device) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *BidRequest_Device) GetMccMnc() string {
	if x != nil {
		return x.MccMnc
	}
	return ""
}

func (x *BidRequest_Device) GetScreenHeight() int32 {
	if x != nil {
		return x.ScreenHeight
	}
	return 0
}

func (x *BidRequest_Device) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *BidRequest_Device) GetPpi() int32 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *BidRequest_Device) GetGeo() *BidRequest_Geo {
	if x != nil {
		return x.Geo
	}
	return nil
}

func (x *BidRequest_Device) GetAppList() string {
	if x != nil {
		return x.AppList
	}
	return ""
}

func (x *BidRequest_Device) GetBootMark() string {
	if x != nil {
		return x.BootMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark() string {
	if x != nil {
		return x.UpdateMark
	}
	return ""
}

func (x *BidRequest_Device) GetUpdateMark1() string {
	if x != nil {
		return x.UpdateMark1
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfHms() string {
	if x != nil {
		return x.VerCodeOfHms
	}
	return ""
}

func (x *BidRequest_Device) GetVerCodeOfAG() string {
	if x != nil {
		return x.VerCodeOfAG
	}
	return ""
}

func (x *BidRequest_Device) GetRomVersion() string {
	if x != nil {
		return x.RomVersion
	}
	return ""
}

func (x *BidRequest_Device) GetOrientation() int32 {
	if x != nil {
		return x.Orientation
	}
	return 0
}

func (x *BidRequest_Device) GetBootTimeSec() string {
	if x != nil {
		return x.BootTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetPhoneName() string {
	if x != nil {
		return x.PhoneName
	}
	return ""
}

func (x *BidRequest_Device) GetMemorySize() int64 {
	if x != nil {
		return x.MemorySize
	}
	return 0
}

func (x *BidRequest_Device) GetDiskSize() int64 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *BidRequest_Device) GetOsUpdateTimeSec() string {
	if x != nil {
		return x.OsUpdateTimeSec
	}
	return ""
}

func (x *BidRequest_Device) GetModelCode() string {
	if x != nil {
		return x.ModelCode
	}
	return ""
}

func (x *BidRequest_Device) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *BidRequest_Device) GetFileTime() string {
	if x != nil {
		return x.FileTime
	}
	return ""
}

func (x *BidRequest_Device) GetDeviceBirthTime() string {
	if x != nil {
		return x.DeviceBirthTime
	}
	return ""
}

func (x *BidRequest_Device) GetCaidVersion() string {
	if x != nil {
		return x.CaidVersion
	}
	return ""
}

func (x *BidRequest_Device) GetCaid() string {
	if x != nil {
		return x.Caid
	}
	return ""
}

func (x *BidRequest_Device) GetPaid() string {
	if x != nil {
		return x.Paid
	}
	return ""
}

func (x *BidRequest_Device) GetCaidWithFactors() *CaidWithFactors {
	if x != nil {
		return x.CaidWithFactors
	}
	return nil
}

func (x *BidRequest_Device) GetWxInstalled() bool {
	if x != nil {
		return x.WxInstalled
	}
	return false
}

type BidRequest_Deal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string  `protobuf:"bytes,57,opt,name=id,proto3" json:"id,omitempty"`
	Bidfloor float64 `protobuf:"fixed64,58,opt,name=bidfloor,proto3" json:"bidfloor,omitempty"`
	DealType int32   `protobuf:"varint,105,opt,name=deal_type,json=dealType,proto3" json:"deal_type,omitempty"`
	Pdfloor  float64 `protobuf:"fixed64,106,opt,name=pdfloor,proto3" json:"pdfloor,omitempty"`
}

func (x *BidRequest_Deal) Reset() {
	*x = BidRequest_Deal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Deal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Deal) ProtoMessage() {}

func (x *BidRequest_Deal) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Deal.ProtoReflect.Descriptor instead.
func (*BidRequest_Deal) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 6}
}

func (x *BidRequest_Deal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BidRequest_Deal) GetBidfloor() float64 {
	if x != nil {
		return x.Bidfloor
	}
	return 0
}

func (x *BidRequest_Deal) GetDealType() int32 {
	if x != nil {
		return x.DealType
	}
	return 0
}

func (x *BidRequest_Deal) GetPdfloor() float64 {
	if x != nil {
		return x.Pdfloor
	}
	return 0
}

type BidRequest_Geo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    int32   `protobuf:"varint,59,opt,name=type,proto3" json:"type,omitempty"`
	Lat     float64 `protobuf:"fixed64,60,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon     float64 `protobuf:"fixed64,61,opt,name=lon,proto3" json:"lon,omitempty"`
	Country string  `protobuf:"bytes,62,opt,name=country,proto3" json:"country,omitempty"`
}

func (x *BidRequest_Geo) Reset() {
	*x = BidRequest_Geo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_Geo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_Geo) ProtoMessage() {}

func (x *BidRequest_Geo) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_Geo.ProtoReflect.Descriptor instead.
func (*BidRequest_Geo) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 7}
}

func (x *BidRequest_Geo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BidRequest_Geo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *BidRequest_Geo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *BidRequest_Geo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

type BidRequest_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Age    int32 `protobuf:"varint,63,opt,name=age,proto3" json:"age,omitempty"`
	Gender int32 `protobuf:"varint,64,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *BidRequest_User) Reset() {
	*x = BidRequest_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidRequest_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidRequest_User) ProtoMessage() {}

func (x *BidRequest_User) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidRequest_User.ProtoReflect.Descriptor instead.
func (*BidRequest_User) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{3, 8}
}

func (x *BidRequest_User) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *BidRequest_User) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type BidResponse_SeatBid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid []*BidResponse_Bid `protobuf:"bytes,68,rep,name=bid,proto3" json:"bid,omitempty"`
}

func (x *BidResponse_SeatBid) Reset() {
	*x = BidResponse_SeatBid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SeatBid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SeatBid) ProtoMessage() {}

func (x *BidResponse_SeatBid) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SeatBid.ProtoReflect.Descriptor instead.
func (*BidResponse_SeatBid) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BidResponse_SeatBid) GetBid() []*BidResponse_Bid {
	if x != nil {
		return x.Bid
	}
	return nil
}

type BidResponse_Bid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Impid   string            `protobuf:"bytes,69,opt,name=impid,proto3" json:"impid,omitempty"`
	AdType  int32             `protobuf:"varint,70,opt,name=adType,proto3" json:"adType,omitempty"`
	AdStyle int32             `protobuf:"varint,71,opt,name=adStyle,proto3" json:"adStyle,omitempty"`
	Items   *BidResponse_Item `protobuf:"bytes,72,opt,name=items,proto3" json:"items,omitempty"`
	Price   float64           `protobuf:"fixed64,73,opt,name=price,proto3" json:"price,omitempty"`
	Nurl    string            `protobuf:"bytes,74,opt,name=nurl,proto3" json:"nurl,omitempty"`
	Cid     string            `protobuf:"bytes,107,opt,name=cid,proto3" json:"cid,omitempty"`
	Crid    string            `protobuf:"bytes,99,opt,name=crid,proto3" json:"crid,omitempty"`
	ApiName string            `protobuf:"bytes,104,opt,name=apiName,proto3" json:"apiName,omitempty"`
	Lurl    string            `protobuf:"bytes,77,opt,name=lurl,proto3" json:"lurl,omitempty"`
}

func (x *BidResponse_Bid) Reset() {
	*x = BidResponse_Bid{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Bid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Bid) ProtoMessage() {}

func (x *BidResponse_Bid) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Bid.ProtoReflect.Descriptor instead.
func (*BidResponse_Bid) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 1}
}

func (x *BidResponse_Bid) GetImpid() string {
	if x != nil {
		return x.Impid
	}
	return ""
}

func (x *BidResponse_Bid) GetAdType() int32 {
	if x != nil {
		return x.AdType
	}
	return 0
}

func (x *BidResponse_Bid) GetAdStyle() int32 {
	if x != nil {
		return x.AdStyle
	}
	return 0
}

func (x *BidResponse_Bid) GetItems() *BidResponse_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BidResponse_Bid) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *BidResponse_Bid) GetNurl() string {
	if x != nil {
		return x.Nurl
	}
	return ""
}

func (x *BidResponse_Bid) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *BidResponse_Bid) GetCrid() string {
	if x != nil {
		return x.Crid
	}
	return ""
}

func (x *BidResponse_Bid) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *BidResponse_Bid) GetLurl() string {
	if x != nil {
		return x.Lurl
	}
	return ""
}

type BidResponse_ResponseExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SdkExt *BidResponse_SdkResponseExt `protobuf:"bytes,1,opt,name=SdkExt,proto3" json:"SdkExt,omitempty"`
}

func (x *BidResponse_ResponseExt) Reset() {
	*x = BidResponse_ResponseExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_ResponseExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_ResponseExt) ProtoMessage() {}

func (x *BidResponse_ResponseExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_ResponseExt.ProtoReflect.Descriptor instead.
func (*BidResponse_ResponseExt) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 2}
}

func (x *BidResponse_ResponseExt) GetSdkExt() *BidResponse_SdkResponseExt {
	if x != nil {
		return x.SdkExt
	}
	return nil
}

type BidResponse_SdkResponseExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ylhtoken string `protobuf:"bytes,1,opt,name=ylhtoken,proto3" json:"ylhtoken,omitempty"`
}

func (x *BidResponse_SdkResponseExt) Reset() {
	*x = BidResponse_SdkResponseExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_SdkResponseExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_SdkResponseExt) ProtoMessage() {}

func (x *BidResponse_SdkResponseExt) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_SdkResponseExt.ProtoReflect.Descriptor instead.
func (*BidResponse_SdkResponseExt) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 3}
}

func (x *BidResponse_SdkResponseExt) GetYlhtoken() string {
	if x != nil {
		return x.Ylhtoken
	}
	return ""
}

type BidResponse_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                       string                       `protobuf:"bytes,75,opt,name=title,proto3" json:"title,omitempty"`
	Desc                        string                       `protobuf:"bytes,76,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                        string                       `protobuf:"bytes,77,opt,name=icon,proto3" json:"icon,omitempty"`
	Html                        string                       `protobuf:"bytes,78,opt,name=html,proto3" json:"html,omitempty"`
	MediaStyle                  int32                        `protobuf:"varint,79,opt,name=mediaStyle,proto3" json:"mediaStyle,omitempty"`
	DownloadUrl                 string                       `protobuf:"bytes,80,opt,name=downloadUrl,proto3" json:"downloadUrl,omitempty"`
	DownloadAppInfo             *BidResponse_DownloadAppInfo `protobuf:"bytes,81,opt,name=downloadAppInfo,proto3" json:"downloadAppInfo,omitempty"`
	ClickUrl                    string                       `protobuf:"bytes,82,opt,name=clickUrl,proto3" json:"clickUrl,omitempty"`
	DplUrl                      string                       `protobuf:"bytes,83,opt,name=dplUrl,proto3" json:"dplUrl,omitempty"`
	Imgs                        []string                     `protobuf:"bytes,84,rep,name=imgs,proto3" json:"imgs,omitempty"`
	ExposalUrls                 []string                     `protobuf:"bytes,85,rep,name=exposalUrls,proto3" json:"exposalUrls,omitempty"`
	ClickMonitorUrls            []string                     `protobuf:"bytes,86,rep,name=clickMonitorUrls,proto3" json:"clickMonitorUrls,omitempty"`
	Video                       *BidResponse_Video           `protobuf:"bytes,87,opt,name=video,proto3" json:"video,omitempty"`
	MiniProgramId               string                       `protobuf:"bytes,88,opt,name=miniProgramId,proto3" json:"miniProgramId,omitempty"`
	MiniProgramPath             string                       `protobuf:"bytes,89,opt,name=miniProgramPath,proto3" json:"miniProgramPath,omitempty"`
	MiniProgramType             int32                        `protobuf:"varint,90,opt,name=miniProgramType,proto3" json:"miniProgramType,omitempty"`
	MiniProgramExtData          string                       `protobuf:"bytes,99,opt,name=miniProgramExtData,proto3" json:"miniProgramExtData,omitempty"`
	MiniProgramSuccessTrackUrls []string                     `protobuf:"bytes,91,rep,name=miniProgramSuccessTrackUrls,proto3" json:"miniProgramSuccessTrackUrls,omitempty"`
	DownloadTrackUrls           []string                     `protobuf:"bytes,92,rep,name=downloadTrackUrls,proto3" json:"downloadTrackUrls,omitempty"`
	DownloadedTrackUrls         []string                     `protobuf:"bytes,93,rep,name=downloadedTrackUrls,proto3" json:"downloadedTrackUrls,omitempty"`
	InstalledTrackUrls          []string                     `protobuf:"bytes,94,rep,name=installedTrackUrls,proto3" json:"installedTrackUrls,omitempty"`
	DpSuccessTrackUrls          []string                     `protobuf:"bytes,95,rep,name=dpSuccessTrackUrls,proto3" json:"dpSuccessTrackUrls,omitempty"`
	ActionTrackUrls             []string                     `protobuf:"bytes,96,rep,name=actionTrackUrls,proto3" json:"actionTrackUrls,omitempty"`
	PackageName                 string                       `protobuf:"bytes,97,opt,name=packageName,proto3" json:"packageName,omitempty"`
	MonitorUrls                 []*BidResponse_MonitorUrl    `protobuf:"bytes,98,rep,name=monitorUrls,proto3" json:"monitorUrls,omitempty"`
}

func (x *BidResponse_Item) Reset() {
	*x = BidResponse_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Item) ProtoMessage() {}

func (x *BidResponse_Item) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Item.ProtoReflect.Descriptor instead.
func (*BidResponse_Item) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 4}
}

func (x *BidResponse_Item) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BidResponse_Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_Item) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *BidResponse_Item) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

func (x *BidResponse_Item) GetMediaStyle() int32 {
	if x != nil {
		return x.MediaStyle
	}
	return 0
}

func (x *BidResponse_Item) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDownloadAppInfo() *BidResponse_DownloadAppInfo {
	if x != nil {
		return x.DownloadAppInfo
	}
	return nil
}

func (x *BidResponse_Item) GetClickUrl() string {
	if x != nil {
		return x.ClickUrl
	}
	return ""
}

func (x *BidResponse_Item) GetDplUrl() string {
	if x != nil {
		return x.DplUrl
	}
	return ""
}

func (x *BidResponse_Item) GetImgs() []string {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *BidResponse_Item) GetExposalUrls() []string {
	if x != nil {
		return x.ExposalUrls
	}
	return nil
}

func (x *BidResponse_Item) GetClickMonitorUrls() []string {
	if x != nil {
		return x.ClickMonitorUrls
	}
	return nil
}

func (x *BidResponse_Item) GetVideo() *BidResponse_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *BidResponse_Item) GetMiniProgramId() string {
	if x != nil {
		return x.MiniProgramId
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramPath() string {
	if x != nil {
		return x.MiniProgramPath
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramType() int32 {
	if x != nil {
		return x.MiniProgramType
	}
	return 0
}

func (x *BidResponse_Item) GetMiniProgramExtData() string {
	if x != nil {
		return x.MiniProgramExtData
	}
	return ""
}

func (x *BidResponse_Item) GetMiniProgramSuccessTrackUrls() []string {
	if x != nil {
		return x.MiniProgramSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadTrackUrls() []string {
	if x != nil {
		return x.DownloadTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDownloadedTrackUrls() []string {
	if x != nil {
		return x.DownloadedTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetInstalledTrackUrls() []string {
	if x != nil {
		return x.InstalledTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetDpSuccessTrackUrls() []string {
	if x != nil {
		return x.DpSuccessTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetActionTrackUrls() []string {
	if x != nil {
		return x.ActionTrackUrls
	}
	return nil
}

func (x *BidResponse_Item) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *BidResponse_Item) GetMonitorUrls() []*BidResponse_MonitorUrl {
	if x != nil {
		return x.MonitorUrls
	}
	return nil
}

type BidResponse_MonitorUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventType string   `protobuf:"bytes,1,opt,name=eventType,proto3" json:"eventType,omitempty"`
	Urls      []string `protobuf:"bytes,2,rep,name=urls,proto3" json:"urls,omitempty"`
}

func (x *BidResponse_MonitorUrl) Reset() {
	*x = BidResponse_MonitorUrl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_MonitorUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_MonitorUrl) ProtoMessage() {}

func (x *BidResponse_MonitorUrl) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_MonitorUrl.ProtoReflect.Descriptor instead.
func (*BidResponse_MonitorUrl) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 5}
}

func (x *BidResponse_MonitorUrl) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *BidResponse_MonitorUrl) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type BidResponse_DownloadAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName    string `protobuf:"bytes,88,opt,name=appName,proto3" json:"appName,omitempty"`
	Developer  string `protobuf:"bytes,89,opt,name=developer,proto3" json:"developer,omitempty"`
	Version    string `protobuf:"bytes,90,opt,name=version,proto3" json:"version,omitempty"`
	PacketSize string `protobuf:"bytes,91,opt,name=packetSize,proto3" json:"packetSize,omitempty"`
	Privacy    string `protobuf:"bytes,92,opt,name=privacy,proto3" json:"privacy,omitempty"`
	Permission string `protobuf:"bytes,93,opt,name=permission,proto3" json:"permission,omitempty"`
	Desc       string `protobuf:"bytes,101,opt,name=desc,proto3" json:"desc,omitempty"`
	DescURL    string `protobuf:"bytes,102,opt,name=descURL,proto3" json:"descURL,omitempty"`
}

func (x *BidResponse_DownloadAppInfo) Reset() {
	*x = BidResponse_DownloadAppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_DownloadAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_DownloadAppInfo) ProtoMessage() {}

func (x *BidResponse_DownloadAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_DownloadAppInfo.ProtoReflect.Descriptor instead.
func (*BidResponse_DownloadAppInfo) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 6}
}

func (x *BidResponse_DownloadAppInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPacketSize() string {
	if x != nil {
		return x.PacketSize
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPrivacy() string {
	if x != nil {
		return x.Privacy
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BidResponse_DownloadAppInfo) GetDescURL() string {
	if x != nil {
		return x.DescURL
	}
	return ""
}

type BidResponse_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VideoUrl       string `protobuf:"bytes,94,opt,name=videoUrl,proto3" json:"videoUrl,omitempty"`
	VideoDuration  int32  `protobuf:"varint,95,opt,name=videoDuration,proto3" json:"videoDuration,omitempty"`
	VideoStartUrl  string `protobuf:"bytes,96,opt,name=videoStartUrl,proto3" json:"videoStartUrl,omitempty"`
	VideoFinishUrl string `protobuf:"bytes,97,opt,name=videoFinishUrl,proto3" json:"videoFinishUrl,omitempty"`
	VideoVastXml   string `protobuf:"bytes,98,opt,name=videoVastXml,proto3" json:"videoVastXml,omitempty"`
	VideoEndImgurl string `protobuf:"bytes,99,opt,name=videoEndImgurl,proto3" json:"videoEndImgurl,omitempty"`
	VideoPreImgurl string `protobuf:"bytes,100,opt,name=videoPreImgurl,proto3" json:"videoPreImgurl,omitempty"`
}

func (x *BidResponse_Video) Reset() {
	*x = BidResponse_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_adx4dsp_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BidResponse_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidResponse_Video) ProtoMessage() {}

func (x *BidResponse_Video) ProtoReflect() protoreflect.Message {
	mi := &file_adx4dsp_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidResponse_Video.ProtoReflect.Descriptor instead.
func (*BidResponse_Video) Descriptor() ([]byte, []int) {
	return file_adx4dsp_proto_rawDescGZIP(), []int{4, 7}
}

func (x *BidResponse_Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoDuration() int32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *BidResponse_Video) GetVideoStartUrl() string {
	if x != nil {
		return x.VideoStartUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoFinishUrl() string {
	if x != nil {
		return x.VideoFinishUrl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoVastXml() string {
	if x != nil {
		return x.VideoVastXml
	}
	return ""
}

func (x *BidResponse_Video) GetVideoEndImgurl() string {
	if x != nil {
		return x.VideoEndImgurl
	}
	return ""
}

func (x *BidResponse_Video) GetVideoPreImgurl() string {
	if x != nil {
		return x.VideoPreImgurl
	}
	return ""
}

var File_adx4dsp_proto protoreflect.FileDescriptor

var file_adx4dsp_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x22, 0x58, 0x0a, 0x0e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x34, 0x64, 0x73, 0x70, 0x2e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0xb1, 0x03, 0x0a, 0x0b, 0x43, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x49,
	0x6e, 0x53, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x6f, 0x6f, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x69, 0x64,
	0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67,
	0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x52, 0x07, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x61, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x69, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28,
	0x0a, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x73,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64,
	0x47, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x61, 0x69, 0x64, 0x47, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xe1, 0x16, 0x0a, 0x0a, 0x42,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x03, 0x69, 0x6d, 0x70,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x52, 0x03, 0x69, 0x6d, 0x70, 0x12, 0x3a, 0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70,
	0x70, 0x12, 0x43, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e,
	0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x65, 0x6e, 0x64, 0x69, 0x78, 0x30, 0x31, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78, 0x30, 0x31, 0x12, 0x3a, 0x0a, 0x03, 0x65, 0x78, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74,
	0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0x53, 0x0a, 0x03, 0x45, 0x78, 0x74, 0x12, 0x4c, 0x0a, 0x09,
	0x73, 0x64, 0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x64, 0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x52,
	0x09, 0x73, 0x64, 0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x1a, 0xab, 0x01, 0x0a, 0x09, 0x53,
	0x64, 0x6b, 0x42, 0x69, 0x64, 0x45, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x79, 0x6c, 0x68, 0x42,
	0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x6c,
	0x68, 0x42, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x79, 0x6c, 0x68, 0x4f,
	0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x79, 0x6c, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x73, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x17, 0x79, 0x6c, 0x68, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x6c, 0x61,
	0x73, 0x68, 0x5a, 0x6f, 0x6f, 0x6d, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x17, 0x79, 0x6c, 0x68, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x6c, 0x61, 0x73,
	0x68, 0x5a, 0x6f, 0x6f, 0x6d, 0x6f, 0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x79, 0x6c, 0x68, 0x53,
	0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x6c,
	0x68, 0x53, 0x64, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x8d, 0x02, 0x0a, 0x03, 0x49, 0x6d, 0x70,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x61, 0x64, 0x73, 0x6c, 0x6f, 0x74,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x64, 0x73, 0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x73,
	0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73,
	0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x65, 0x61,
	0x6c, 0x52, 0x05, 0x64, 0x65, 0x61, 0x6c, 0x73, 0x1a, 0xea, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x73,
	0x6c, 0x6f, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x10,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x57, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xd7,
	0x0a, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x73, 0x76,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x73, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x6d, 0x65, 0x69, 0x4d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x61, 0x69,
	0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x61, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x61, 0x69, 0x64, 0x4d, 0x64, 0x35, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x49, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72,
	0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x64, 0x66,
	0x61, 0x4d, 0x64, 0x35, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x66, 0x61,
	0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x61, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x70, 0x56, 0x36, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x56,
	0x36, 0x12, 0x0e, 0x0a, 0x02, 0x75, 0x61, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75,
	0x61, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x68, 0x77, 0x76,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x68, 0x77, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x63, 0x63, 0x4d, 0x6e, 0x63, 0x18,
	0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x63, 0x63, 0x4d, 0x6e, 0x63, 0x12, 0x22, 0x0a,
	0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x30, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x3a, 0x0a, 0x03, 0x67, 0x65, 0x6f, 0x18, 0x31, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x65, 0x6f, 0x52, 0x03, 0x67, 0x65,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6f, 0x6f, 0x74, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x72, 0x6b, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x72, 0x6b, 0x31, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x31, 0x12, 0x22, 0x0a, 0x0c, 0x76, 0x65, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x48, 0x6d, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x41, 0x47, 0x18, 0x36, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x66, 0x41, 0x47, 0x12,
	0x1e, 0x0a, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x37, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x20, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x38,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63,
	0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x3b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x3c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63,
	0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x40, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69, 0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x69,
	0x72, 0x74, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x61, 0x69, 0x64, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61,
	0x69, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x61, 0x69,
	0x64, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x61, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x69, 0x64, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x69,
	0x64, 0x12, 0x53, 0x0a, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x18, 0x45, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x43, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x0f, 0x63, 0x61, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x78, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x46, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x77, 0x78, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x1a, 0x69, 0x0a, 0x04, 0x44, 0x65, 0x61, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x3a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x62, 0x69, 0x64, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x69, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x64, 0x66,
	0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x64, 0x66, 0x6c,
	0x6f, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x03, 0x47, 0x65, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c, 0x61, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6c,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x3e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x1a, 0x30, 0x0a, 0x04,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x83,
	0x12, 0x0a, 0x0b, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x62, 0x69, 0x64, 0x69, 0x64, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62,
	0x69, 0x64, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x18,
	0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34, 0x64, 0x73, 0x70,
	0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x74, 0x42, 0x69, 0x64, 0x52, 0x07, 0x73, 0x65, 0x61, 0x74, 0x62, 0x69, 0x64, 0x12, 0x43, 0x0a,
	0x03, 0x65, 0x78, 0x74, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x78, 0x74, 0x52, 0x03, 0x65,
	0x78, 0x74, 0x1a, 0x46, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x74, 0x42, 0x69, 0x64, 0x12, 0x3b, 0x0a,
	0x03, 0x62, 0x69, 0x64, 0x18, 0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x2e,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64,
	0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x03, 0x62, 0x69, 0x64, 0x1a, 0x8d, 0x02, 0x0a, 0x03, 0x42,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x18, 0x45, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x46, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x47, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x61, 0x64, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x48, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x49, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x6b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x69, 0x64,
	0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x68, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x18, 0x4d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x75, 0x72, 0x6c, 0x1a, 0x5b, 0x0a, 0x0b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x78, 0x74, 0x12, 0x4c, 0x0a, 0x06, 0x53, 0x64, 0x6b,
	0x45, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x64, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x78, 0x74, 0x52,
	0x06, 0x53, 0x64, 0x6b, 0x45, 0x78, 0x74, 0x1a, 0x2c, 0x0a, 0x0e, 0x53, 0x64, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x45, 0x78, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x79, 0x6c, 0x68,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x79, 0x6c, 0x68,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xa0, 0x08, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x4c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x74, 0x6d, 0x6c, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x74, 0x6d, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x4f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x79, 0x6c, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18,
	0x50, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55,
	0x72, 0x6c, 0x12, 0x5f, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f,
	0x2e, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x64, 0x78, 0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18,
	0x52, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x64, 0x70, 0x6c, 0x55, 0x72, 0x6c, 0x18, 0x53, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x70, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x18,
	0x54, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x67, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x65,
	0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x55, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a,
	0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c,
	0x73, 0x18, 0x56, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x18, 0x57, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x2e, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78, 0x34,
	0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x24, 0x0a, 0x0d,
	0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x58, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x50, 0x61, 0x74, 0x68, 0x18, 0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x0f,
	0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x5a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x45, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x45,
	0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x1b, 0x6d, 0x69, 0x6e, 0x69, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1b, 0x6d, 0x69, 0x6e,
	0x69, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x11, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x13, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5e,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x64, 0x70, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x5f,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x64, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x60, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55,
	0x72, 0x6c, 0x73, 0x18, 0x62, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x2e, 0x6d,
	0x69, 0x63, 0x72, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x64, 0x78,
	0x34, 0x64, 0x73, 0x70, 0x2e, 0x42, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x52, 0x0b, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x1a, 0x3e, 0x0a, 0x0a, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x1a, 0xeb, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x58, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x18, 0x59, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x5a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x5b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x64, 0x65, 0x73, 0x63, 0x55, 0x52, 0x4c, 0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x65, 0x73, 0x63, 0x55, 0x52, 0x4c, 0x1a, 0x8b, 0x02, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x5e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x24, 0x0a, 0x0d,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x5f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x55, 0x72, 0x6c, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x55, 0x72, 0x6c, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x61, 0x73, 0x74, 0x58, 0x6d, 0x6c,
	0x18, 0x62, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x56, 0x61, 0x73,
	0x74, 0x58, 0x6d, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64,
	0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x18, 0x63, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x45, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x75, 0x72, 0x6c, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x72, 0x65, 0x49, 0x6d,
	0x67, 0x75, 0x72, 0x6c, 0x2a, 0x10, 0x0a, 0x06, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x42, 0x1d, 0x5a, 0x1b, 0x2e, 0x2f, 0x3b, 0x67, 0x6f, 0x5f,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x64,
	0x78, 0x34, 0x64, 0x73, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_adx4dsp_proto_rawDescOnce sync.Once
	file_adx4dsp_proto_rawDescData = file_adx4dsp_proto_rawDesc
)

func file_adx4dsp_proto_rawDescGZIP() []byte {
	file_adx4dsp_proto_rawDescOnce.Do(func() {
		file_adx4dsp_proto_rawDescData = protoimpl.X.CompressGZIP(file_adx4dsp_proto_rawDescData)
	})
	return file_adx4dsp_proto_rawDescData
}

var file_adx4dsp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_adx4dsp_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_adx4dsp_proto_goTypes = []interface{}{
	(STATUS)(0),                         // 0: go.micro.service.adx4dsp.STATUS
	(*ResponseStatus)(nil),              // 1: go.micro.service.adx4dsp.ResponseStatus
	(*CaidFactors)(nil),                 // 2: go.micro.service.adx4dsp.CaidFactors
	(*CaidWithFactors)(nil),             // 3: go.micro.service.adx4dsp.CaidWithFactors
	(*BidRequest)(nil),                  // 4: go.micro.service.adx4dsp.BidRequest
	(*BidResponse)(nil),                 // 5: go.micro.service.adx4dsp.BidResponse
	(*BidRequest_Ext)(nil),              // 6: go.micro.service.adx4dsp.BidRequest.Ext
	(*BidRequest_SdkBidExt)(nil),        // 7: go.micro.service.adx4dsp.BidRequest.SdkBidExt
	(*BidRequest_Imp)(nil),              // 8: go.micro.service.adx4dsp.BidRequest.Imp
	(*BidRequest_AdslotSize)(nil),       // 9: go.micro.service.adx4dsp.BidRequest.AdslotSize
	(*BidRequest_App)(nil),              // 10: go.micro.service.adx4dsp.BidRequest.App
	(*BidRequest_Device)(nil),           // 11: go.micro.service.adx4dsp.BidRequest.Device
	(*BidRequest_Deal)(nil),             // 12: go.micro.service.adx4dsp.BidRequest.Deal
	(*BidRequest_Geo)(nil),              // 13: go.micro.service.adx4dsp.BidRequest.Geo
	(*BidRequest_User)(nil),             // 14: go.micro.service.adx4dsp.BidRequest.User
	(*BidResponse_SeatBid)(nil),         // 15: go.micro.service.adx4dsp.BidResponse.SeatBid
	(*BidResponse_Bid)(nil),             // 16: go.micro.service.adx4dsp.BidResponse.Bid
	(*BidResponse_ResponseExt)(nil),     // 17: go.micro.service.adx4dsp.BidResponse.ResponseExt
	(*BidResponse_SdkResponseExt)(nil),  // 18: go.micro.service.adx4dsp.BidResponse.SdkResponseExt
	(*BidResponse_Item)(nil),            // 19: go.micro.service.adx4dsp.BidResponse.Item
	(*BidResponse_MonitorUrl)(nil),      // 20: go.micro.service.adx4dsp.BidResponse.MonitorUrl
	(*BidResponse_DownloadAppInfo)(nil), // 21: go.micro.service.adx4dsp.BidResponse.DownloadAppInfo
	(*BidResponse_Video)(nil),           // 22: go.micro.service.adx4dsp.BidResponse.Video
}
var file_adx4dsp_proto_depIdxs = []int32{
	0,  // 0: go.micro.service.adx4dsp.ResponseStatus.code:type_name -> go.micro.service.adx4dsp.STATUS
	2,  // 1: go.micro.service.adx4dsp.CaidWithFactors.factors:type_name -> go.micro.service.adx4dsp.CaidFactors
	8,  // 2: go.micro.service.adx4dsp.BidRequest.imp:type_name -> go.micro.service.adx4dsp.BidRequest.Imp
	10, // 3: go.micro.service.adx4dsp.BidRequest.app:type_name -> go.micro.service.adx4dsp.BidRequest.App
	11, // 4: go.micro.service.adx4dsp.BidRequest.device:type_name -> go.micro.service.adx4dsp.BidRequest.Device
	14, // 5: go.micro.service.adx4dsp.BidRequest.user:type_name -> go.micro.service.adx4dsp.BidRequest.User
	6,  // 6: go.micro.service.adx4dsp.BidRequest.ext:type_name -> go.micro.service.adx4dsp.BidRequest.Ext
	15, // 7: go.micro.service.adx4dsp.BidResponse.seatbid:type_name -> go.micro.service.adx4dsp.BidResponse.SeatBid
	17, // 8: go.micro.service.adx4dsp.BidResponse.ext:type_name -> go.micro.service.adx4dsp.BidResponse.ResponseExt
	7,  // 9: go.micro.service.adx4dsp.BidRequest.Ext.sdkBidExt:type_name -> go.micro.service.adx4dsp.BidRequest.SdkBidExt
	9,  // 10: go.micro.service.adx4dsp.BidRequest.Imp.adslotSize:type_name -> go.micro.service.adx4dsp.BidRequest.AdslotSize
	12, // 11: go.micro.service.adx4dsp.BidRequest.Imp.deals:type_name -> go.micro.service.adx4dsp.BidRequest.Deal
	13, // 12: go.micro.service.adx4dsp.BidRequest.Device.geo:type_name -> go.micro.service.adx4dsp.BidRequest.Geo
	3,  // 13: go.micro.service.adx4dsp.BidRequest.Device.caidWithFactors:type_name -> go.micro.service.adx4dsp.CaidWithFactors
	16, // 14: go.micro.service.adx4dsp.BidResponse.SeatBid.bid:type_name -> go.micro.service.adx4dsp.BidResponse.Bid
	19, // 15: go.micro.service.adx4dsp.BidResponse.Bid.items:type_name -> go.micro.service.adx4dsp.BidResponse.Item
	18, // 16: go.micro.service.adx4dsp.BidResponse.ResponseExt.SdkExt:type_name -> go.micro.service.adx4dsp.BidResponse.SdkResponseExt
	21, // 17: go.micro.service.adx4dsp.BidResponse.Item.downloadAppInfo:type_name -> go.micro.service.adx4dsp.BidResponse.DownloadAppInfo
	22, // 18: go.micro.service.adx4dsp.BidResponse.Item.video:type_name -> go.micro.service.adx4dsp.BidResponse.Video
	20, // 19: go.micro.service.adx4dsp.BidResponse.Item.monitorUrls:type_name -> go.micro.service.adx4dsp.BidResponse.MonitorUrl
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_adx4dsp_proto_init() }
func file_adx4dsp_proto_init() {
	if File_adx4dsp_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_adx4dsp_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResponseStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaidFactors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaidWithFactors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Ext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_SdkBidExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Imp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_AdslotSize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Deal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_Geo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidRequest_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SeatBid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Bid); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_ResponseExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_SdkResponseExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_MonitorUrl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_DownloadAppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_adx4dsp_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BidResponse_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_adx4dsp_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_adx4dsp_proto_goTypes,
		DependencyIndexes: file_adx4dsp_proto_depIdxs,
		EnumInfos:         file_adx4dsp_proto_enumTypes,
		MessageInfos:      file_adx4dsp_proto_msgTypes,
	}.Build()
	File_adx4dsp_proto = out.File
	file_adx4dsp_proto_rawDesc = nil
	file_adx4dsp_proto_goTypes = nil
	file_adx4dsp_proto_depIdxs = nil
}
